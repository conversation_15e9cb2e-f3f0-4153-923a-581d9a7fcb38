const express = require('express');
const cachegoose = require('cachegoose');
// var cors = require('cors')

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.tempData = {};
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

const multer = require('multer');
const upload = multer({
  dest: 'public/uploads',
});
// Caching

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyToken = require('./lib/middleware/verifyToken');
const verifyPermission = require('./lib/middleware/verifyPermission');

// const PetitionStatJob = require('./lib/job/petitionStatJob');

// Handle routes
// const OrderTypeHandle = require("./lib/routes/orderType")
const UtilHandle = require('./lib/routes/util');
const ServicesHandle = require('./lib/routes/services');
const MemberHandle = require('./lib/routes/member');
const UserHandle = require('./lib/routes/user');
const UserAdminHandle = require('./lib/routes/admin/user');
const PermissionAdminHandle = require('./lib/routes/admin/permission');
const GroupPermissionAdminHandle = require('./lib/routes/admin/groupPermission');
const CategoryPermissionAdminHandle = require('./lib/routes/admin/categoryPermission');
const RoleAdminHandle = require('./lib/routes/admin/role');
const UnitAdminHandle = require('./lib/routes/admin/unit');
const PetitionAdminHandle = require('./lib/routes/admin/petition');
const CategoryAdminHandle = require('./lib/routes/admin/category');
const PositionAdminHandle = require('./lib/routes/admin/position');
const ServiceAdminHandle = require('./lib/routes/admin/services');
const ChatbotHandle = require('./lib/routes/chatbot');
const ChatbotJsonHandle = require('./lib/routes/chatbotJson');
const TeachingCenterHandle = require('./lib/routes/education/teachingCenter');
const SchoolHandle = require('./lib/routes/education/school');
const LawEnforcementAgencyHandle = require('./lib/routes/administrativeAgency/lawEnforcementAgency');
const LegislatureHandle = require('./lib/routes/administrativeAgency/legislature');
const BilliardsHandle = require('./lib/routes/entertainment/billiards');
const KaraokeHandle = require('./lib/routes/entertainment/karaoke');
const MassageHandle = require('./lib/routes/entertainment/massage');
const SportsFieldHandle = require('./lib/routes/entertainment/sportsField');
const HospitalHandle = require('./lib/routes/medical/hospital');
const MaternityClinicHandle = require('./lib/routes/medical/maternityClinic');
const PharmacyHandle = require('./lib/routes/medical/pharmacy');
const FestivalHandle = require('./lib/routes/travel/festival');
const HotelHandle = require('./lib/routes/travel/hotel');
const RestaurantHandle = require('./lib/routes/travel/restaurant');
const TouristAttractionHandle = require('./lib/routes/travel/touristAttraction');
const TourListHandle = require('./lib/routes/travel/tourList');
const PetitionHandle = require('./lib/routes/petition');
const ProxyHandle = require('./lib/routes/proxy');
const NotifyHandle = require('./lib/routes/notify');
const AdminStatisticHandle = require('./lib/routes/admin/statistic');
const FeedbackHandle = require('./lib/routes/feedback');
const BannerHandle = require('./lib/routes/admin/banner');
const InstructionAdminHandle = require('./lib/routes/admin/instructions');
const InstructionHandle = require('./lib/routes/instructions');
const MemberAdminHandle = require('./lib/routes/admin/member');
const NotifyAdminHandle = require('./lib/routes/admin/notify');
const UserNotifyAdminHandle = require('./lib/routes/admin/userNotify');
const SystemLogAdminHandle = require('./lib/routes/admin/systemLog');
const VersionHandle = require('./lib/routes/proxy/version');
const OcopProductHandle = require('./lib/routes/agriculture/ocop');
const CooperativetHandle = require('./lib/routes/agriculture/cooperative');
const TraditionalVillageHandle = require('./lib/routes/agriculture/traditionalVillage');
const EmailHandle = require('./lib/routes/chatbotJson/email');
const ReportHandle = require('./lib/routes/chatbotJson/report');
const SpeechHandle = require('./lib/routes/chatbotJson/speech');
const PlanningHandle = require('./lib/routes/chatbotJson/planning');
const HistoricalSiteHandle = require('./lib/routes/travel/historicalSite');
const CulturalHeritageHandle = require('./lib/routes/travel/culturalHeritage');
const NewspaperAnalysisHandle = require('./lib/routes/chatbotJson/newspaperAnalysis');
const MakeDecisionHandle = require('./lib/routes/chatbotJson/makeDecision');
const DocumentSummaryHandle = require('./lib/routes/chatbotJson/documentSummary');
const MeetingSummaryHandle = require('./lib/routes/chatbotJson/meetingSummary');
const MeetingSynthesisHandle = require('./lib/routes/chatbotJson/meetingSynthesis');
const SummaryManageDocumentHandle = require('./lib/routes/chatbotJson/summaryManageDocument');
const SpeechManageDocumentHandle = require('./lib/routes/chatbotJson/speechManageDocument');
const WriteReportManageDocumentHandle = require('./lib/routes/chatbotJson/writeReportManageDocument');
const VideoSummaryHandle = require('./lib/routes/chatbotJson/videoSummary');
const VideoExcerptHandle = require('./lib/routes/chatbotJson/videoExcerpt');
const EvaluationHandle = require('./lib/routes/chatbotJson/evaluation');
const SpellCheckHandle = require('./lib/routes/chatbotJson/spellCheck');
const ITTHandle = require('./lib/routes/chatbotJson/imageToText');
const Educationhandle = require('./lib/routes/edu/education');
const EducationCenterhandle = require('./lib/routes/edu/educationCenter');
const EducationJobhandle = require('./lib/routes/edu/educationJob');
const TouristCompanyHandle = require('./lib/routes/travel/touristCompany');
const SocioEconomicHandle = require('./lib/routes/chatbotJson/socio-economic');
const VaccineCenterHanlde = require('./lib/routes/medical/vaccineCenter');
const DrugStoreHandle = require('./lib/routes/medical/drugStore');
const PressAgencyHandle = require('./lib/routes/press/pressAgency');
const TeleServiceHandle = require('./lib/routes/press/teleService');
const LawyerOrganizationHandle = require('./lib/routes/law/lawyerOrganization');
const NotaryOfficeHandle = require('./lib/routes/law/notaryOffice');
const DichvucongHandle = require('./lib/routes/dichVuCong');
const ReadNewspaperHandle = require('./lib/routes/chatbotJson/readNewspaper');
const WorkScheduleHandle = require('./lib/routes/chatbotJson/workSchedule');
const WriteReportHandle = require('./lib/routes/chatbotJson/writeReport');
const WriteArticleHandle = require('./lib/routes/chatbotJson/writeArticle');
const ChatWithDocumentHandle = require('./lib/routes/chatbotJson/chatWithDocument');
const WriteSpeechWithDocumentHandle = require('./lib/routes/chatbotJson/writeSpeechWithDocument');
const WriteReportWithDocumentHandle = require('./lib/routes/chatbotJson/writeReportWithDocument');
const CategoryNotificationAdminHandle = require('./lib/routes/admin/categoryNotification');
const ChatbotAdminHandle = require('./lib/routes/admin/chatbot');
const ChatManageDocumentHandle = require('./lib/routes/chatbotJson/chatManageDocument');
const TextToSpeechHandle = require('./lib/routes/chatbotJson/textToSpeech');
const WriteScriptHandle = require('./lib/routes/chatbotJson/writeScript');
const TestimonyHandle = require('./lib/routes/chatbotJson/testimony');

const TrafficRegistrationCenterHandle = require('./lib/routes/traffic/registrationCenter');
const TrafficBusStationHandle = require('./lib/routes/traffic/busStation');
const EmergencyContactHandle = require('./lib/routes/emergency/contact');
const EntertainmentChildrenPlayAreaHandle = require('./lib/routes/entertainment/childrenPlayArea');
const EntertainmentCinemaHandle = require('./lib/routes/entertainment/cinema');
const EntertainmentCoffeeHandle = require('./lib/routes/entertainment/coffee');
const EntertainmentEcozoneHandle = require('./lib/routes/entertainment/ecozone');
const EntertainmentShoppingMallHandle = require('./lib/routes/entertainment/shoppingMall');
const GovernmentHPHandle = require('./lib/routes/government/governmentHP');
const QLVBHandle = require('./lib/routes/qlvb');
const AppHandle = require('./lib/routes/app');
const AgentHandle = require('./lib/routes/agent');

const request = require('request');

// Start server, socket
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);
global.io = require('socket.io')(server);

app.use(bodyParser.json({ limit: '200mb' }));
app.use(express.static('public'));
app.use((req, res, next) => {
  let lat = _.get(req, 'body.location.lat', 0);
  let lng = _.get(req, 'body.location.lng', 0);
  if (typeof lat === 'string') {
    _.set(req, 'body.location.lat', Number(lat.replace(',', '.')));
  }
  if (typeof lng === 'string') {
    _.set(req, 'body.location.lng', Number(lng.replace(',', '.')));
  }
  next();
});
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }
  // if (!_.isEmpty(middlewares)) {
  Object.keys(destinationRoute).forEach((version) => {
    // if (file) {
    //   console.log('haha:file index', file)
    //   app[method](`/api/${version}${routeName}`, upload.single(file), middlewares, destinationRoute[version]);
    // } else {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
    // }
  });
  // } else {
  //   Object.keys(destinationRoute).forEach((version) => {
  //     app[method](`/api/${version}${routeName}`, destinationRoute[version]);
  //   });
  // }
};

// apis for admin
declareRoute('post', '/admin/login', [], UserHandle.login);
declareRoute('post', '/admin/change-password', [tokenToUserMiddleware], UserHandle.changePassword);
declareRoute('post', '/admin/logout', [tokenToUserMiddleware], UserHandle.logout);
declareRoute('post', '/admin/get', [tokenToUserMiddleware], UserHandle.get);
declareRoute('post', '/admin/send-otp', [], UserHandle.sendOTP);
declareRoute('post', '/admin/change-password-otp', [], UserHandle.checkOTPChangePassword);

declareRoute('post', '/admin/user/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-tai-khoan')], UserAdminHandle.list);
declareRoute('post', '/admin/user/create', [tokenToUserMiddleware, verifyPermission('create-user')], UserAdminHandle.create);
declareRoute('post', '/admin/user/update', [tokenToUserMiddleware, verifyPermission('update-user')], UserAdminHandle.update);
declareRoute('post', '/admin/user/inactive', [tokenToUserMiddleware, verifyPermission('inactive-user')], UserAdminHandle.inactive);
declareRoute('post', '/admin/user/get', [tokenToUserMiddleware, verifyPermission('get-user')], UserAdminHandle.get);
declareRoute('post', '/admin/user/grant-permission', [tokenToUserMiddleware, verifyPermission('grant-pemission')], UserAdminHandle.grantPermission);

declareRoute('post', '/admin/permission/list', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.list);
declareRoute('post', '/admin/permission/create', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.create);
declareRoute('post', '/admin/permission/update', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.update);
declareRoute('post', '/admin/permission/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.inactive);
declareRoute('post', '/admin/permission/get', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.get);

declareRoute('post', '/admin/group-permission/list', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.list);
declareRoute('post', '/admin/group-permission/create', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.create);
declareRoute('post', '/admin/group-permission/update', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.update);
declareRoute('post', '/admin/group-permission/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.inactive);
declareRoute('post', '/admin/group-permission/get', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.get);

declareRoute('post', '/admin/role/list', [tokenToUserMiddleware, verifyPermission('list-role')], RoleAdminHandle.list);
declareRoute('post', '/admin/role/create', [tokenToUserMiddleware, verifyPermission('create-role')], RoleAdminHandle.create);
declareRoute('post', '/admin/role/update', [tokenToUserMiddleware, verifyPermission('update-role')], RoleAdminHandle.update);
declareRoute('post', '/admin/role/inactive', [tokenToUserMiddleware, verifyPermission('inactive-role')], RoleAdminHandle.inactive);

declareRoute('post', '/admin/unit/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.list);
declareRoute('post', '/admin/unit/get', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.get);
declareRoute('post', '/admin/unit/list-level', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.listUnitLevel);
declareRoute('post', '/admin/unit/create', [tokenToUserMiddleware, verifyPermission('taodonvi')], UnitAdminHandle.create);
declareRoute('post', '/admin/unit/update', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], UnitAdminHandle.update);
declareRoute('post', '/admin/unit/inactive', [tokenToUserMiddleware, verifyPermission('xoa-don-vi')], UnitAdminHandle.inactive);
declareRoute('post', '/admin/unit/ordering', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], UnitAdminHandle.ordering);

declareRoute('post', '/admin/category/list', [tokenToUserMiddleware], CategoryAdminHandle.list);
declareRoute('post', '/admin/category/create', [tokenToUserMiddleware, verifyPermission('create-category')], CategoryAdminHandle.create);
declareRoute('post', '/admin/category/update', [tokenToUserMiddleware, verifyPermission('update-category')], CategoryAdminHandle.update);
declareRoute('post', '/admin/category/inactive', [tokenToUserMiddleware, verifyPermission('inactive-category')], CategoryAdminHandle.inactive);
declareRoute('post', '/admin/category/ordering', [tokenToUserMiddleware, verifyPermission('update-category')], CategoryAdminHandle.ordering);

declareRoute('post', '/admin/category-permission/list', [tokenToUserMiddleware], CategoryPermissionAdminHandle.list);

declareRoute('post', '/admin/position/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], PositionAdminHandle.list);
declareRoute('post', '/admin/position/get', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], PositionAdminHandle.get);
declareRoute('post', '/admin/position/create', [tokenToUserMiddleware, verifyPermission('taodonvi')], PositionAdminHandle.create);
declareRoute('post', '/admin/position/update', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], PositionAdminHandle.update);
declareRoute('post', '/admin/position/inactive', [tokenToUserMiddleware, verifyPermission('xoa-don-vi')], PositionAdminHandle.inactive);
declareRoute('post', '/admin/position/ordering', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], PositionAdminHandle.ordering);

declareRoute('post', '/admin/service/list-category', [tokenToUserMiddleware], ServiceAdminHandle.listCategories);
declareRoute('post', '/admin/service/list-service', [tokenToUserMiddleware], ServiceAdminHandle.listService);
declareRoute('post', '/admin/service/hide-show', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.hideShowService);
declareRoute('post', '/admin/service/ordering', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.orderingService);
declareRoute('post', '/admin/service/update-icon', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.updateIconService);
declareRoute('post', '/admin/service/list-child', [tokenToUserMiddleware], ServiceAdminHandle.listServiceChild);
declareRoute('post', '/admin/service/info-child', [tokenToUserMiddleware], ServiceAdminHandle.infoServiceChild);
declareRoute('post', '/admin/service/hide-show-child', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.hideShowServiceChildren);
declareRoute('post', '/admin/service/ordering-child', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.orderingServiceChildren);
declareRoute('post', '/admin/service/update-icon-child', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.updateIconServiceChildren);
declareRoute('post', '/admin/service/update-source', [tokenToUserMiddleware, verifyPermission('manager-service')], ServiceAdminHandle.updateServiceSource);

declareRoute('post', '/admin/petition/list', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.list);
declareRoute('post', '/admin/petition/list-for-individual', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.listForIndividual);
declareRoute('post', '/admin/petition/count', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.count);
declareRoute('post', '/admin/petition/count-for-individual', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.countForIndividual);
declareRoute('post', '/admin/petition/get', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.get);
declareRoute('post', '/admin/petition/get-log-job', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.getLogJob);
declareRoute('post', '/admin/petition/tiep-nhan-xu-ly', [tokenToUserMiddleware, verifyPermission('xu-ly-phan-anh')], PetitionAdminHandle.tiepNhanXuLy);
declareRoute('post', '/admin/petition/chuyen-xu-ly', [tokenToUserMiddleware, verifyPermission('dieu-chuyen-phan-anh')], PetitionAdminHandle.chuyenXuLy);
declareRoute('post', '/admin/petition/get-user-by-unit', [tokenToUserMiddleware, verifyPermission('dieu-chuyen-phan-anh')], PetitionAdminHandle.getUserByUnit);
declareRoute('post', '/admin/petition/get-unit-children', [tokenToUserMiddleware, verifyPermission('dieu-chuyen-phan-anh')], PetitionAdminHandle.getUnitChildren);
declareRoute('post', '/admin/petition/tu-choi-phan-anh', [tokenToUserMiddleware, verifyPermission('duyet-ket-qua')], PetitionAdminHandle.tuChoiPhanAnh);
declareRoute('post', '/admin/petition/tu-choi-xu-ly', [tokenToUserMiddleware, verifyPermission('xu-ly-phan-anh')], PetitionAdminHandle.tuChoiXuLy);
declareRoute('post', '/admin/petition/bao-trung', [tokenToUserMiddleware, verifyPermission('xu-ly-phan-anh')], PetitionAdminHandle.baoTrung);
declareRoute('post', '/admin/petition/list-by-user', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.listPetitionByUser);
declareRoute('post', '/admin/petition/cap-nhat-ket-qua', [tokenToUserMiddleware, verifyPermission('duyet-ket-qua')], PetitionAdminHandle.capNhatKetQua);
declareRoute('post', '/admin/petition/gui-duyet-ket-qua', [tokenToUserMiddleware, verifyPermission('xu-ly-phan-anh')], PetitionAdminHandle.guiDuyetKetQua);
declareRoute('post', '/admin/petition/sua-ket-qua', [tokenToUserMiddleware, verifyPermission('duyet-ket-qua')], PetitionAdminHandle.suaKetQua);
declareRoute('post', '/admin/petition/duyet-ket-qua', [tokenToUserMiddleware, verifyPermission('duyet-ket-qua')], PetitionAdminHandle.duyetKetQua);
declareRoute('post', '/admin/petition/update-category', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.updateCategory);
declareRoute('post', '/admin/petition/update-priority', [tokenToUserMiddleware, verifyPermission('view-list-petition')], PetitionAdminHandle.updatePriority);
declareRoute('post', '/admin/petition/tra-lai-xu-ly', [tokenToUserMiddleware, verifyPermission('dieu-chuyen-phan-anh')], PetitionAdminHandle.traLaiXuLy);
declareRoute('post', '/admin/petition/get-config-maintain', [tokenToUserMiddleware, verifyPermission('system-manager')], PetitionHandle.getConfigMaintain);
declareRoute('post', '/admin/petition/open-maintain', [tokenToUserMiddleware, verifyPermission('system-manager')], PetitionAdminHandle.openMaintain);
declareRoute('post', '/admin/petition/get-config-time-processing', [tokenToUserMiddleware, verifyPermission('system-manager')], PetitionAdminHandle.getConfigTimeProcessing);
declareRoute('post', '/admin/petition/update-config-time-processing', [tokenToUserMiddleware, verifyPermission('system-manager')], PetitionAdminHandle.updateConfigTimeProcessing);
declareRoute('post', '/admin/petition/sync-category', [], PetitionAdminHandle.syncCategory);
declareRoute('post', '/admin/petition/sync-guide', [], PetitionAdminHandle.syncGuide);
declareRoute('post', '/admin/petition/sync-processing', [], PetitionAdminHandle.syncProcessing);

declareRoute('post', '/admin/petition/statistic-by-month', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.statisticByMonth);
declareRoute('post', '/admin/petition/statistic-by-month-in-area', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.statisticByMonthInArea);
declareRoute('post', '/admin/petition/statistic-multi-month', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.statisticMultiMonth);
declareRoute('post', '/admin/petition/statistic-time-process-unit', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.statisticTimeProcessUnit);
declareRoute('post', '/admin/petition/statistic-rate-unit', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.statisticRateUnit);
declareRoute('post', '/admin/petition/statistic-by-category', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.statisticByCategory);
declareRoute('post', '/admin/petition/export-rate-unit', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.exportRateUnit);
declareRoute('post', '/admin/petition/export-time-process-unit', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.exportTimeProcessUnit);
declareRoute('post', '/admin/petition/export-by-category', [tokenToUserMiddleware, verifyPermission('thong-ke-phan-anh')], AdminStatisticHandle.exportByCategory);

//admin member
declareRoute('post', '/admin/member/list', [tokenToUserMiddleware], MemberAdminHandle.list);

declareRoute('post', '/admin/chatbot/open-maintain', [tokenToUserMiddleware, verifyPermission('system-manager')], ChatbotAdminHandle.openMaintain);
declareRoute('post', '/admin/chatbot/get-current-maintain', [tokenToUserMiddleware, verifyPermission('system-manager')], ChatbotAdminHandle.getCurrentMaintain);

//medical-hospital
declareRoute('post', '/admin/medical/hospital/add', [tokenToUserMiddleware, verifyPermission('medical-manager')], HospitalHandle.add);
declareRoute('post', '/admin/medical/hospital/modify', [tokenToUserMiddleware, verifyPermission('medical-manager')], HospitalHandle.modify);
declareRoute('post', '/admin/medical/hospital/inactive', [tokenToUserMiddleware, verifyPermission('medical-manager')], HospitalHandle.inactive);
declareRoute('post', '/admin/medical/hospital/list', [tokenToUserMiddleware, verifyPermission('medical-manager')], HospitalHandle.listForAdmin);
declareRoute('post', '/admin/medical/hospital/get', [tokenToUserMiddleware, verifyPermission('medical-manager')], HospitalHandle.get);

//medical-pharmacy
declareRoute('post', '/admin/medical/pharmacy/add', [tokenToUserMiddleware, verifyPermission('medical-manager')], PharmacyHandle.add);
declareRoute('post', '/admin/medical/pharmacy/modify', [tokenToUserMiddleware, verifyPermission('medical-manager')], PharmacyHandle.modify);
declareRoute('post', '/admin/medical/pharmacy/inactive', [tokenToUserMiddleware, verifyPermission('medical-manager')], PharmacyHandle.inactive);
declareRoute('post', '/admin/medical/pharmacy/list', [tokenToUserMiddleware, verifyPermission('medical-manager')], PharmacyHandle.listForAdmin);
declareRoute('post', '/admin/medical/pharmacy/get', [tokenToUserMiddleware, verifyPermission('medical-manager')], PharmacyHandle.get);

//medical-maternity-clinic
declareRoute('post', '/admin/medical/maternity-clinic/add', [tokenToUserMiddleware, verifyPermission('medical-manager')], MaternityClinicHandle.add);
declareRoute('post', '/admin/medical/maternity-clinic/modify', [tokenToUserMiddleware, verifyPermission('medical-manager')], MaternityClinicHandle.modify);
declareRoute('post', '/admin/medical/maternity-clinic/inactive', [tokenToUserMiddleware, verifyPermission('medical-manager')], MaternityClinicHandle.inactive);
declareRoute('post', '/admin/medical/maternity-clinic/list', [tokenToUserMiddleware, verifyPermission('medical-manager')], MaternityClinicHandle.listForAdmin);
declareRoute('post', '/admin/medical/maternity-clinic/get', [tokenToUserMiddleware, verifyPermission('medical-manager')], MaternityClinicHandle.get);

//education-school
declareRoute('post', '/admin/education/school/add', [tokenToUserMiddleware, verifyPermission('education-manager')], SchoolHandle.add);
declareRoute('post', '/admin/education/school/modify', [tokenToUserMiddleware, verifyPermission('education-manager')], SchoolHandle.modify);
declareRoute('post', '/admin/education/school/inactive', [tokenToUserMiddleware, verifyPermission('education-manager')], SchoolHandle.inactive);
declareRoute('post', '/admin/education/school/list', [tokenToUserMiddleware, verifyPermission('education-manager')], SchoolHandle.listForAdmin);
declareRoute('post', '/admin/education/school/get', [tokenToUserMiddleware, verifyPermission('education-manager')], SchoolHandle.get);

//education-teaching-center
declareRoute('post', '/admin/education/teaching-center/add', [tokenToUserMiddleware, verifyPermission('education-manager')], TeachingCenterHandle.add);
declareRoute('post', '/admin/education/teaching-center/modify', [tokenToUserMiddleware, verifyPermission('education-manager')], TeachingCenterHandle.modify);
declareRoute('post', '/admin/education/teaching-center/inactive', [tokenToUserMiddleware, verifyPermission('education-manager')], TeachingCenterHandle.inactive);
declareRoute('post', '/admin/education/teaching-center/list', [tokenToUserMiddleware, verifyPermission('education-manager')], TeachingCenterHandle.listForAdmin);
declareRoute('post', '/admin/education/teaching-center/get', [tokenToUserMiddleware, verifyPermission('education-manager')], TeachingCenterHandle.get);

//travel-festival
declareRoute('post', '/admin/travel/festival/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], FestivalHandle.add);
declareRoute('post', '/admin/travel/festival/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], FestivalHandle.modify);
declareRoute('post', '/admin/travel/festival/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], FestivalHandle.inactive);
declareRoute('post', '/admin/travel/festival/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], FestivalHandle.listForAdmin);
declareRoute('post', '/admin/travel/festival/get', [tokenToUserMiddleware, verifyPermission('travel-manager')], FestivalHandle.get);

//travel-hotel
declareRoute('post', '/admin/travel/hotel/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.add);
declareRoute('post', '/admin/travel/hotel/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.modify);
declareRoute('post', '/admin/travel/hotel/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.inactive);
declareRoute('post', '/admin/travel/hotel/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.listForAdmin);
declareRoute('post', '/admin/travel/hotel/get', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.get);
declareRoute('post', '/admin/travel/hotel/list-amenities', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.listAmenities);
declareRoute('post', '/admin/travel/hotel/switch-priority', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.switchPriority);
declareRoute('post', '/admin/travel/hotel/ordering', [tokenToUserMiddleware, verifyPermission('travel-manager')], HotelHandle.ordering);

//travel-restaurant
declareRoute('post', '/admin/travel/restaurant/check-name', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.checkName);
declareRoute('post', '/admin/travel/restaurant/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.add);
declareRoute('post', '/admin/travel/restaurant/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.modify);
declareRoute('post', '/admin/travel/restaurant/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.inactive);
declareRoute('post', '/admin/travel/restaurant/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.listForAdmin);
declareRoute('post', '/admin/travel/restaurant/get', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.get);
declareRoute('post', '/admin/travel/restaurant/switch-priority', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.switchPriority);
declareRoute('post', '/admin/travel/restaurant/ordering', [tokenToUserMiddleware, verifyPermission('travel-manager')], RestaurantHandle.ordering);

//travel-tourist-attraction
declareRoute('post', '/admin/travel/tourist-attraction/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], TouristAttractionHandle.add);
declareRoute('post', '/admin/travel/tourist-attraction/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], TouristAttractionHandle.modify);
declareRoute('post', '/admin/travel/tourist-attraction/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], TouristAttractionHandle.inactive);
declareRoute('post', '/admin/travel/tourist-attraction/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], TouristAttractionHandle.listForAdmin);
declareRoute('post', '/admin/travel/tourist-attraction/get', [tokenToUserMiddleware, verifyPermission('travel-manager')], TouristAttractionHandle.get);

//travel-tour-list
declareRoute('post', '/admin/travel/tour-list/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.add);
declareRoute('post', '/admin/travel/tour-list/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.modify);
declareRoute('post', '/admin/travel/tour-list/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.inactive);
declareRoute('post', '/admin/travel/tour-list/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.listForAdmin);
declareRoute('post', '/admin/travel/tour-list/get', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.get);
declareRoute('post', '/admin/travel/tour-list/switch-priority', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.switchPriority);
declareRoute('post', '/admin/travel/tour-list/ordering', [tokenToUserMiddleware, verifyPermission('travel-manager')], TourListHandle.ordering);

//banner
declareRoute('post', '/admin/banner/add', [tokenToUserMiddleware, verifyPermission('manager-banner')], BannerHandle.add);
declareRoute('post', '/admin/banner/modify', [tokenToUserMiddleware, verifyPermission('manager-banner')], BannerHandle.modify);
declareRoute('post', '/admin/banner/inactive', [tokenToUserMiddleware, verifyPermission('manager-banner')], BannerHandle.inactive);
declareRoute('post', '/admin/banner/list', [tokenToUserMiddleware, verifyPermission('manager-banner')], BannerHandle.list);

//instruction
declareRoute('post', '/admin/instruction/create', [tokenToUserMiddleware, verifyPermission('manager-service')], InstructionAdminHandle.create);
declareRoute('post', '/admin/instruction/modify', [tokenToUserMiddleware, verifyPermission('manager-service')], InstructionAdminHandle.modify);
declareRoute('post', '/admin/instruction/inactive', [tokenToUserMiddleware, verifyPermission('manager-service')], InstructionAdminHandle.inactive);
declareRoute('post', '/admin/instruction/list', [tokenToUserMiddleware, verifyPermission('manager-service')], InstructionAdminHandle.list);
declareRoute('post', '/admin/instruction/get', [tokenToUserMiddleware, verifyPermission('manager-service')], InstructionAdminHandle.get);

//tricks warning
declareRoute('post', '/admin/tricks-warning/create', [tokenToUserMiddleware, verifyPermission('tricks-warning-manager')], NotifyAdminHandle.create);
declareRoute('post', '/admin/tricks-warning/modify', [tokenToUserMiddleware, verifyPermission('tricks-warning-manager')], NotifyAdminHandle.modify);
declareRoute('post', '/admin/tricks-warning/inactive', [tokenToUserMiddleware, verifyPermission('tricks-warning-manager')], NotifyAdminHandle.inactive);
declareRoute('post', '/admin/tricks-warning/list', [tokenToUserMiddleware, verifyPermission('tricks-warning-manager')], NotifyAdminHandle.list);
declareRoute('post', '/admin/tricks-warning/get', [tokenToUserMiddleware, verifyPermission('tricks-warning-manager')], NotifyAdminHandle.get);

// warning info
declareRoute('post', '/admin/warning-info/create', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], NotifyAdminHandle.create);
declareRoute('post', '/admin/warning-info/modify', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], NotifyAdminHandle.modify);
declareRoute('post', '/admin/warning-info/inactive', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], NotifyAdminHandle.inactive);
declareRoute('post', '/admin/warning-info/list', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], NotifyAdminHandle.list);
declareRoute('post', '/admin/warning-info/get', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], NotifyAdminHandle.get);

declareRoute('post', '/admin/warning-info/category/create', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], CategoryNotificationAdminHandle.create);
declareRoute('post', '/admin/warning-info/category/modify', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], CategoryNotificationAdminHandle.modify);
declareRoute('post', '/admin/warning-info/category/inactive', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], CategoryNotificationAdminHandle.inactive);
declareRoute('post', '/admin/warning-info/category/list', [], CategoryNotificationAdminHandle.list);
declareRoute('post', '/admin/warning-info/category/get', [tokenToUserMiddleware, verifyPermission('warning-info-manager')], CategoryNotificationAdminHandle.get);

// notification
declareRoute('post', '/admin/notification/create', [tokenToUserMiddleware, verifyPermission('notification-manager')], NotifyAdminHandle.create);
declareRoute('post', '/admin/notification/modify', [tokenToUserMiddleware, verifyPermission('notification-manager')], NotifyAdminHandle.modify);
declareRoute('post', '/admin/notification/inactive', [tokenToUserMiddleware, verifyPermission('notification-manager')], NotifyAdminHandle.inactive);
declareRoute('post', '/admin/notification/list', [tokenToUserMiddleware, verifyPermission('notification-manager')], NotifyAdminHandle.list);
declareRoute('post', '/admin/notification/get', [tokenToUserMiddleware, verifyPermission('notification-manager')], NotifyAdminHandle.get);

declareRoute('post', '/admin/user-notify/list', [tokenToUserMiddleware], UserNotifyAdminHandle.list);
declareRoute('post', '/admin/user-notify/count', [tokenToUserMiddleware], UserNotifyAdminHandle.count);
declareRoute('post', '/admin/user-notify/seen', [tokenToUserMiddleware], UserNotifyAdminHandle.seen);

//system-log
declareRoute('post', '/admin/system-log/list', [tokenToUserMiddleware, verifyPermission('system-log-manager')], SystemLogAdminHandle.list);
declareRoute('post', '/admin/system-log/get', [tokenToUserMiddleware, verifyPermission('system-log-manager')], SystemLogAdminHandle.get);

// api for members
declareRoute('post', '/member/send-otp', [], MemberHandle.sendOTP);
declareRoute('post', '/member/list-district', [], MemberHandle.listDistrict);
declareRoute('post', '/member/list-ward', [], MemberHandle.listWard);
declareRoute('post', '/member/check-otp', [], MemberHandle.checkOTP);
declareRoute('post', '/member/register', [], MemberHandle.register);
declareRoute('post', '/member/update-profile', [tokenToUserMiddleware], MemberHandle.updateProfile);
declareRoute('post', '/member/login', [], MemberHandle.login);
declareRoute('post', '/member/logout', [tokenToUserMiddleware], MemberHandle.logout);
declareRoute('post', '/member/change-password', [tokenToUserMiddleware], MemberHandle.changePassword);
declareRoute('post', '/member/reset-password', [], MemberHandle.resetPassword);
declareRoute('post', '/member/get-location-detail', [], MemberHandle.getLocationDetail);
declareRoute('post', '/member/get-user-id', [tokenToUserMiddleware], MemberHandle.getUserId);
declareRoute('post', '/member/get', [tokenToUserMiddleware], MemberHandle.get);
declareRoute('post', '/member/get-config-remove-account', [tokenToUserMiddleware], MemberHandle.getConfigRemoveAccount);
declareRoute('post', '/member/send-feedback', [tokenToUserMiddleware], FeedbackHandle.sendFeedback);
declareRoute('post', '/member/tracking-action', [tokenToUserMiddleware], MemberHandle.tracking);
declareRoute('post', '/member/check-member-exists', [], MemberHandle.checkMember);

declareRoute('post', '/google/place-auto-complete', [], UtilHandle.placeAutoComplete);
declareRoute('post', '/google/place-detail', [], UtilHandle.placeDetail);
declareRoute('post', '/google/get-location-detail', [], UtilHandle.getLocationDetail);
declareRoute('post', '/region/list-district', [], UtilHandle.listDistrict);
declareRoute('post', '/region/list-ward', [], UtilHandle.listWard);
declareRoute('post', '/util/download', [], UtilHandle.download);

// declareRoute('post','/user/login',null,null,UserHandle.login)
// Object.keys(UserHandle.login).forEach((version) => {
//   app.post(`/${version}/user/login`, UserHandle.login[version]);
// });
////order
declareRoute('post', '/services/list', [], ServicesHandle.listService);
declareRoute('post', '/services/list-service-children', [], ServicesHandle.listServiceChild);
declareRoute('post', '/services/list-category', [], ServicesHandle.listCategories);
declareRoute('post', '/services/list-news', [], ServicesHandle.getNews);
declareRoute('post', '/services/list-news-category', [], ServicesHandle.getNewsCategory);
declareRoute('post', '/services/get-banner', [], ServicesHandle.getBanner);
declareRoute('post', '/services/get-weather', [], ServicesHandle.getWeather);
declareRoute('post', '/services/list-chatbot', [], ServicesHandle.listServiceChatbot);

//chatbot
declareRoute('post', '/chatbot/create-conversation', [], ChatbotHandle.createConversation);
declareRoute('post', '/chatbot/ask', [], ChatbotHandle.ask);
declareRoute('post', '/chatbot/ask-stream', [], ChatbotHandle.askStream);
declareRoute('post', '/chatbot/list-chat', [], ChatbotHandle.listChat);
declareRoute('post', '/chatbot/list-ask-default', [], ChatbotHandle.listAskDefault);
declareRoute('post', '/chatbot/list-conversation', [], ChatbotHandle.listConversation);
declareRoute('post', '/chatbot/admin/list-chat', [], ChatbotHandle.listChatAdmin);
declareRoute('post', '/chatbot/admin/list-conversation', [], ChatbotHandle.listConversationAdmin);
declareRoute('post', '/chatbot/list-device-id', [], ChatbotHandle.listDeviceId);
declareRoute('post', '/chatbot/speech-to-text', [], ChatbotHandle.speechToText);
declareRoute('post', '/chatbot/delete-conversation', [], ChatbotHandle.deleteConversation);

declareRoute('post', '/admin/chatbot/create-conversation-email', [tokenToUserMiddleware], EmailHandle.createConversation);
declareRoute('post', '/admin/chatbot/write-email', [tokenToUserMiddleware], EmailHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-email', [tokenToUserMiddleware], EmailHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-email', [tokenToUserMiddleware], EmailHandle.listChat);
declareRoute('post', '/admin/chatbot/save-email', [tokenToUserMiddleware], EmailHandle.save);

declareRoute('post', '/admin/chatbot/create-conversation-report', [tokenToUserMiddleware], ReportHandle.createConversation);
declareRoute('post', '/admin/chatbot/report', [tokenToUserMiddleware], ReportHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-report', [tokenToUserMiddleware], ReportHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-report', [tokenToUserMiddleware], ReportHandle.listChat);
declareRoute('post', '/admin/chatbot/save-report', [tokenToUserMiddleware], ReportHandle.save);
declareRoute('post', '/admin/chatbot/report/get-conversation', [tokenToUserMiddleware], ReportHandle.getConversation);
declareRoute('post', '/internal/chatbot/report/process-waiting-job', [], ReportHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-speech', [tokenToUserMiddleware], SpeechHandle.createConversation);
declareRoute('post', '/admin/chatbot/speech', [tokenToUserMiddleware], SpeechHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-speech', [tokenToUserMiddleware], SpeechHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-speech', [tokenToUserMiddleware], SpeechHandle.listChat);
declareRoute('post', '/admin/chatbot/save-speech', [tokenToUserMiddleware], SpeechHandle.save);
declareRoute('post', '/admin/chatbot/speech/get-conversation', [tokenToUserMiddleware], SpeechHandle.getConversation);
declareRoute('post', '/internal/chatbot/speech/process-waiting-job', [], SpeechHandle.processWaitingJob);
declareRoute('post', '/admin/chatbot/speech/get-suggest-prompt', [tokenToUserMiddleware], SpeechHandle.getSuggestedPrompt);

declareRoute('post', '/admin/chatbot/create-conversation-planning', [tokenToUserMiddleware], PlanningHandle.createConversation);
declareRoute('post', '/admin/chatbot/planning', [tokenToUserMiddleware], PlanningHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-planning', [tokenToUserMiddleware], PlanningHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-planning', [tokenToUserMiddleware], PlanningHandle.listChat);
declareRoute('post', '/admin/chatbot/planning/get-conversation', [tokenToUserMiddleware], PlanningHandle.getConversation);
declareRoute('post', '/internal/chatbot/planning/process-waiting-job', [], PlanningHandle.processWaitingJob);
declareRoute('post', '/admin/chatbot/planning/get-suggest-prompt', [tokenToUserMiddleware], PlanningHandle.getSuggestedPrompt);

declareRoute('post', '/admin/chatbot/create-conversation-read-newspaper', [tokenToUserMiddleware], ReadNewspaperHandle.createConversation);
declareRoute('post', '/admin/chatbot/read-newspaper', [tokenToUserMiddleware], ReadNewspaperHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-read-newspaper', [tokenToUserMiddleware], ReadNewspaperHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-read-newspaper', [tokenToUserMiddleware], ReadNewspaperHandle.listChat);
declareRoute('post', '/admin/chatbot/save-read-newspaper', [tokenToUserMiddleware], ReadNewspaperHandle.save);
declareRoute('post', '/admin/chatbot/get-summary', [tokenToUserMiddleware], ReadNewspaperHandle.getSummary);

declareRoute('post', '/admin/chatbot/create-conversation-work-schedule', [tokenToUserMiddleware], WorkScheduleHandle.createConversation);
declareRoute('post', '/admin/chatbot/work-schedule', [tokenToUserMiddleware], WorkScheduleHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-work-schedule', [tokenToUserMiddleware], WorkScheduleHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-work-schedule', [tokenToUserMiddleware], WorkScheduleHandle.listChat);
declareRoute('post', '/admin/chatbot/save-work-schedule', [tokenToUserMiddleware], WorkScheduleHandle.save);

declareRoute('post', '/admin/chatbot/create-conversation-newspaper-analysis', [tokenToUserMiddleware], NewspaperAnalysisHandle.createConversation);
declareRoute('post', '/admin/chatbot/newspaper-analysis', [tokenToUserMiddleware], NewspaperAnalysisHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-newspaper-analysis', [tokenToUserMiddleware], NewspaperAnalysisHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-newspaper-analysis', [tokenToUserMiddleware], NewspaperAnalysisHandle.listChat);
declareRoute('post', '/admin/chatbot/save-newspaper-analysis', [tokenToUserMiddleware], NewspaperAnalysisHandle.save);
declareRoute('post', '/admin/chatbot/write-newspaper', [tokenToUserMiddleware], NewspaperAnalysisHandle.write);
declareRoute('post', '/admin/chatbot/filter-newspaper', [tokenToUserMiddleware], NewspaperAnalysisHandle.filter);

declareRoute('post', '/admin/chatbot/create-conversation-make-decision', [tokenToUserMiddleware], MakeDecisionHandle.createConversation);
declareRoute('post', '/admin/chatbot/make-decision', [tokenToUserMiddleware], MakeDecisionHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-make-decision', [tokenToUserMiddleware], MakeDecisionHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-make-decision', [tokenToUserMiddleware], MakeDecisionHandle.listChat);
declareRoute('post', '/admin/chatbot/save-make-decision', [tokenToUserMiddleware], MakeDecisionHandle.save);
declareRoute('post', '/admin/chatbot/make-decision/get-conversation', [tokenToUserMiddleware], MakeDecisionHandle.getConversation);
declareRoute('post', '/internal/chatbot/make-decision/process-waiting-job', [], MakeDecisionHandle.processWaitingJob);
declareRoute('post', '/admin/chatbot/make-decision/get-suggest-prompt', [tokenToUserMiddleware], MakeDecisionHandle.getSuggestedPrompt);

declareRoute('post', '/admin/chatbot/create-conversation-document-summary', [tokenToUserMiddleware], DocumentSummaryHandle.createConversation);
declareRoute('post', '/admin/chatbot/document-summary', [tokenToUserMiddleware], DocumentSummaryHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-document-summary', [tokenToUserMiddleware], DocumentSummaryHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-document-summary', [tokenToUserMiddleware], DocumentSummaryHandle.listChat);
declareRoute('post', '/admin/chatbot/save-document-summary', [tokenToUserMiddleware], DocumentSummaryHandle.save);
declareRoute('post', '/admin/chatbot/upload-file-document-summary', [upload.single('fileUpload'), tokenToUserMiddleware], DocumentSummaryHandle.uploadFile);
declareRoute('post', '/admin/chatbot/save-file-document-summary', [tokenToUserMiddleware], DocumentSummaryHandle.saveFile);
declareRoute('post', '/admin/chatbot/document-summary/get-conversation', [tokenToUserMiddleware], DocumentSummaryHandle.getConversation);
declareRoute('post', '/internal/chatbot/document-summary/process-waiting-job', [], DocumentSummaryHandle.processWaitingJob);
declareRoute('post', '/callback/document-summary', [], DocumentSummaryHandle.callback);

declareRoute('post', '/admin/chatbot/create-conversation-meeting-summary', [tokenToUserMiddleware], MeetingSummaryHandle.createConversation);
declareRoute('post', '/admin/chatbot/meeting-summary', [tokenToUserMiddleware], MeetingSummaryHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-meeting-summary', [tokenToUserMiddleware], MeetingSummaryHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-meeting-summary', [tokenToUserMiddleware], MeetingSummaryHandle.listChat);
declareRoute('post', '/admin/chatbot/get-conversation-meeting-summary', [tokenToUserMiddleware], MeetingSummaryHandle.getConversation);
declareRoute('post', '/internal/chatbot/meeting-summary/process-waiting-job', [], MeetingSummaryHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-meeting-synthesis', [tokenToUserMiddleware], MeetingSynthesisHandle.createConversation);
declareRoute('post', '/admin/chatbot/meeting-synthesis', [tokenToUserMiddleware], MeetingSynthesisHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-meeting-synthesis', [tokenToUserMiddleware], MeetingSynthesisHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-meeting-synthesis', [tokenToUserMiddleware], MeetingSynthesisHandle.listChat);
declareRoute('post', '/admin/chatbot/get-conversation-meeting-synthesis', [tokenToUserMiddleware], MeetingSynthesisHandle.getConversation);
declareRoute('post', '/internal/chatbot/meeting-synthesis/process-waiting-job', [], MeetingSynthesisHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-summary-manage-document', [tokenToUserMiddleware], SummaryManageDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/summary-manage-document', [tokenToUserMiddleware], SummaryManageDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-summary-manage-document', [tokenToUserMiddleware], SummaryManageDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-summary-manage-document', [tokenToUserMiddleware], SummaryManageDocumentHandle.listChat);
declareRoute('post', '/admin/chatbot/summary-manage-document/get-conversation', [tokenToUserMiddleware], SummaryManageDocumentHandle.getConversation);
declareRoute('post', '/internal/chatbot/summary-manage-document/process-waiting-job', [], SummaryManageDocumentHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-speech-manage-document', [tokenToUserMiddleware], SpeechManageDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/speech-manage-document', [tokenToUserMiddleware], SpeechManageDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-speech-manage-document', [tokenToUserMiddleware], SpeechManageDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-speech-manage-document', [tokenToUserMiddleware], SpeechManageDocumentHandle.listChat);
declareRoute('post', '/admin/chatbot/speech-manage-document/get-conversation', [tokenToUserMiddleware], SpeechManageDocumentHandle.getConversation);
declareRoute('post', '/internal/chatbot/speech-manage-document/process-waiting-job', [], SpeechManageDocumentHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-write-report-manage-document', [tokenToUserMiddleware], WriteReportManageDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/write-report-manage-document', [tokenToUserMiddleware], WriteReportManageDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-write-report-manage-document', [tokenToUserMiddleware], WriteReportManageDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-write-report-manage-document', [tokenToUserMiddleware], WriteReportManageDocumentHandle.listChat);
declareRoute('post', '/admin/chatbot/write-report-manage-document/get-conversation', [tokenToUserMiddleware], WriteReportManageDocumentHandle.getConversation);
declareRoute('post', '/internal/chatbot/write-report-manage-document/process-waiting-job', [], WriteReportManageDocumentHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-video-summary', [tokenToUserMiddleware], VideoSummaryHandle.createConversation);
declareRoute('post', '/admin/chatbot/video-summary', [tokenToUserMiddleware], VideoSummaryHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-video-summary', [tokenToUserMiddleware], VideoSummaryHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-video-summary', [tokenToUserMiddleware], VideoSummaryHandle.listChat);
declareRoute('post', '/admin/chatbot/get-conversation-video-summary', [tokenToUserMiddleware], VideoSummaryHandle.getConversation);
declareRoute('post', '/internal/chatbot/video-summary/process-waiting-job', [], VideoSummaryHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-video-excerpt', [tokenToUserMiddleware], VideoExcerptHandle.createConversation);
declareRoute('post', '/admin/chatbot/video-excerpt', [tokenToUserMiddleware], VideoExcerptHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-video-excerpt', [tokenToUserMiddleware], VideoExcerptHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-video-excerpt', [tokenToUserMiddleware], VideoExcerptHandle.listChat);
declareRoute('post', '/admin/chatbot/get-conversation-video-excerpt', [tokenToUserMiddleware], VideoExcerptHandle.getConversation);
declareRoute('post', '/internal/chatbot/video-excerpt/process-waiting-job', [], VideoExcerptHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/evaluation', [tokenToUserMiddleware], EvaluationHandle.askStream);

declareRoute('post', '/admin/chatbot/create-conversation-spell-check', [tokenToUserMiddleware], SpellCheckHandle.createConversation);
declareRoute('post', '/admin/chatbot/spell-check', [tokenToUserMiddleware], SpellCheckHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-spell-check', [tokenToUserMiddleware], SpellCheckHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-spell-check', [tokenToUserMiddleware], SpellCheckHandle.listChat);
declareRoute('post', '/admin/chatbot/create-doc', [tokenToUserMiddleware], SpellCheckHandle.createDoc);
declareRoute('post', '/admin/chatbot/fix-message', [tokenToUserMiddleware], SpellCheckHandle.fixMessage);
declareRoute('post', '/admin/chatbot/spell-check/get-conversation', [tokenToUserMiddleware], SpellCheckHandle.getConversation);
declareRoute('post', '/internal/chatbot/spell-check/process-waiting-job', [], SpellCheckHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-itt', [tokenToUserMiddleware], ITTHandle.createConversation);
declareRoute('post', '/admin/chatbot/image-to-text', [tokenToUserMiddleware], ITTHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-itt', [tokenToUserMiddleware], ITTHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-itt', [tokenToUserMiddleware], ITTHandle.listChat);
declareRoute('post', '/admin/chatbot/image-to-text/get-conversation', [tokenToUserMiddleware], ITTHandle.getConversation);
declareRoute('post', '/internal/chatbot/image-to-text/process-waiting-job', [], ITTHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-write-report', [tokenToUserMiddleware], WriteReportHandle.createConversation);
declareRoute('post', '/admin/chatbot/write-report', [tokenToUserMiddleware], WriteReportHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-write-report', [tokenToUserMiddleware], WriteReportHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-write-report', [tokenToUserMiddleware], WriteReportHandle.listChat);
declareRoute('post', '/admin/chatbot/save-write-report', [tokenToUserMiddleware], WriteReportHandle.save);
declareRoute('post', '/admin/chatbot/write-report/get-conversation', [tokenToUserMiddleware], WriteReportHandle.getConversation);
declareRoute('post', '/internal/chatbot/write-report/process-waiting-job', [], WriteReportHandle.processWaitingJob);
declareRoute('post', '/admin/chatbot/write-report/get-suggest-prompt', [tokenToUserMiddleware], WriteReportHandle.getSuggestedPrompt);

declareRoute('post', '/admin/chatbot/create-conversation-write-article', [tokenToUserMiddleware, verifyPermission('press-manager')], WriteArticleHandle.createConversation);
declareRoute('post', '/admin/chatbot/write-article', [tokenToUserMiddleware, verifyPermission('press-manager')], WriteArticleHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-write-article', [tokenToUserMiddleware, verifyPermission('press-manager')], WriteArticleHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-write-article', [tokenToUserMiddleware, verifyPermission('press-manager')], WriteArticleHandle.listChat);
declareRoute('post', '/admin/chatbot/write-article/get-conversation', [tokenToUserMiddleware], WriteArticleHandle.getConversation);
declareRoute('post', '/internal/chatbot/write-article/process-waiting-job', [], WriteArticleHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/chat-with-doc/create-conversation', [tokenToUserMiddleware], ChatWithDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/chat-with-doc/ask', [tokenToUserMiddleware], ChatWithDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/chat-with-doc/list-conversation', [tokenToUserMiddleware], ChatWithDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/chat-with-doc/list-chat', [tokenToUserMiddleware], ChatWithDocumentHandle.listChat);
declareRoute('post', '/admin/chatbot/chat-with-doc/save', [tokenToUserMiddleware], ChatWithDocumentHandle.save);
declareRoute('post', '/admin/chatbot/chat-with-doc/upload', [upload.single('fileUpload'), tokenToUserMiddleware], ChatWithDocumentHandle.uploadFile);
declareRoute('post', '/admin/chatbot/chat-with-doc/search-document', [tokenToUserMiddleware], ChatWithDocumentHandle.searchDocument);
declareRoute('post', '/admin/chatbot/chat-with-doc/list-document', [tokenToUserMiddleware], ChatWithDocumentHandle.listDocument);
declareRoute('post', '/admin/chatbot/chat-with-doc/get-document', [tokenToUserMiddleware], ChatWithDocumentHandle.getDocument);
declareRoute('post', '/admin/chatbot/chat-with-doc/attach-document', [tokenToUserMiddleware], ChatWithDocumentHandle.attachDocument);
declareRoute('post', '/admin/chatbot/chat-with-doc/get-start-question', [tokenToUserMiddleware], ChatWithDocumentHandle.getStartQuestion);
declareRoute('post', '/admin/chatbot/chat-with-doc/get-suggest-question', [tokenToUserMiddleware], ChatWithDocumentHandle.getSuggestedQuestion);
declareRoute('post', '/callback/update-document-status', [], ChatWithDocumentHandle.callback);
declareRoute('post', '/admin/chatbot/chat-with-doc/update', [tokenToUserMiddleware], ChatWithDocumentHandle.update);
declareRoute('post', '/admin/chatbot/chat-with-doc/delete', [tokenToUserMiddleware], ChatWithDocumentHandle.delete);
declareRoute('post', '/admin/chatbot/chat-with-doc/check-document-status', [tokenToUserMiddleware], ChatWithDocumentHandle.checkDocumentStatus);
declareRoute('post', '/admin/chatbot/chat-with-doc/search-unit', [tokenToUserMiddleware], ChatWithDocumentHandle.searchUnit);
declareRoute('post', '/admin/chatbot/chat-with-doc/list-shared-documents', [tokenToUserMiddleware], ChatWithDocumentHandle.listSharedDocuments);

declareRoute('post', '/admin/chatbot/chat-mange-document/create-conversation', [tokenToUserMiddleware], ChatManageDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/chat-mange-document/ask', [tokenToUserMiddleware], ChatManageDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/chat-mange-document/list-conversation', [tokenToUserMiddleware], ChatManageDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/chat-mange-document/list-chat', [tokenToUserMiddleware], ChatManageDocumentHandle.listChat);
declareRoute('post', '/admin/chatbot/chat-mange-document/attach-document', [tokenToUserMiddleware], ChatManageDocumentHandle.attachDocument);
declareRoute('post', '/admin/chatbot/chat-mange-document/get-start-question', [tokenToUserMiddleware], ChatManageDocumentHandle.getStartQuestion);
declareRoute('post', '/admin/chatbot/chat-mange-document/get-suggest-question', [tokenToUserMiddleware], ChatManageDocumentHandle.getSuggestedQuestion);
declareRoute('post', '/admin/chatbot/chat-mange-document/list-document', [tokenToUserMiddleware], ChatManageDocumentHandle.listDocument);
declareRoute('post', '/admin/chatbot/chat-mange-document/get-document', [tokenToUserMiddleware], ChatManageDocumentHandle.getDocument);
declareRoute('post', '/admin/chatbot/chat-mange-document/search-document', [tokenToUserMiddleware], ChatManageDocumentHandle.searchDocument);

declareRoute('post', '/admin/chatbot/speech-with-doc/create-conversation', [tokenToUserMiddleware], WriteSpeechWithDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/speech-with-doc/ask', [tokenToUserMiddleware], WriteSpeechWithDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/speech-with-doc/list-conversation', [tokenToUserMiddleware], WriteSpeechWithDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/speech-with-doc/get-conversation', [tokenToUserMiddleware], WriteSpeechWithDocumentHandle.getConversation);
declareRoute('post', '/admin/chatbot/speech-with-doc/list-chat', [tokenToUserMiddleware], WriteSpeechWithDocumentHandle.listChat);
declareRoute('post', '/internal/chatbot/speech-with-doc/process-waiting-job', [], WriteSpeechWithDocumentHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/report-with-doc/create-conversation', [tokenToUserMiddleware], WriteReportWithDocumentHandle.createConversation);
declareRoute('post', '/admin/chatbot/report-with-doc/ask', [tokenToUserMiddleware], WriteReportWithDocumentHandle.askStream);
declareRoute('post', '/admin/chatbot/report-with-doc/list-conversation', [tokenToUserMiddleware], WriteReportWithDocumentHandle.listConversation);
declareRoute('post', '/admin/chatbot/report-with-doc/list-chat', [tokenToUserMiddleware], WriteReportWithDocumentHandle.listChat);

declareRoute('post', '/admin/chatbot/create-conversation-tts', [tokenToUserMiddleware, verifyPermission('television-manager')], TextToSpeechHandle.createConversation);
declareRoute('post', '/admin/chatbot/text-to-speech', [tokenToUserMiddleware, verifyPermission('television-manager')], TextToSpeechHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-tts', [tokenToUserMiddleware, verifyPermission('television-manager')], TextToSpeechHandle.listConversation);
declareRoute('post', '/admin/chatbot/list-chat-tts', [tokenToUserMiddleware, verifyPermission('television-manager')], TextToSpeechHandle.listChat);
declareRoute('post', '/admin/chatbot/list-voices', [tokenToUserMiddleware, verifyPermission('television-manager')], TextToSpeechHandle.listVoices);
declareRoute('post', '/callback/update-audio-tts', [], TextToSpeechHandle.callback);
declareRoute('post', '/admin/chatbot/text-to-speech/get-conversation', [tokenToUserMiddleware, verifyPermission('television-manager')], TextToSpeechHandle.getConversation);
declareRoute('post', '/internal/chatbot/text-to-speech/process-waiting-job', [], TextToSpeechHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-write-script', [tokenToUserMiddleware], WriteScriptHandle.createConversation);
declareRoute('post', '/admin/chatbot/write-script', [tokenToUserMiddleware], WriteScriptHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-write-script', [tokenToUserMiddleware], WriteScriptHandle.listConversation);
declareRoute('post', '/admin/chatbot/get-conversation-write-script', [tokenToUserMiddleware], WriteScriptHandle.getConversation);
declareRoute('post', '/admin/chatbot/list-chat-write-script', [tokenToUserMiddleware], WriteScriptHandle.listChat);
declareRoute('post', '/internal/chatbot/write-script/process-waiting-job', [], WriteScriptHandle.processWaitingJob);

declareRoute('post', '/admin/chatbot/create-conversation-testimony', [tokenToUserMiddleware], TestimonyHandle.createConversation);
declareRoute('post', '/admin/chatbot/testimony', [tokenToUserMiddleware], TestimonyHandle.askStream);
declareRoute('post', '/admin/chatbot/list-conversation-testimony', [tokenToUserMiddleware], TestimonyHandle.listConversation);
declareRoute('post', '/admin/chatbot/get-conversation-testimony', [tokenToUserMiddleware], TestimonyHandle.getConversation);
declareRoute('post', '/admin/chatbot/list-chat-testimony', [tokenToUserMiddleware], TestimonyHandle.listChat);
declareRoute('post', '/internal/chatbot/testimony/process-waiting-job', [], TestimonyHandle.processWaitingJob);

declareRoute('post', '/chatbot-json/create-conversation', [], ChatbotJsonHandle.createConversation);
declareRoute('post', '/chatbot-json/ask-stream', [tokenToUserMiddleware], ChatbotJsonHandle.askStream);
declareRoute('post', '/chatbot-json/list-chat', [tokenToUserMiddleware], ChatbotJsonHandle.listChat);
declareRoute('post', '/chatbot-json/list-ask-default', [], ChatbotJsonHandle.listAskDefault);
declareRoute('post', '/chatbot-json/list-conversation', [tokenToUserMiddleware], ChatbotJsonHandle.listConversation);
declareRoute('post', '/chatbot-json/list-device-id', [], ChatbotJsonHandle.listDeviceId);
declareRoute('post', '/chatbot-json/speech-to-text', [tokenToUserMiddleware], ChatbotJsonHandle.speechToText);
declareRoute('post', '/chatbot-json/delete-conversation', [tokenToUserMiddleware], ChatbotJsonHandle.deleteConversation);
declareRoute('post', '/chatbot-json/stop-stream', [tokenToUserMiddleware], ChatbotJsonHandle.stopStream);
declareRoute('post', '/chatbot-json/get-answer', [], ChatbotJsonHandle.getAnswer);
declareRoute('post', '/chatbot-json/rating', [tokenToUserMiddleware], ChatbotJsonHandle.rating);

declareRoute('post', '/admin/chatbot-test/ask', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testAsk);
declareRoute('post', '/admin/chatbot-test/add', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testAdd);
declareRoute('post', '/admin/chatbot-test/get', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testGet);
declareRoute('post', '/admin/chatbot-test/list', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testList);
declareRoute('post', '/admin/chatbot-test/remove', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testRemove);
declareRoute('post', '/admin/chatbot-test/add-group', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testAddGroup);
declareRoute('post', '/admin/chatbot-test/group-get', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testGroupGet);
declareRoute('post', '/admin/chatbot-test/get-group', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testListGroupGet);
declareRoute('post', '/admin/chatbot-test/list-group', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testListGroup);
declareRoute('post', '/admin/chatbot-test/add-category', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testAddCategory);
declareRoute('post', '/admin/chatbot-test/get-category', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testGetCategory);
declareRoute('post', '/admin/chatbot-test/list-category', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testListCategory);
declareRoute('post', '/admin/chatbot-test/remove-category', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testRemoveCategory);
declareRoute('post', '/admin/chatbot-test/group-update-isvalid', [tokenToUserMiddleware, verifyPermission('tester')], ChatbotHandle.testGroupUpdateIsValid);

declareRoute('post', '/chatbot/create-conversation-socio-economic', [tokenToUserMiddleware], SocioEconomicHandle.createConversation);
declareRoute('post', '/chatbot/ask-socio-economic', [tokenToUserMiddleware], SocioEconomicHandle.askStream);
declareRoute('post', '/chatbot/list-chat-socio-economic', [tokenToUserMiddleware], SocioEconomicHandle.listChat);
declareRoute('post', '/socio-economic/list-category', [tokenToUserMiddleware], SocioEconomicHandle.listCategory);
declareRoute('post', '/socio-economic/list-title', [tokenToUserMiddleware], SocioEconomicHandle.listTitle);
declareRoute('post', '/socio-economic/list-month', [tokenToUserMiddleware], SocioEconomicHandle.listMonth);
declareRoute('post', '/socio-economic/list-year', [tokenToUserMiddleware], SocioEconomicHandle.listYear);

//education
declareRoute('post', '/education/school/list', [], SchoolHandle.list);
declareRoute('post', '/education/school/get', [], SchoolHandle.get);
declareRoute('post', '/education/teaching-center/list', [], TeachingCenterHandle.list);
declareRoute('post', '/education/teaching-center/get', [], TeachingCenterHandle.get);

declareRoute('post', '/edu/education/list', [], Educationhandle.list);
declareRoute('post', '/edu/education/list-level', [], Educationhandle.listEducationLevel);
declareRoute('post', '/edu/education/get', [], Educationhandle.get);
declareRoute('post', '/admin/edu/education/list', [tokenToUserMiddleware, verifyPermission('education-manager')], Educationhandle.listForAdmin);
declareRoute('post', '/admin/edu/education/add', [tokenToUserMiddleware, verifyPermission('education-manager')], Educationhandle.add);
declareRoute('post', '/admin/edu/education/modify', [tokenToUserMiddleware, verifyPermission('education-manager')], Educationhandle.modify);
declareRoute('post', '/admin/edu/education/inactive', [tokenToUserMiddleware, verifyPermission('education-manager')], Educationhandle.inactive);

declareRoute('post', '/edu/education-center/list', [], EducationCenterhandle.list);
declareRoute('post', '/edu/education-center/get', [], EducationCenterhandle.get);
declareRoute('post', '/admin/edu/education-center/list', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationCenterhandle.listForAdmin);
declareRoute('post', '/admin/edu/education-center/add', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationCenterhandle.add);
declareRoute('post', '/admin/edu/education-center/modify', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationCenterhandle.modify);
declareRoute('post', '/admin/edu/education-center/inactive', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationCenterhandle.inactive);

declareRoute('post', '/edu/education-job/list', [], EducationJobhandle.list);
declareRoute('post', '/edu/education-job/get', [], EducationJobhandle.get);
declareRoute('post', '/admin/edu/education-job/list', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationJobhandle.listForAdmin);
declareRoute('post', '/admin/edu/education-job/add', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationJobhandle.add);
declareRoute('post', '/admin/edu/education-job/modify', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationJobhandle.modify);
declareRoute('post', '/admin/edu/education-job/inactive', [tokenToUserMiddleware, verifyPermission('education-manager')], EducationJobhandle.inactive);

//administrative-agency
declareRoute('post', '/administrative-agency/law-enforcement-agency/add', [tokenToUserMiddleware], LawEnforcementAgencyHandle.add);
declareRoute('post', '/administrative-agency/law-enforcement-agency/modify', [tokenToUserMiddleware], LawEnforcementAgencyHandle.modify);
declareRoute('post', '/administrative-agency/law-enforcement-agency/inactive', [tokenToUserMiddleware], LawEnforcementAgencyHandle.inactive);
declareRoute('post', '/administrative-agency/law-enforcement-agency/list', [], LawEnforcementAgencyHandle.list);
declareRoute('post', '/administrative-agency/law-enforcement-agency/list-structure', [], LawEnforcementAgencyHandle.listStructure);
declareRoute('post', '/administrative-agency/law-enforcement-agency/list-level', [], LawEnforcementAgencyHandle.listLevel);
declareRoute('post', '/admin/administrative-agency/law-enforcement-agency/list', [tokenToUserMiddleware], LawEnforcementAgencyHandle.listForAdmin);
declareRoute('post', '/administrative-agency/law-enforcement-agency/get', [], LawEnforcementAgencyHandle.get);

declareRoute('post', '/administrative-agency/legislature/add', [tokenToUserMiddleware], LegislatureHandle.add);
declareRoute('post', '/administrative-agency/legislature/modify', [tokenToUserMiddleware], LegislatureHandle.modify);
declareRoute('post', '/administrative-agency/legislature/inactive', [tokenToUserMiddleware], LegislatureHandle.inactive);
declareRoute('post', '/administrative-agency/legislature/list', [], LegislatureHandle.list);
declareRoute('post', '/admin/administrative-agency/legislature/list', [tokenToUserMiddleware], LegislatureHandle.listForAdmin);
declareRoute('post', '/administrative-agency/legislature/get', [], LegislatureHandle.get);

//entertainment
declareRoute('post', '/entertainment/billiards/add', [], BilliardsHandle.add);
declareRoute('post', '/entertainment/billiards/modify', [], BilliardsHandle.modify);
declareRoute('post', '/entertainment/billiards/inactive', [], BilliardsHandle.inactive);
declareRoute('post', '/entertainment/billiards/list', [], BilliardsHandle.list);
declareRoute('post', '/entertainment/billiards/get', [], BilliardsHandle.get);

declareRoute('post', '/entertainment/karaoke/add', [], KaraokeHandle.add);
declareRoute('post', '/entertainment/karaoke/modify', [], KaraokeHandle.modify);
declareRoute('post', '/entertainment/karaoke/inactive', [], KaraokeHandle.inactive);
declareRoute('post', '/entertainment/karaoke/list', [], KaraokeHandle.list);
declareRoute('post', '/entertainment/karaoke/get', [], KaraokeHandle.get);

declareRoute('post', '/agriculture/ocop/list', [], OcopProductHandle.list);
declareRoute('post', '/agriculture/ocop/get', [], OcopProductHandle.get);
declareRoute('post', '/admin/agriculture/ocop/list', [tokenToUserMiddleware], OcopProductHandle.listForAdmin);
declareRoute('post', '/admin/agriculture/ocop/add', [tokenToUserMiddleware], OcopProductHandle.add);
declareRoute('post', '/admin/agriculture/ocop/modify', [tokenToUserMiddleware], OcopProductHandle.modify);
declareRoute('post', '/admin/agriculture/ocop/inactive', [tokenToUserMiddleware], OcopProductHandle.inactive);

declareRoute('post', '/agriculture/cooperative/list', [], CooperativetHandle.list);
declareRoute('post', '/agriculture/cooperative/get', [], CooperativetHandle.get);
declareRoute('post', '/admin/agriculture/cooperative/list', [tokenToUserMiddleware], CooperativetHandle.listForAdmin);
declareRoute('post', '/admin/agriculture/cooperative/add', [tokenToUserMiddleware], CooperativetHandle.add);
declareRoute('post', '/admin/agriculture/cooperative/modify', [tokenToUserMiddleware], CooperativetHandle.modify);
declareRoute('post', '/admin/agriculture/cooperative/inactive', [tokenToUserMiddleware], CooperativetHandle.inactive);

declareRoute('post', '/agriculture/traditional-village/list', [], TraditionalVillageHandle.list);
declareRoute('post', '/agriculture/traditional-village/get', [], TraditionalVillageHandle.get);
declareRoute('post', '/admin/agriculture/traditional-village/list', [tokenToUserMiddleware], TraditionalVillageHandle.listForAdmin);
declareRoute('post', '/admin/agriculture/traditional-village/add', [tokenToUserMiddleware], TraditionalVillageHandle.add);
declareRoute('post', '/admin/agriculture/traditional-village/modify', [tokenToUserMiddleware], TraditionalVillageHandle.modify);
declareRoute('post', '/admin/agriculture/traditional-village/inactive', [tokenToUserMiddleware], TraditionalVillageHandle.inactive);

declareRoute('post', '/travel/historical-site/list', [], HistoricalSiteHandle.list);
declareRoute('post', '/travel/historical-site/get', [], HistoricalSiteHandle.get);
declareRoute('post', '/admin/travel/historical-site/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], HistoricalSiteHandle.listForAdmin);
declareRoute('post', '/admin/travel/historical-site/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], HistoricalSiteHandle.add);
declareRoute('post', '/admin/travel/historical-site/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], HistoricalSiteHandle.modify);
declareRoute('post', '/admin/travel/historical-site/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], HistoricalSiteHandle.inactive);

declareRoute('post', '/travel/cultural-heritage/list', [], CulturalHeritageHandle.list);
declareRoute('post', '/travel/cultural-heritage/get', [], CulturalHeritageHandle.get);
declareRoute('post', '/admin/travel/cultural-heritage/list', [tokenToUserMiddleware, verifyPermission('travel-manager')], CulturalHeritageHandle.listForAdmin);
declareRoute('post', '/admin/travel/cultural-heritage/add', [tokenToUserMiddleware, verifyPermission('travel-manager')], CulturalHeritageHandle.add);
declareRoute('post', '/admin/travel/cultural-heritage/modify', [tokenToUserMiddleware, verifyPermission('travel-manager')], CulturalHeritageHandle.modify);
declareRoute('post', '/admin/travel/cultural-heritage/inactive', [tokenToUserMiddleware, verifyPermission('travel-manager')], CulturalHeritageHandle.inactive);

declareRoute('post', '/entertainment/massage/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], MassageHandle.add);
declareRoute('post', '/entertainment/massage/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], MassageHandle.modify);
declareRoute('post', '/entertainment/massage/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], MassageHandle.inactive);
declareRoute('post', '/entertainment/massage/list', [], MassageHandle.list);
declareRoute('post', '/entertainment/massage/get', [], MassageHandle.get);

declareRoute('post', '/entertainment/sports-field/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], SportsFieldHandle.add);
declareRoute('post', '/entertainment/sports-field/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], SportsFieldHandle.modify);
declareRoute('post', '/entertainment/sports-field/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], SportsFieldHandle.inactive);
declareRoute('post', '/entertainment/sports-field/list', [], SportsFieldHandle.list);
declareRoute('post', '/entertainment/sports-field/get', [], SportsFieldHandle.get);
declareRoute('post', '/entertainment/sports-field/list-type', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], SportsFieldHandle.listType);

//medical
declareRoute('post', '/medical/hospital/list', [], HospitalHandle.list);
declareRoute('post', '/medical/hospital/get', [], HospitalHandle.get);
declareRoute('post', '/medical/maternity-clinic/list', [], MaternityClinicHandle.list);
declareRoute('post', '/medical/maternity-clinic/get', [], MaternityClinicHandle.get);
declareRoute('post', '/medical/pharmacy/list', [], PharmacyHandle.list);
declareRoute('post', '/medical/pharmacy/get', [], PharmacyHandle.get);

declareRoute('post', '/medical/vaccine-center/list', [], VaccineCenterHanlde.list);
declareRoute('post', '/medical/vaccine-center/get', [], VaccineCenterHanlde.get);
declareRoute('post', '/admin/medical/vaccine-center/list', [tokenToUserMiddleware, verifyPermission('medical-manager')], VaccineCenterHanlde.listForAdmin);
declareRoute('post', '/admin/medical/vaccine-center/add', [tokenToUserMiddleware, verifyPermission('medical-manager')], VaccineCenterHanlde.add);
declareRoute('post', '/admin/medical/vaccine-center/modify', [tokenToUserMiddleware, verifyPermission('medical-manager')], VaccineCenterHanlde.modify);
declareRoute('post', '/admin/medical/vaccine-center/inactive', [tokenToUserMiddleware, verifyPermission('medical-manager')], VaccineCenterHanlde.inactive);

declareRoute('post', '/medical/drug-store/list', [], DrugStoreHandle.list);
declareRoute('post', '/medical/drug-store/get', [], DrugStoreHandle.get);
declareRoute('post', '/admin/medical/drug-store/list', [tokenToUserMiddleware, verifyPermission('medical-manager')], DrugStoreHandle.listForAdmin);
declareRoute('post', '/admin/medical/drug-store/add', [tokenToUserMiddleware, verifyPermission('medical-manager')], DrugStoreHandle.add);
declareRoute('post', '/admin/medical/drug-store/modify', [tokenToUserMiddleware, verifyPermission('medical-manager')], DrugStoreHandle.modify);
declareRoute('post', '/admin/medical/drug-store/inactive', [tokenToUserMiddleware, verifyPermission('medical-manager')], DrugStoreHandle.inactive);

//press
declareRoute('post', '/press/press-agency/list', [], PressAgencyHandle.list);
declareRoute('post', '/press/press-agency/get', [], PressAgencyHandle.get);
declareRoute('post', '/admin/press/press-agency/list', [], PressAgencyHandle.listForAdmin);
declareRoute('post', '/admin/press/press-agency/add', [], PressAgencyHandle.add);
declareRoute('post', '/admin/press/press-agency/modify', [], PressAgencyHandle.modify);
declareRoute('post', '/admin/press/press-agency/inactive', [], PressAgencyHandle.inactive);

declareRoute('post', '/press/tele-service/list', [], TeleServiceHandle.list);
declareRoute('post', '/press/tele-service/get', [], TeleServiceHandle.get);
declareRoute('post', '/admin/press/tele-service/list', [], TeleServiceHandle.listForAdmin);
declareRoute('post', '/admin/press/tele-service/add', [], TeleServiceHandle.add);
declareRoute('post', '/admin/press/tele-service/modify', [], TeleServiceHandle.modify);
declareRoute('post', '/admin/press/tele-service/inactive', [], TeleServiceHandle.inactive);

//law
declareRoute('post', '/law/lawyer-organization/list', [], LawyerOrganizationHandle.list);
declareRoute('post', '/law/lawyer-organization/get', [], LawyerOrganizationHandle.get);
declareRoute('post', '/admin/law/lawyer-organization/list', [tokenToUserMiddleware], LawyerOrganizationHandle.listForAdmin);
declareRoute('post', '/admin/law/lawyer-organization/add', [tokenToUserMiddleware], LawyerOrganizationHandle.add);
declareRoute('post', '/admin/law/lawyer-organization/modify', [tokenToUserMiddleware], LawyerOrganizationHandle.modify);
declareRoute('post', '/admin/law/lawyer-organization/inactive', [tokenToUserMiddleware], LawyerOrganizationHandle.inactive);

declareRoute('post', '/law/notary-office/list', [], NotaryOfficeHandle.list);
declareRoute('post', '/law/notary-office/get', [], NotaryOfficeHandle.get);
declareRoute('post', '/admin/law/notary-office/list', [tokenToUserMiddleware], NotaryOfficeHandle.listForAdmin);
declareRoute('post', '/admin/law/notary-office/add', [tokenToUserMiddleware], NotaryOfficeHandle.add);
declareRoute('post', '/admin/law/notary-office/modify', [tokenToUserMiddleware], NotaryOfficeHandle.modify);
declareRoute('post', '/admin/law/notary-office/inactive', [tokenToUserMiddleware], NotaryOfficeHandle.inactive);

//travel
declareRoute('post', '/travel/festival/list', [], FestivalHandle.list);
declareRoute('post', '/travel/festival/get', [], FestivalHandle.get);
declareRoute('post', '/travel/hotel/list', [], HotelHandle.list);
declareRoute('post', '/travel/hotel/get', [], HotelHandle.get);
declareRoute('post', '/travel/restaurant/list', [], RestaurantHandle.list);
declareRoute('post', '/travel/restaurant/get', [], RestaurantHandle.get);
declareRoute('post', '/travel/tourist-attraction/list', [], TouristAttractionHandle.list);
declareRoute('post', '/travel/tourist-attraction/get', [], TouristAttractionHandle.get);
declareRoute('post', '/travel/tour-list/list', [], TourListHandle.list);
declareRoute('post', '/travel/tour-list/get', [], TourListHandle.get);
declareRoute('post', '/travel/tourist-company/list', [], TouristCompanyHandle.list);
declareRoute('post', '/travel/tourist-company/get', [], TouristCompanyHandle.get);
declareRoute('post', '/admin/travel/tourist-company/list', [tokenToUserMiddleware], TouristCompanyHandle.listForAdmin);
declareRoute('post', '/admin/travel/tourist-company/add', [tokenToUserMiddleware], TouristCompanyHandle.add);
declareRoute('post', '/admin/travel/tourist-company/modify', [tokenToUserMiddleware], TouristCompanyHandle.modify);
declareRoute('post', '/admin/travel/tourist-company/inactive', [tokenToUserMiddleware], TouristCompanyHandle.inactive);

//petition
declareRoute('post', '/petition/create', [tokenToUserMiddleware], PetitionHandle.create);
declareRoute('post', '/petition/get', [], PetitionHandle.get);
declareRoute('post', '/petition/list', [tokenToUserMiddleware], PetitionHandle.list);
declareRoute('post', '/petition/update', [tokenToUserMiddleware], PetitionHandle.update);
declareRoute('post', '/petition/list-community', [], PetitionHandle.listCommunity);
declareRoute('post', '/petition/category/list', [], PetitionHandle.listCategory);
declareRoute('post', '/petition/get-config-maintain', [], PetitionHandle.getConfigMaintain);
declareRoute('post', '/petition/rate', [tokenToUserMiddleware], PetitionHandle.rating);
declareRoute('post', '/petition/statistic', [], PetitionHandle.statistic);

declareRoute('get', '/proxy/config', [], ProxyHandle.configApp);
declareRoute('post', '/admin/proxy/force-update', [tokenToUserMiddleware, verifyPermission('system-manager')], ProxyHandle.forceUpdate);
declareRoute('post', '/admin/proxy/open-maintain', [tokenToUserMiddleware, verifyPermission('system-manager')], ProxyHandle.openMaintain);
declareRoute('post', '/admin/proxy/get-force-inf', [tokenToUserMiddleware, verifyPermission('system-manager')], ProxyHandle.getForceInf);
declareRoute('post', '/admin/proxy/get-maintain-inf', [tokenToUserMiddleware, verifyPermission('system-manager')], ProxyHandle.getMaintainInf);

declareRoute('post', '/admin/version/create', [tokenToUserMiddleware, verifyPermission('system-manager')], VersionHandle.create);
declareRoute('post', '/admin/version/modify', [tokenToUserMiddleware, verifyPermission('system-manager')], VersionHandle.modify);
declareRoute('post', '/admin/version/inactive', [tokenToUserMiddleware, verifyPermission('system-manager')], VersionHandle.inactive);
declareRoute('post', '/admin/version/list', [tokenToUserMiddleware, verifyPermission('system-manager')], VersionHandle.list);

declareRoute('post', '/notify/add-token', [], NotifyHandle.addToken);
declareRoute('post', '/assistant/notify/add-token', [], NotifyHandle.addTokenAssistant);

declareRoute('post', '/dich-vu-cong/list', [], DichvucongHandle.list);
declareRoute('post', '/dich-vu-cong/list-procedure', [], DichvucongHandle.listProcedure);
declareRoute('post', '/dich-vu-cong/list-event', [], DichvucongHandle.listEvent);
declareRoute('post', '/dich-vu-cong/get-procedure', [], DichvucongHandle.getProcedure);

// instruction
declareRoute('post', '/instruction/list', [], InstructionHandle.list);
declareRoute('post', '/instruction/get', [], InstructionHandle.get);

//traffic
declareRoute('post', '/traffic/registration-center/list', [], TrafficRegistrationCenterHandle.list);
declareRoute('post', '/traffic/registration-center/get', [], TrafficRegistrationCenterHandle.get);
declareRoute('post', '/admin/traffic/registration-center/list', [tokenToUserMiddleware], TrafficRegistrationCenterHandle.listForAdmin);
declareRoute('post', '/admin/traffic/registration-center/add', [tokenToUserMiddleware], TrafficRegistrationCenterHandle.add);
declareRoute('post', '/admin/traffic/registration-center/modify', [tokenToUserMiddleware], TrafficRegistrationCenterHandle.modify);
declareRoute('post', '/admin/traffic/registration-center/inactive', [tokenToUserMiddleware], TrafficRegistrationCenterHandle.inactive);

declareRoute('post', '/traffic/bus-station/list', [], TrafficBusStationHandle.list);
declareRoute('post', '/traffic/bus-station/get', [], TrafficBusStationHandle.get);
declareRoute('post', '/admin/traffic/bus-station/list', [tokenToUserMiddleware], TrafficBusStationHandle.listForAdmin);
declareRoute('post', '/admin/traffic/bus-station/add', [tokenToUserMiddleware], TrafficBusStationHandle.add);
declareRoute('post', '/admin/traffic/bus-station/modify', [tokenToUserMiddleware], TrafficBusStationHandle.modify);
declareRoute('post', '/admin/traffic/bus-station/inactive', [tokenToUserMiddleware], TrafficBusStationHandle.inactive);

//emergency-contact
declareRoute('post', '/emergency/contact/list', [], EmergencyContactHandle.list);
declareRoute('post', '/emergency/contact/get', [], EmergencyContactHandle.get);
declareRoute('post', '/admin/emergency/contact/list', [tokenToUserMiddleware], EmergencyContactHandle.listForAdmin);
declareRoute('post', '/admin/emergency/contact/add', [tokenToUserMiddleware], EmergencyContactHandle.add);
declareRoute('post', '/admin/emergency/contact/modify', [tokenToUserMiddleware], EmergencyContactHandle.modify);
declareRoute('post', '/admin/emergency/contact/inactive', [tokenToUserMiddleware], EmergencyContactHandle.inactive);

//entertainment-children-playarea
declareRoute('post', '/entertainment/children-playarea/list', [], EntertainmentChildrenPlayAreaHandle.list);
declareRoute('post', '/entertainment/children-playarea/get', [], EntertainmentChildrenPlayAreaHandle.get);
declareRoute('post', '/admin/entertainment/children-playarea/list', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentChildrenPlayAreaHandle.listForAdmin);
declareRoute('post', '/admin/entertainment/children-playarea/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentChildrenPlayAreaHandle.add);
declareRoute('post', '/admin/entertainment/children-playarea/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentChildrenPlayAreaHandle.modify);
declareRoute('post', '/admin/entertainment/children-playarea/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentChildrenPlayAreaHandle.inactive);

//entertainment-cinema
declareRoute('post', '/entertainment/cinema/list', [], EntertainmentCinemaHandle.list);
declareRoute('post', '/entertainment/cinema/get', [], EntertainmentCinemaHandle.get);
declareRoute('post', '/admin/entertainment/cinema/list', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCinemaHandle.listForAdmin);
declareRoute('post', '/admin/entertainment/cinema/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCinemaHandle.add);
declareRoute('post', '/admin/entertainment/cinema/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCinemaHandle.modify);
declareRoute('post', '/admin/entertainment/cinema/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCinemaHandle.inactive);

//entertainment-coffee
declareRoute('post', '/entertainment/coffee/list', [], EntertainmentCoffeeHandle.list);
declareRoute('post', '/entertainment/coffee/get', [], EntertainmentCoffeeHandle.get);
declareRoute('post', '/admin/entertainment/coffee/list', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCoffeeHandle.listForAdmin);
declareRoute('post', '/admin/entertainment/coffee/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCoffeeHandle.add);
declareRoute('post', '/admin/entertainment/coffee/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCoffeeHandle.modify);
declareRoute('post', '/admin/entertainment/coffee/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentCoffeeHandle.inactive);

//entertainment-ecozone
declareRoute('post', '/entertainment/ecozone/list', [], EntertainmentEcozoneHandle.list);
declareRoute('post', '/entertainment/ecozone/get', [], EntertainmentEcozoneHandle.get);
declareRoute('post', '/admin/entertainment/ecozone/list', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentEcozoneHandle.listForAdmin);
declareRoute('post', '/admin/entertainment/ecozone/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentEcozoneHandle.add);
declareRoute('post', '/admin/entertainment/ecozone/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentEcozoneHandle.modify);
declareRoute('post', '/admin/entertainment/ecozone/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentEcozoneHandle.inactive);

//entertainment-shopping-mall
declareRoute('post', '/entertainment/shopping-mall/list', [], EntertainmentShoppingMallHandle.list);
declareRoute('post', '/entertainment/shopping-mall/get', [], EntertainmentShoppingMallHandle.get);
declareRoute('post', '/admin/entertainment/shopping-mall/list', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentShoppingMallHandle.listForAdmin);
declareRoute('post', '/admin/entertainment/shopping-mall/add', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentShoppingMallHandle.add);
declareRoute('post', '/admin/entertainment/shopping-mall/modify', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentShoppingMallHandle.modify);
declareRoute('post', '/admin/entertainment/shopping-mall/inactive', [tokenToUserMiddleware, verifyPermission('manager-entertainment')], EntertainmentShoppingMallHandle.inactive);

//government-hp
declareRoute('post', '/government/haiphong/list', [], GovernmentHPHandle.list);
declareRoute('post', '/government/haiphong/get', [], GovernmentHPHandle.get);
declareRoute('post', '/admin/government/haiphong/list', [tokenToUserMiddleware], GovernmentHPHandle.listForAdmin);
declareRoute('post', '/admin/government/haiphong/add', [tokenToUserMiddleware], GovernmentHPHandle.add);
declareRoute('post', '/admin/government/haiphong/modify', [tokenToUserMiddleware], GovernmentHPHandle.modify);
declareRoute('post', '/admin/government/haiphong/inactive', [tokenToUserMiddleware], GovernmentHPHandle.inactive);

//QLVB
declareRoute('post', '/qlvb/list', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.documentList);
declareRoute('post', '/qlvb/get', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.documentGet);
declareRoute('post', '/qlvb/get-comments', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.documentGetComments);
declareRoute('post', '/qlvb/settings', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.userSettings);
declareRoute('post', '/qlvb/get-settings', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.getUserSettings);
declareRoute('post', '/qlvb/get-status', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.documentStatus);
declareRoute('post', '/qlvb/summary', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.documentSummary);
declareRoute('post', '/qlvb/sync', [tokenToUserMiddleware, verifyPermission('qlvb')], QLVBHandle.documentSync);

declareRoute('post', '/app/send-crash-log', [], AppHandle.sendCrashReport);


//agent
declareRoute('post', '/agent/switch', [tokenToUserMiddleware, verifyPermission('system-manager')], AgentHandle.switch);
declareRoute('post', '/agent/list', [tokenToUserMiddleware, verifyPermission('system-manager')], AgentHandle.list);
declareRoute('post', '/agent/update', [tokenToUserMiddleware, verifyPermission('system-manager')], AgentHandle.update);
declareRoute('post', '/agent/create', [tokenToUserMiddleware, verifyPermission('system-manager')], AgentHandle.create);

app.get('/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.resolve(__dirname, 'files', filename);

  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);
});

app.get('/proxy', (req, res) => {
  const url = req.query.url;

  if (!url || !url.startsWith(config.proxyRequestServer.serverChatBot)) {
    return res.status(400).send('Invalid image URL');
  }

  request
    .get(url)
    .on('error', () => res.status(500).send('Failed to fetch image'))
    .pipe(res);
});
const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo('Server listening at port:', port);
});

io.on('connection', function (socket) {
  console.log('socket:connected');
  socket.on('join', (roomId, cb) => {
    console.log('socket:join', roomId);
    socket.join(roomId);
  });

  socket.on('leave', (roomId, cb) => {
    console.log('socket:leave', roomId);
    socket.leave(roomId);
  });

  socket.on('disconnect', (reason) => {
    console.log('socket:disconnected', reason);
  });
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
