const _ = require('lodash');
const async = require('async');
const config = require('config');
const ms = require('ms');
const moment = require('moment');
const CONSTANTS = require('../const');
const schedule = require('node-schedule');
const PetitionStat = require('../models/petitionStat');
const Petition = require('../models/petition');

class PetitionStatJob {
  constructor() {
    this.init();
  }

  init() {
    this.generateAndSaveStats();

    schedule.scheduleJob('*/15 * * * *', this.generateAndSaveStats);
  }

  generateAndSaveStats = async () => {
    const nowVN = new Date().toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      hour12: false
    });

    try {
      const now = moment();
      const firstMonth = moment('2025-01-01'); // hoặc mốc đầu tiên có data
      const monthlyResults = [];

      while (firstMonth.isSameOrBefore(now, 'month')) {
        const year = firstMonth.year();
        const month = firstMonth.month() + 1;

        const stats = await this.getMonthlyStats(year, month); // sẽ throw nếu lỗi
        monthlyResults.push(stats);

        firstMonth.add(1, 'month');
      }

      const result = {
        total: 0,
        choTiepNhan: 0,
        daTiepNhan: 0,
        dangXuLy: 0,
        daXuLy: 0,
        daTuChoi: 0
      };

      for (const stats of monthlyResults) {
        for (const s of stats) {
          result.total += s.count;
          switch (s._id) {
            case 0: result.choTiepNhan += s.count; break;
            case 1: result.daTiepNhan += s.count; break;
            case 2: result.dangXuLy += s.count; break;
            case 3: result.daXuLy += s.count; break;
            case 4: result.daTuChoi += s.count; break;
          }
        }
      }

      await PetitionStat.create(result);
      console.log(`[${nowVN}] ✅ Đã lưu thống kê tổng cộng`, result);
    } catch (err) {
      console.error(`[${nowVN}] ❌ Dừng thống kê! Lỗi xảy ra, không lưu gì cả.`);
    }
  };

  getMonthlyStats = async (year, month) => {
    const start = moment.utc({ year, month: month - 1 }).startOf('month').valueOf();
    const end = moment.utc(start).endOf('month').valueOf();
    const monthLabel = `${String(month).padStart(2, '0')}/${year}`;

    try {
      const stats = await Petition.aggregate([
        {
          $match: {
            createdAt: { $gte: start, $lte: end }
          }
        },
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 }
          }
        }
      ]);


      const nowVN = new Date().toLocaleString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        hour12: false
      });
      console.log(`[${nowVN}] 📊 Thống kê tháng ${monthLabel}:`, stats);

      return stats;
    } catch (err) {
      console.error(`❌ Lỗi khi thống kê tháng ${monthLabel}:`, err);
      throw err; // ném ra ngoài để dừng job
    }
  };
}

module.exports = new PetitionStatJob;
