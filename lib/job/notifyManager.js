const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const rp = require('request-promise');
const UserModel = require('../models/user')
const UserNotifyModel = require('../models/userNotify')

class NotifyManager {
  constructor() {

  }

  handleNotifyUnit(unit, permission, notification) {
    UserModel
      .find({
        units: unit,
        status: 1
      })
      .populate("permissions", "code -_id")
      .populate({
        path: "groupPermissions",
        populate: {
          path: "permissions",
          select: "code -_id",
        },
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }

        results.map((user) => {
          let hasPermission = false;
          if(user.permissions && user.permissions.length && user.permissions.some(per => (per.code === 'admin-all' || per.code === permission))) {
            hasPermission = true
          }
          if(!permission) {
            hasPermission = true
          }
          user.groupPermissions && user.groupPermissions.some((group) => {
            if(group.permissions && group.permissions.length && group.permissions.some(per => (per.code === 'admin-all' || per.code === permission))) {
              hasPermission = true
              return true
            }
          })
          if(hasPermission) {
            this.handleNotify(user._id.toString(), notification)
          }
        })

      })
  }

  handleNotify(user, notification) {

    PushNotifyManager
      .sendToMember(user, notification.title, notification.message, notification.data, notification.eventName,'cms')

    UserNotifyModel
      .create({
        user: user,
        title: notification.title || '',
        message: notification.message || '',
        data: notification.data || {},
      })
  }
}

module.exports = new NotifyManager;
