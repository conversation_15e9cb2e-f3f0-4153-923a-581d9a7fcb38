const _ = require('lodash');
const CONSTANTS = require('../const');
const MESSAGES = require('../message');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const AgentModel = require('../models/agent')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const rp = require('request-promise');

class AgentManager {
  constructor() {
    this.agents = []
    this.syncConfig();
    setInterval(() => {
      this.syncConfig();
    }, ms('5m'));
  }

  syncConfig() {
    AgentModel
      .find({
      })
      .lean()
      .exec((err, results) => {
        if(!err && results.length) {
          this.agents = results;
        }
      })
  }

  getAgentValue(name, isDoc) {
    const agent = this.agents.find(item => item.name === name);
    if (!agent) {
      if(isDoc) {
        return config.chatbotAgentWithDoc[name];
      } else {
        return config.chatbotAgent[name];
      }
    }
    if(isDoc) {
      return agent.valueWithDoc;
    }
    return agent.value;
  }

  isAgentDev(name) {
    const agent = this.agents.find(item => item.name === name);
    if(!agent) {
      return false
    }
    return agent.env === 'dev';
  }

  setEnv(name, env, cb) {
    const agent = this.agents.find(item => item.name === name);
    if(!agent) {
      return cb({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head:'Thông báo',
          body: 'Agent không tồn tại'
        }
      });
    }
    agent.env = env;
    const agentIndex = this.agents.findIndex(item => item.name === name);
    if (agentIndex !== -1) {
      this.agents[agentIndex].env = env;
    }
    AgentModel.findByIdAndUpdate(agent._id, {
      $set: {
        env: env,
        updatedAt: Date.now()
      }
    },{new: true}, (err, result) => {
      if(err) {
        cb({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        })
      } else {
        cb(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: `Environment switched to ${env}`,
          data: result
        })
      }
    })
  }

}

module.exports = new AgentManager;
