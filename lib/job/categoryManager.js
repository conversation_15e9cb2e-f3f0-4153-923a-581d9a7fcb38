const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const rp = require('request-promise');

class CategoryManager {
  constructor() {
    this.categories = []
    this.categoryString = ''
    this.categoryStringArr = []
    this.syncConfig();
    setInterval(() => {
      this.syncConfig();
    }, ms('5m'));
  }

  syncConfig() {
    CategoryModel
      .find({
      })
      .lean()
      .exec((err, results) => {
        if(!err && results.length) {
          this.categories = results;
          this.categoryString = results.map(item => item.name).join(",");
          this.categoryStringArr = results.map(item => item.name)
        }
      })
  }



  addPetition(id, content) {
    let category
    let categoryReason
    const userId = uuidv4()
    const serverChatbot = config.proxyRequestServer.serverChatBot;
    let conversationId;
    let response;

    const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: userId
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id) {
            conversationId = result.conversation_id
            ask();
            next({
              code: 200
            });
          } else {
            return next({
              code: 400
            })
          }
        })
        .catch((error) => {
          console.log(error);
          return next({
            code: 400,
            error: error
          })
        })
  }

  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
}

  const  ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api-v2/chat_stream`,
        responseType: 'stream',
        data: {
          "conversation_id": conversationId,
          text: `Tôi là cán bộ xử lý phản ánh kiến nghị của người dân với nội dung như sau: ${content}. Giúp tôi phân loại phản ánh đó vào một trong các lĩnh vực sau: ${this.categoryString}. Đưa ra lý do tại sao lại chọn`
        },
        timeout:300000
      });

      const stream = resAxios.data;

      resAxios.data.on('data',(chunk) => {
        try {
          const data = JSON.parse(chunk.toString());
          if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;
          }
        } catch (e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if(response) {
          const categoryText = findFirstMatch(response,this.categoryStringArr)

          this.categories.some((cat) =>{

            if(categoryText == cat.name || categoryText == cat.name.toLowerCase()) {
              category = cat._id;
              categoryReason = response;
              updatePetition()
              return true
            }
          })
        }

      });
      stream.on('error', (err) => {
        console.log("ahihi",err)
      });
    } catch (err) {
      console.log("ahihi",err)
    }
  }


  function findFirstMatch(paragraph, fields) {
    let firstField = null;
    let firstIndex = Infinity;

    for (const field of fields) {
      const position = paragraph.indexOf(field);
      if (position !== -1) {
        if (position < firstIndex) {
          firstIndex = position;
          firstField = field;
        }
      }
    }

    return firstField; // Trả về lĩnh vực xuất hiện đầu tiên
  }

    const updatePetition = () => {
      PetitionModel
        .findOneAndUpdate({
          _id: id
        },{
          category,
          categoryReason
        },(err, petition) => {
            PushNotifyManager
              .sendToMember(petition.member.toString(), '', ``, {link: 'MyPetitionDetailScreen', extras: {id}}, 'petition_update','')
        })
    }

    async.waterfall([
      createConversation
    ], (err, data) => {
    })
  }

}

module.exports = new CategoryManager;
