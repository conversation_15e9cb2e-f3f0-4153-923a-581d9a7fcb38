const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const rp = require('request-promise');

class GuideManager {
  constructor() {

  }

  addPetition(id, content, place) {

    const userId = uuidv4()
    const serverChatbot = config.proxyRequestServer.serverChatBot;
    let response;
    let conversationId;

    const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: userId
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id) {
            conversationId = result.conversation_id
            ask();
            next({
              code: 200
            });
          } else {
            return next({
              code: 400
            })
          }
        })
        .catch((error) => {
          return next({
            code: 400,
            error: error
          })
        })
  }

  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
  }

  const  ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api-v2/chat_stream`,
        responseType: 'stream',
        data: {
          "conversation_id": conversationId,
          text: `Tôi là cán bộ xử lý phản ánh trong chính quyền. tôi có nhận được 1 phản ánh của ngươi dân gửi lên. xin vui lòng hướng dẫn cho tôi cách thức xử lý của phản ánh như cần gửi đến đơn vị nào, phòng ban nào, cũng như hướng giải quyết của các phòng ban, đơn vị để thoả đáng cho người dân. nội dung phản ánh như sau: Địa điểm ${place}. Nội dung:${content}`
        },
        timeout:300000
      });

      const stream = resAxios.data;

      resAxios.data.on('data',(chunk) => {
        try {
          const data = JSON.parse(chunk.toString());
          if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;
          }
        } catch (e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if(response) {
          updatePetition()
        }
      });
      stream.on('error', (err) => {
      });
    } catch (err) {
    }
  }

    const updatePetition = () => {
      PetitionModel
        .update({
          _id: id
        },{
          guideContent: response
        },() => {
        })
    }

    async.waterfall([
      createConversation
    ], (err, data) => {
    })
  }

}

module.exports = new GuideManager;
