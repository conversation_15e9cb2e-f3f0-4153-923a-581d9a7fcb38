const _ = require('lodash');
const async = require('async');
const Document = require('../models/document');
const ChatBotManager = require('./chatStreamManager');

class DocumentWaitingManager {
  constructor() {
    this.waitingJobs = {};
    this.checkInterval = 30000; // 30 seconds
    this.intervalId = null;
    this.startChecking();
  }

  addJob(conversationId, jobData) {
    this.waitingJobs[conversationId] = {
      ...jobData,
      addedAt: Date.now()
    };

    // Set document status to PROCESSING in ChatBotManager
    try {
      ChatBotManager.setDocumentStatus(conversationId, 'PROCESSING');
      logger.logInfo(`Set document status to PROCESSING for conversation ${conversationId} in ChatBotManager`);
    } catch (error) {
      logger.logError([error], 'DocumentWaitingManager.addJob.updateChatBotManager', { conversationId });
    }

    if (!this.intervalId) {
      this.startChecking();
    }
  }

  removeJob(conversationId) {
    if (this.waitingJobs[conversationId]) {
      delete this.waitingJobs[conversationId];
    }

    // If no more jobs, stop the interval
    if (Object.keys(this.waitingJobs).length === 0 && this.intervalId) {
      this.stopChecking();
    }
  }

  startChecking() {
    if (!this.intervalId) {
      this.intervalId = setInterval(() => this.checkDocuments(), this.checkInterval);
    }
  }

  stopChecking() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  checkDocuments() {
    const conversationIds = Object.keys(this.waitingJobs);

    if (conversationIds.length === 0) {
      this.stopChecking();
      return;
    }

    conversationIds.forEach(conversationId => {
      const jobData = this.waitingJobs[conversationId];

      if (!jobData || !jobData.documents || jobData.documents.length === 0) {
        // No documents to check, execute the job
        logger.logInfo(`No documents to check for conversation ${conversationId}, executing job immediately`);
        this.executeJob(conversationId);
        return;
      }

      // Check if all documents are completed
      async.every(jobData.documents, (doc, callback) => {
        const docId = doc._id || doc; // Handle both object and string/ObjectId
        Document.findOne({ _id: docId }, 'status')
          .lean()
          .exec((err, document) => {
            if (err || !document) {
              return callback(null, false);
            }

            doc.status = document.status;
            callback(null, document.status === 'COMPLETED');
          });
      }, (err, allCompleted) => {
        if (err) {
          console.error('Error checking document status:', err);
          return;
        }

        if (allCompleted) {
          // All documents are completed, execute the job
          logger.logInfo(`All documents completed for conversation ${conversationId}, executing job, func checkDocuments, allCompleted`);
          this.executeJob(conversationId);
        }
      });
    });
  }

  executeJob(conversationId) {
    const jobData = this.waitingJobs[conversationId];

    if (!jobData) {
      return;
    }

    // Update the document status in ChatBotManager to indicate processing is complete
    try {
      // Set document status to COMPLETED in ChatBotManager
      ChatBotManager.setDocumentStatus(conversationId, 'COMPLETED');
    } catch (error) {
      logger.logError([error], 'DocumentWaitingManager.executeJob.updateChatBotManager', { conversationId });
    }

    // Instead of calling the original askFunction, we'll make a new request to the internal API
    try {
      const axios = require('axios');
      const config = require('config');

      // Get the stored request data
      const requestData = jobData.requestData || {};

      // Make a new request to the internal API that doesn't require authentication
      logger.logInfo(`Making new request to internal API for conversation ${conversationId}`);

      // Use the internal API endpoint that doesn't require authentication
      const apiPath = `/api/v1.0/internal/chatbot/${this.waitingJobs[conversationId].type}/process-waiting-job`;
      const serverUrl = `http://localhost:${config.port || 3000}`;

      // Make the request with all the necessary data
      axios.post(`${serverUrl}${apiPath}`, {
        userId: jobData.userId,
        conversationId: jobData.conversationId,
        ...requestData
      })
        .then(response => {
          logger.logInfo(`Successfully executed internal API for conversation ${conversationId}`);
        })
        .catch(error => {
          logger.logError([error], 'DocumentWaitingManager.executeJob', { conversationId, requestData });
        });
    } catch (error) {
      logger.logError([error], 'DocumentWaitingManager.executeJob', { conversationId });
    }

    // Remove the job from the waiting list
    this.removeJob(conversationId);
  }

  // Check a specific conversation when a document status changes
  // Check all conversations in memory that contain a specific document
  checkDocumentInConversations(documentId, status) {
    if (!documentId || status !== 'COMPLETED') {
      return;
    }

    const documentIdStr = documentId.toString();
    logger.logInfo(`Checking in-memory conversations containing document ${documentIdStr} with status ${status}`);

    // Get all conversation IDs from waitingJobs
    const conversationIds = Object.keys(this.waitingJobs);
    if (conversationIds.length === 0) {
      logger.logInfo('No waiting jobs found in memory');
      return;
    }

    // Track conversations that contain this document
    let matchingConversations = 0;

    // Check each conversation in memory
    conversationIds.forEach(conversationId => {
      const jobData = this.waitingJobs[conversationId];

      // Skip if no documents or empty documents array
      if (!jobData || !jobData.documents || jobData.documents.length === 0) {
        return;
      }

      // Check if this conversation contains the document
      const hasDocument = jobData.documents.some(doc => {
        const docId = doc._id || doc;
        return docId.toString() === documentIdStr;
      });

      if (hasDocument) {
        matchingConversations++;
        // Update this conversation with the document status
        this.checkConversation(conversationId, documentId, status);
      }
    });

    logger.logInfo(`Found ${matchingConversations} in-memory conversations containing document ${documentIdStr}`);
  }

  // Check a specific conversation when a document status changes
  checkConversation(conversationId, completedDocId = null, documentStatus = null) {
    const jobData = this.waitingJobs[conversationId];

    if (!jobData) {
      // No waiting job for this conversation
      return;
    }

    if (!jobData.documents || jobData.documents.length === 0) {
      // No documents to check, execute the job
      logger.logInfo(`No documents to check for conversation ${conversationId}, executing job immediately, func checkConversation`);
      this.executeJob(conversationId);
      return;
    }

    // If we have a specific document that just completed, update its status
    if (completedDocId) {
      const status = documentStatus || 'COMPLETED'; // Default to COMPLETED if not specified

      for (const doc of jobData.documents) {
        const docId = doc._id || doc;
        if (docId.toString() === completedDocId.toString()) {
          if (typeof doc === 'object') {
            doc.status = status;
          }
        }
      }

      logger.logInfo(`Updated document ${completedDocId} status to ${status} in conversation ${conversationId}`);
    }

    // First check if we already know all documents are completed
    let allCompletedFromCache = true;
    const docsToCheck = [];

    for (const doc of jobData.documents) {
      if (typeof doc === 'object' && doc.status === 'COMPLETED') {
        // This document is already known to be completed, skip checking
        continue;
      } else {
        // Need to check this document
        allCompletedFromCache = false;
        docsToCheck.push(doc);
      }
    }

    // If all documents are already known to be completed, execute job immediately
    if (allCompletedFromCache) {
      logger.logInfo(`All documents already completed for conversation ${conversationId}, executing job immediately, func checkConversation, allCompletedFromCache`);
      this.executeJob(conversationId);
      return;
    }

    // Only check documents that we don't know are completed
    if (docsToCheck.length === 0) {
      // This shouldn't happen, but just in case
      return;
    }

    // Check remaining documents
    async.every(docsToCheck, (doc, callback) => {
      const docId = doc._id || doc; // Handle both object and string/ObjectId
      Document.findOne({ _id: docId }, 'status')
        .lean()
        .exec((err, document) => {
          if (err || !document) {
            return callback(null, false);
          }

          // Update status in our cache
          if (typeof doc === 'object') {
            doc.status = document.status;
          }

          callback(null, document.status === 'COMPLETED');
        });
    }, (err, allCompleted) => {
      if (err) {
        logger.logError([err], 'DocumentWaitingManager.checkConversation', { conversationId });
        return;
      }

      if (allCompleted) {
        // All documents are completed, execute the job
        logger.logInfo(`All documents completed for conversation ${conversationId}, executing job, func checkConversation, allCompleted`);
        this.executeJob(conversationId);
      }
    });
  }
}

module.exports = new DocumentWaitingManager();
