const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const rp = require('request-promise');

// Import Chat models for database status checking
const Chat = require('../models/chat');
const ChatMakeDecision = require('../models/chatMakeDecision');
const ChatSpeech = require('../models/chatSpeech');
const ChatITT = require('../models/chatITT');
const ChatTextToSpeech = require('../models/chatTextToSpeech');
const ChatTestimony = require('../models/chatTestimony');
const ChatWriteSpeechWithDoc = require('../models/chatWriteSpeechWithDoc');
const ChatVideoSummary = require('../models/chatVideoSummary');
const ChatDocumentSummary = require('../models/chatDocumentSummary');
const ChatPlanning = require('../models/chatPlanning');
const ChatSpellCheck = require('../models/chatSpellCheck');
const ChatSocioEconomic = require('../models/chatSocioEconomic');
const ChatWithDocument = require('../models/chatWithDocument');
const ChatManageDocument = require('../models/chatManageDocument');
const ChatReport = require('../models/chatReport');
const ChatWriteReport = require('../models/chatWriteReport')
const ChatMeetingSummary = require('../models/chatMeetingSummary');
const ChatMeetingSynthesis = require('../models/chatMeetingSynthesis');
const ChatVideoExcerpt = require('../models/chatVideoExcerpt');
const ChatWriteScript = require('../models/chatWriteScript');
const ChatWriteArticle = require('../models/chatWriteArticle');
const ChatSummaryManageDocument = require('../models/chatSummaryManageDocument');
const ChatWriteReportManageDocument = require('../models/chatWriteReportManageDocument');
const ChatSpeechManageDocument = require('../models/chatSpeechManageDocument');
const Conversation = require('../models/conversation');
const ConversationMakeDecision = require('../models/conversationMakeDecision');
const ConversationSpeech = require('../models/conversationSpeech');
const ConversationITT = require('../models/conversationITT');
const ConversationTextToSpeech = require('../models/conversationTextToSpeech');
const ConversationTestimony = require('../models/conversationTestimony');
const ConversationWriteSpeechWithDoc = require('../models/conversationWriteSpeechWithDoc');
const ConversationVideoSummary = require('../models/conversationVideoSummary');
const ConversationDocumentSummary = require('../models/conversationDocumentSummary');
const ConversationPlanning = require('../models/conversationPlanning');
const ConversationSpellCheck = require('../models/conversationSpellCheck');
const ConversationSocioEconomic = require('../models/conversationSocioEconomic');
const ConversationChatWithDocument = require('../models/conversationChatWithDocument');
const ConversationManageDocument = require('../models/conversationManageDocument');
const ConversationReport = require('../models/conversationReport');
const ConversationWriteReport = require('../models/conversationWriteReport')
const ConversationMeetingSummary = require('../models/conversationMeetingSummary');
const ConversationMeetingSynthesis = require('../models/conversationMeetingSynthesis');
const ConversationVideoExcerpt = require('../models/conversationVideoExcerpt');
const ConversationWriteScript = require('../models/conversationWriteScript');
const ConversationWriteArticle = require('../models/conversationWriteArticle');
const ConversationSummaryManageDocument = require('../models/conversationSummaryManageDocument');
const ConversationWriteReportManageDocument = require('../models/conversationWriteReportManageDocument');
const ConversationSpeechManageDocument = require('../models/conversationSpeechManageDocument');

// Map for chat models
const CHAT_MODELS = {
  'chat': Chat,
  'makeDecision': ChatMakeDecision,
  'speech': ChatSpeech,
  'imageToText': ChatITT,
  'textToSpeech': ChatTextToSpeech,
  'testimony': ChatTestimony,
  'writeSpeechWithDocument': ChatWriteSpeechWithDoc,
  'videoSummary': ChatVideoSummary,
  'documentSummary': ChatDocumentSummary,
  'planning': ChatPlanning,
  'spellCheck': ChatSpellCheck,
  'socioEconomic': ChatSocioEconomic,
  'chatWithDocument': ChatWithDocument,
  'manageDocument': ChatManageDocument,
  'report': ChatReport,
  'writeReport': ChatWriteReport,
  'meetingSummary': ChatMeetingSummary,
  'meetingSynthesis': ChatMeetingSynthesis,
  'videoExcerpt': ChatVideoExcerpt,
  'writeScript': ChatWriteScript,
  'writeArticle': ChatWriteArticle,
  'summaryManageDocument': ChatSummaryManageDocument,
  'writeReportManageDocument': ChatWriteReportManageDocument,
  'speechManageDocument': ChatSpeechManageDocument
};

const CONVERSATION_MODELS = {
  'chat': Conversation,
  'makeDecision': ConversationMakeDecision,
  'speech': ConversationSpeech,
  'imageToText': ConversationITT,
  'textToSpeech': ConversationTextToSpeech,
  'testimony': ConversationTestimony,
  'writeSpeechWithDocument': ConversationWriteSpeechWithDoc,
  'videoSummary': ConversationVideoSummary,
  'documentSummary': ConversationDocumentSummary,
  'planning': ConversationPlanning,
  'spellCheck': ConversationSpellCheck,
  'socioEconomic': ConversationSocioEconomic,
  'chatWithDocument': ConversationChatWithDocument,
  'manageDocument': ConversationManageDocument,
  'report': ConversationReport,
  'writeReport': ConversationWriteReport,
  'meetingSummary': ConversationMeetingSummary,
  'meetingSynthesis': ConversationMeetingSynthesis,
  'videoExcerpt': ConversationVideoExcerpt,
  'writeScript': ConversationWriteScript,
  'writeArticle': ConversationWriteArticle,
  'summaryManageDocument': ConversationSummaryManageDocument,
  'writeReportManageDocument': ConversationWriteReportManageDocument,
  'speechManageDocument': ConversationSpeechManageDocument
};

class ChatBotManager {
  constructor() {
    this.listChat = []
    this.listResponse = {}
    this.bodyData = {}
  }

  answering(id, bodyData) {
    id = id.toString()
    if (!this.listChat.includes(id)) {
      this.listChat.push(id)
    }

    if (bodyData) {
      this.bodyData[id] = bodyData;
    }
  }

  /**
   * Set document status for a conversation
   * @param {string} id - Conversation ID
   * @param {string} status - Document status (e.g., 'PROCESSING', 'COMPLETED')
   */
  setDocumentStatus(id, status) {
    id = id.toString();

    if (!this.bodyData[id]) {
      this.bodyData[id] = {};
    }

    this.bodyData[id].documentStatus = status;
  }

  isStreaming(id) {
    id = id.toString()
    return this.listChat.includes(id)
  }

  stop(id, isUserStopped = false) {
    id = id.toString()
    const index = this.listChat.indexOf(id);
    const modelType = this.bodyData[id] && this.bodyData[id].modelType;
    if (modelType && !this.listResponse[id] && isUserStopped) {
      const ChatModel = CHAT_MODELS[modelType];
      const ConversationModel = CONVERSATION_MODELS[modelType];
      const userId = this.bodyData[id] && this.bodyData[id].member;

      if (ChatModel) {
        ChatModel.create(
          {
            conversation: id,
            // question: text,
            answer: '',
            error: new Error('user stopped'),
            createdAt: Date.now(),
            ...this.bodyData[id]
          },
          (err, result) => {
            PushNotifyManager.sendViaSocket(userId, 'conversation_update', { data: { link: '', extras: { id } } }, []);

            if (ConversationModel) {
              ConversationModel.update(
                {
                  _id: id,
                },
                {
                  hasMessage: true,
                  updatedAt: Date.now(),
                },
                () => { }
              );
            }
          }
        );
      }
    }

    if (index !== -1) {
      this.listChat.splice(index, 1);
      delete this.listResponse[id];
      delete this.bodyData[id];
    }
  }

  saveAnswer(id, response = '') {
    id = id.toString();

    if (this.listChat.includes(id)) {
      this.listResponse[id] = response;
    }
  }

  /**
   * Check database for chat status by conversation ID
   * @param {string} conversationId - Conversation ID
   * @returns {Promise<Object|null>} - Promise resolving to chat object or null
   */
  checkChatStatusInDatabase(conversationId, modelType) {
    if (!modelType && this.bodyData[conversationId] && this.bodyData[conversationId].modelType) {
      modelType = this.bodyData[conversationId].modelType;
    }

    return new Promise((resolve, reject) => {
      conversationId = conversationId.toString();

      // Check if we know which chat model to use
      if (modelType) {
        const ChatModel = CHAT_MODELS[modelType];

        if (ChatModel) {
          // We know which model to check, so query only that one
          console.log(`Checking status for conversation ${conversationId} in ${modelType} model`);

          ChatModel.findOne({ conversation: conversationId }, 'error')
            .sort('-createdAt')
            .lean()
            .exec((err, chat) => {
              if (err) {
                console.error(`Error checking chat status in ${modelType} model:`, err);
                return resolve(null);
              }

              return resolve(chat);
            });

          return;
        }
      }

      // If we don't know the model type or it's not valid, just return null
      console.log(`No specific model type for conversation ${conversationId}, using default status`);
      return resolve(null);
    });
  }

  /**
   * Get answer status for a conversation
   * If the conversation is not in memory, check the database for status
   * @param {string} id - Conversation ID
   * @returns {Object} - Status object
   */
  async getAnswer(id, modelType) {
    id = id.toString();

    let status = this.listResponse[id] ? 'PROCESSING' : 'PENDING';

    // If conversation is in memory, use in-memory status
    if (this.listChat.includes(id)) {
      // Check if we have document processing status in bodyData
      if (this.bodyData[id] && this.bodyData[id].documentStatus === 'PROCESSING') {
        status = 'PROCESSING_DOCUMENT';
      }
    } else {
      // If conversation is not in memory, check database if we know the model type
      status = 'COMPLETED'; // Default status

      if (modelType) {
        try {
          // Check database for status using await to wait for the result
          const chat = await this.checkChatStatusInDatabase(id, modelType);

          if (chat && chat.error) {
            // If we find a chat with an error, update the status to FAILED
            console.log(`Found error for conversation ${id}, setting status to FAILED`);
            status = 'FAILED';

            // Update status in memory for future calls
            if (!this.bodyData[id]) {
              this.bodyData[id] = {};
            }
            this.bodyData[id].status = 'FAILED';
          }
        } catch (err) {
          console.error(`Error checking status for ${id}:`, err);
          // Use default COMPLETED status on error
        }
      }
    }

    return {
      answer: this.listResponse[id] || '',
      isStreaming: this.listChat.includes(id),
      status,
      bodyData: this.bodyData[id] || null
    }
  }
}

module.exports = new ChatBotManager;
