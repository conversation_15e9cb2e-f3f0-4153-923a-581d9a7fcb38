const _ = require('lodash');
const async = require('async');
const config = require('config');
const ms = require('ms');
const schedule = require('node-schedule');
const axios = require('axios');
const Document = require('../models/document');
const DocumentWaitingManager = require('./documentWaitingManager');
const ChatBotManager = require('./chatStreamManager');
const PushNotifyManager = require('./pushNotify');
const UserNotifyModel = require('../models/userNotify');

/**
 * DocumentStatusChecker
 *
 * This job manages checking the status of documents that are in PROCESSING state.
 * It adds a job for each document to check its status every minute until it's completed.
 * When a document is completed, the job is removed from the queue.
 */
class DocumentStatusChecker {
  constructor() {
    this.checkInterval = '*/1 * * * *'; // Run every minute
    this.serverChatbot = config.proxyRequestServer.serverChatBot;
    this.documentJobs = new Map(); // Map to store document jobs: documentId -> job

    this.init();
  }

  init() {
    // Load pending documents from the last 24 hours at startup
    this.loadPendingDocuments();

    // Schedule the main job to run every minute
    schedule.scheduleJob(this.checkInterval, this.processDocumentJobs);

    console.log('DocumentStatusChecker initialized');
  }

  /**
   * Load pending documents from the last 24 hours from the database at startup
   */
  loadPendingDocuments = async () => {
    try {
      console.log('Loading pending documents from the last 24 hours at startup...');

      // Calculate timestamp for 24 hours ago
      const twentyFourHoursAgo = Date.now() - (24 * 60 * 60 * 1000);

      // Find documents that are in PROCESSING state and created within the last 24 hours
      Document.find({
        status: 'PROCESSING',
        createdAt: { $gte: twentyFourHoursAgo }
      })
      .lean()
      .exec((err, documents) => {
        if (err) {
          console.error('DocumentStatusChecker.loadPendingDocuments:', err);
          return;
        }

        if (!documents || documents.length === 0) {
          console.log('No pending documents found from the last 24 hours at startup');
          return;
        }

        console.log(`Found ${documents.length} pending documents from the last 24 hours, adding to check queue...`);

        // Add each document to the check queue
        documents.forEach(document => {
          this.addDocumentJob(document._id);
        });
      });
    } catch (error) {
      console.error('DocumentStatusChecker.loadPendingDocuments:', error);
    }
  }

  /**
   * Add a document to the check queue
   * @param {String} documentId - The document ID to check
   */
  addDocumentJob = (documentId) => {
    if (!documentId) {
      console.error('DocumentStatusChecker.addDocumentJob: Invalid document ID');
      return;
    }

    // Check if job already exists for this document
    if (this.documentJobs.has(documentId)) {
      console.log(`Document ${documentId} is already in the check queue`);
      return;
    }

    console.log(`Adding document ${documentId} to the check queue`);

    // Add to the map of documents to check
    this.documentJobs.set(documentId, {
      id: documentId,
      lastChecked: 0
    });
  }

  /**
   * Remove a document from the check queue
   * @param {String} documentId - The document ID to remove
   */
  removeDocumentJob = (documentId) => {
    if (!documentId) {
      return;
    }

    if (this.documentJobs.has(documentId)) {
      console.log(`Removing document ${documentId} from the check queue`);
      this.documentJobs.delete(documentId);
    }
  }

  /**
   * Process all document jobs in the queue
   */
  processDocumentJobs = async () => {
    try {
      const documentCount = this.documentJobs.size;

      if (documentCount === 0) {
        return;
      }

      console.log(`Processing ${documentCount} documents in the check queue`);

      // Create an array of document IDs to check
      const documentIds = Array.from(this.documentJobs.keys());

      // Process each document
      async.eachSeries(documentIds, this.processDocumentJob, (err) => {
        if (err) {
          console.error('DocumentStatusChecker.processDocumentJobs.eachSeries:', err);
        }
      });
    } catch (error) {
      console.error('DocumentStatusChecker.processDocumentJobs:', error);
    }
  }

  /**
   * Process a single document job
   * @param {String} documentId - The document ID to process
   * @param {Function} callback - Callback function
   */
  processDocumentJob = (documentId, callback) => {
    try {
      // Get the document from the database
      Document.findById(documentId)
        .lean()
        .exec((err, document) => {
          if (err) {
            console.error('DocumentStatusChecker.processDocumentJob:', err, { documentId });
            return callback();
          }

          if (!document) {
            console.error('DocumentStatusChecker.processDocumentJob: Document not found', { documentId });
            this.removeDocumentJob(documentId);
            return callback();
          }

          // If document is already completed or failed, remove it from the queue
          if (document.status !== 'PROCESSING') {
            console.log(`Document ${documentId} is already ${document.status}, removing from check queue`);
            this.removeDocumentJob(documentId);
            return callback();
          }

          // Check if document has been in PROCESSING status for 24+ hours
          const twentyFourHoursAgo = Date.now() - (24 * 60 * 60 * 1000);
          if (document.createdAt <= twentyFourHoursAgo) {
            console.log(`Document ${documentId} has been in PROCESSING status for 24+ hours, marking as FAILED`);

            // Update document status to FAILED
            Document.findByIdAndUpdate(
              documentId,
              { status: 'FAILED' },
              { new: true },
              (err, updatedDocument) => {
                if (err) {
                  console.error('DocumentStatusChecker.processDocumentJob.updateToFailed:', err, { documentId });
                  return callback();
                }

                if (!updatedDocument) {
                  console.error('DocumentStatusChecker.processDocumentJob: Document not found when trying to mark as FAILED', { documentId });
                  return callback();
                }

                console.log(`Successfully marked document ${documentId} as FAILED due to 24+ hours in PROCESSING status`);

                // Remove document from the check queue
                this.removeDocumentJob(documentId.toString());

                // Notify DocumentWaitingManager
                try {
                  DocumentWaitingManager.checkDocumentInConversations(documentId, 'FAILED');
                  console.log(`Notified DocumentWaitingManager about document ${documentId} failure due to timeout`);
                } catch (notifyError) {
                  console.error('Failed to notify DocumentWaitingManager:', notifyError);
                }

                // Notify user about document failure
                try {
                  if (updatedDocument.member) {
                    const notification = {
                      title: 'Thông báo',
                      message: `Tài liệu ${updatedDocument.name || ''} không thể xử lý được.`,
                      data: {
                        link: '/manage-document',
                        extras: {
                          id: updatedDocument._id
                        }
                      }
                    };

                    // Send push notification
                    PushNotifyManager.sendToMemberAssistant(
                      updatedDocument.member.toString(),
                      notification.title,
                      notification.message,
                      notification.data,
                      'document_update'
                    );

                    // Create notification in database
                    UserNotifyModel.create({
                      user: updatedDocument.member,
                      title: notification.title || '',
                      message: notification.message || '',
                      data: notification.data || {},
                    });

                    console.log(`Sent notification to user ${updatedDocument.member} about document ${documentId} failure due to timeout`);
                  }
                } catch (notifyError) {
                  console.error('DocumentStatusChecker.processDocumentJob.notifyUser:', notifyError);
                }

                return callback();
              }
            );
          } else {
            // Document is still within the 24-hour window, check its status
            this.checkDocumentStatus(document, callback);
          }
        });
    } catch (error) {
      console.error('DocumentStatusChecker.processDocumentJob:', error, { documentId });
      callback();
    }
  }

  /**
   * Check the status of a specific document by calling the API
   * @param {Object} document - The document to check
   * @param {Function} callback - Callback function
   */
  checkDocumentStatus = (document, callback) => {
    const docId = document._id || document.id;

    if (!docId) {
      console.error('DocumentStatusChecker.checkDocumentStatus: Missing document ID', { document });
      return callback();
    }

    // Update last checked time
    const job = this.documentJobs.get(docId.toString());
    if (job) {
      job.lastChecked = Date.now();
    }

    console.log(`Checking status for document ${docId}`);

    try {
      axios.get(`${this.serverChatbot}/api/2.0.0/documents/status/${document.id}`)
        .then((response) => {
          if (!response || !response.data || !response.data.data) {
            console.error('DocumentStatusChecker.checkDocumentStatus: Invalid response from document status API', { docId });
            return callback();
          }

          const apiStatus = response.data.data.status;
          let normalizedStatus = apiStatus;

          // Normalize status
          if (apiStatus === 'PENDING' || apiStatus === 'CHUNKED') {
            normalizedStatus = 'PROCESSING';
          }

          // If status has changed, update the document
          if (normalizedStatus !== document.status) {
            this.updateDocumentStatus(docId, normalizedStatus, response.data.data, callback);
          } else {
            console.log(`Document ${docId} status unchanged: ${normalizedStatus}`);
            callback();
          }
        })
        .catch((error) => {
          console.error('DocumentStatusChecker.checkDocumentStatus.apiCall:', error);

          // Nếu API trả về lỗi 404, cập nhật trạng thái thành FAILED
          if (error.response && error.response.status === 404) {
            console.log(`Document ${docId} not found on server (404), marking as FAILED`);

            // Cập nhật trạng thái document thành FAILED
            Document.findByIdAndUpdate(
              docId,
              { status: 'FAILED' },
              { new: true },
              (err, updatedDocument) => {
                if (err) {
                  console.error('Failed to update document status to FAILED:', err);
                  return callback();
                }

                if (!updatedDocument) {
                  console.error('Document not found when trying to mark as FAILED:', docId);
                  return callback();
                }

                console.log(`Successfully marked document ${docId} as FAILED due to 404 error`);

                // Xóa document khỏi hàng đợi kiểm tra
                this.removeDocumentJob(docId.toString());

                // Thông báo cho DocumentWaitingManager
                try {
                  DocumentWaitingManager.checkDocumentInConversations(docId, 'FAILED');
                  console.log(`Notified DocumentWaitingManager about document ${docId} failure`);
                } catch (notifyError) {
                  console.error('Failed to notify DocumentWaitingManager:', notifyError);
                }

                // Notify user about document failure
                try {
                  if (updatedDocument.member) {
                    const notification = {
                      title: 'Thông báo',
                      message: `Tài liệu ${updatedDocument.name || ''} không thể xử lý được.`,
                      data: {
                        link: '/manage-document',
                        extras: {
                          id: updatedDocument._id
                        }
                      }
                    };

                    // Send push notification
                    PushNotifyManager.sendToMemberAssistant(
                      updatedDocument.member.toString(),
                      notification.title,
                      notification.message,
                      notification.data,
                      'document_update'
                    );

                    // Create notification in database
                    UserNotifyModel.create({
                      user: updatedDocument.member,
                      title: notification.title || '',
                      message: notification.message || '',
                      data: notification.data || {},
                    });

                    console.log(`Sent notification to user ${updatedDocument.member} about document ${docId} failure due to 404 error`);
                  }
                } catch (notifyError) {
                  console.error('DocumentStatusChecker.checkDocumentStatus.notifyUser:', notifyError);
                }

                callback();
              }
            );
          } else {
            // Các lỗi khác, tiếp tục kiểm tra sau
            callback();
          }
        });
    } catch (error) {
      console.error('DocumentStatusChecker.checkDocumentStatus:', error, { docId });
      callback();
    }
  }

  /**
   * Update the document status in the database
   * @param {String} documentId - The document ID
   * @param {String} status - The new status
   * @param {Object} apiData - The data from the API
   * @param {Function} callback - Callback function
   */
  updateDocumentStatus = (documentId, status, apiData, callback) => {
    console.log(`Updating document ${documentId} status to ${status}`);

    const updateData = {
      status,
      docCategory: apiData.doc_category || '',
      summary: apiData.doc_summary || '',
      title: apiData.doc_title || '',
      subtitle: apiData.subtitle || '',
      highlight: apiData.highlight || '',
      aiName: apiData.name || '',
    };

    Document.findByIdAndUpdate(
      documentId,
      updateData,
      { new: true },
      (err, updatedDocument) => {
        if (err) {
          console.error('DocumentStatusChecker.updateDocumentStatus:', err, { documentId, status });
          return callback();
        }

        if (!updatedDocument) {
          console.error('DocumentStatusChecker.updateDocumentStatus: Document not found', { documentId });
          return callback();
        }

        console.log(`Successfully updated document ${documentId} status to ${status}`);

        // If document is completed or failed, remove it from the check queue
        if (status !== 'PROCESSING') {
          this.removeDocumentJob(documentId.toString());

          // Notify DocumentWaitingManager about the status change
          try {
            DocumentWaitingManager.checkDocumentInConversations(documentId, status);
            console.log(`Notified DocumentWaitingManager about document ${documentId} status change to ${status}`);
          } catch (error) {
            console.error('DocumentStatusChecker.updateDocumentStatus.notifyManager:', error);
          }

          // Notify user about document status change
          try {
            // Create appropriate notification message based on status
            const notificationTitle = 'Thông báo';
            let notificationMessage = '';

            if (status === 'COMPLETED') {
              notificationMessage = `Tài liệu ${updatedDocument.name || ''} đã được xử lý xong.`;
            } else {
              notificationMessage = `Tài liệu ${updatedDocument.name || ''} không thể xử lý được.`;
            }

            // Only send notification if we have a message and a member ID
            if (notificationMessage && updatedDocument.member) {
              const notification = {
                title: notificationTitle,
                message: notificationMessage,
                data: {
                  link: '/manage-document',
                  extras: {
                    id: updatedDocument._id
                  }
                }
              };

              // Send push notification
              PushNotifyManager.sendToMemberAssistant(
                updatedDocument.member.toString(),
                notification.title,
                notification.message,
                notification.data,
                'document_update'
              );

              // Create notification in database
              UserNotifyModel.create({
                user: updatedDocument.member,
                title: notification.title || '',
                message: notification.message || '',
                data: notification.data || {},
              });

              console.log(`Sent notification to user ${updatedDocument.member} about document ${documentId} status change to ${status}`);
            }
          } catch (notifyError) {
            console.error('DocumentStatusChecker.updateDocumentStatus.notifyUser:', notifyError);
          }
        }

        callback();
      }
    );
  }
}

// Create a singleton instance
const instance = new DocumentStatusChecker();

// Export the instance
module.exports = instance;
