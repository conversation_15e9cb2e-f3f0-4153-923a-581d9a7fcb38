const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const CategoryModel = require('../models/category')
const PushNotifyManager = require('./pushNotify');
const PetitionModel = require('../models/petition')
const config = require('config')
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const rp = require('request-promise');

class ProcessingManager {
  constructor() {
  }

  addPetition(id, content, place) {

    const userId = uuidv4()
    const serverChatbot = config.proxyRequestServer.serverChatBot;
    let response;
    let conversationId;

    const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: userId
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id) {
            conversationId = result.conversation_id
            ask();
            next({
              code: 200
            });
          } else {
            return next({
              code: 400
            })
          }
        })
        .catch((error) => {
          return next({
            code: 400,
            error: error
          })
        })
  }

  const  ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api-v2/chat_stream`,
        responseType: 'stream',
        data: {
          "conversation_id": conversationId,
          text: `Tôi là nhà quản trị ứng dụng có chức năng để người dân gửi phản ánh kiến nghị. Người dân đã gửi phản ánh với nội dung: ${content}. Phản ánh xảy ra tại Địa điểm ${place}. Tôi muốn hướng dẫn người dân về: hướng giải quyết, cơ quan phòng ban sẽ tiếp nhận, tiến trình và thời gian giải quyết(Không quá 7 ngày) của phản ánh nếu được tiếp nhận. Nếu phản ánh có thể tự xử lý mà không cần gửi lên cơ quan chức năng thì hãy đưa ra lời khuyên chi tiết cho người dân trên tinh thần xử lý nhẹ nhàng, hòa giải, thượng tôn pháp luật là trên hết. Hãy trả về cho tôi theo mẫu sau:
Trong khi đợi Phản Ánh Kiến Nghị được cơ quan chức năng giải quyết và gửi kết quả trả lời đến bạn.

***PAKN của bạn có thể được gửi đến các Đơn vị liên quan như sau:***
    - Đơn vị 1
    - Đơn vị 2
***Thời gian xử lý PAKN này:***

***Lời khuyên của AI Hải Phòng dành cho bạn:***
(nếu dài trình bày theo danh sách)
          `
        },
        timeout:300000
      });

      const stream = resAxios.data;
      resAxios.data.on('data',(chunk) => {
        try {
          const data = JSON.parse(chunk.toString());
          if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;
          }
        } catch (e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if(response) {
          updatePetition()
        }
      });
      stream.on('error', (err) => {
      });
    } catch (err) {
      console.error(err);
    }
  }

  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
  }

    const updatePetition = () => {
      PetitionModel
        .findOneAndUpdate({
          _id: id
        },{
          processingContent: response
        },(err, petition) => {
            PushNotifyManager
              .sendToMember(petition.member.toString(), '', ``, {link: 'MyPetitionDetailScreen', extras: {id}}, 'petition_update','')
        })
    }

    async.waterfall([
      createConversation
    ], (err, data) => {
    })
  }

}

module.exports = new ProcessingManager;
