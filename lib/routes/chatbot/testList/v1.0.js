const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Chat = require('../../../models/chat');

module.exports = (req, res) => {
  const page = req.body.page || 0;
  const limit = req.body.limit || 10;
  const category = req.body.category;
  const question = req.body.question;
  const sortBy = req.body.sortBy || '-updatedAt';

  let count = 0;
  let query = {
    status: 1,
  };
  const checkParams = (next) => {
    if (question) {
      query.question = { $regex: question, $options: 'gi' };
    }
    if (category) {
      query.category = category
    }
    next();
  };

  const countTotal = (next) => {
    ChatTesingModel.count(query).exec((err, total) => {
      if (err) {
        return next(err);
      }
      count = Math.ceil(total / limit);
      next(null);
    });
  };

  const listTest = (next) => {
    ChatTesingModel.find(query)
      .sort(sortBy)
      .skip(page * limit)
      .limit(limit)
      .populate('category')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, countTotal, listTest], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
