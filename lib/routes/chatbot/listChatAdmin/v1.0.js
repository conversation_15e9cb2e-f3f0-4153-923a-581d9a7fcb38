const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const rp = require('request-promise');

module.exports = (req, res) => {
  const from = _.toSafeInteger(req.body.from) || Date.now() + 100000;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 100, 100);
  const model = _.get(req, 'body.model', '');
  const conversation = req.body.conversation;
  let Chat = ChatModel;
  try {
    switch (model) {
      case 'SocioEconomic':
        Chat = ChatSocioEconomicModel;
        break;
      case 'ChatWithDocument':
        Chat = ChatWithDocumentModel;
        break;
      case 'DocumentSummary':
        Chat = ChatDocumentSummaryModel;
        break;
      case 'Email':
        Chat = ChatEmailModel;
        break;
      case 'Evaluation':
        Chat = ChatEvaluationModel;
        break;
      case 'ITT':
        Chat = ChatIttModel;
        break;
      case 'MakeDecision':
        Chat = ChatMakeDecisionModel;
        break;
      case 'ManageDocument':
        Chat = ChatManageDocumentModel;
        break;
      case 'NewspaperAnalysis':
        Chat = ChatNewspaperAnalysisModel;
        break;
      case 'Planning':
        Chat = ChatPlanningModel;
        break;
      case 'ReadNewspaper':
        Chat = ChatReadNewspaperModel;
        break;
      case 'Report':
        Chat = ChatReportModel;
        break;
      case 'SocioEconomic':
        Chat = ChatSocioEconomicModel;
        break;
      case 'Speech':
        Chat = ChatSpeechModel;
        break;
      case 'SpellCheck':
        Chat = ChatSpellCheckModel;
        break;
      case 'SummaryManageDocument':
        Chat = ChatSummaryManageDocumentModel;
        break;
      case 'VideoSummary':
        Chat = ChatVideoSummaryModel;
        break;
      case 'WorkSchedule':
        Chat = ChatWorkScheduleModel;
        break;
      case 'WriteReport':
        Chat = ChatWriteReportModel;
        break;
      case 'WriteReportWithDoc':
        Chat = ChatWriteReportWithDocModel;
        break;
      case 'WriteSpeechWithDoc':
        Chat = ChatWriteSpeechWithDocModel;
        break;
      default:
        break;
    }
  } catch (error) {
    console.log('Error: ', error);
  }
  Chat.find({
    conversation: conversation,
  })
    .sort('-createdAt')
    .limit(limit)
    .lean()
    .exec((err, results) => {
      if (err) {
        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });
      }

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: results,
      });
    });
};
