const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Chat = require('../../../models/chat');

module.exports = (req, res) => {
  const group = req.body.group;
  let groupInf = {};
  let list = [];

  const checkParams = (next) => {
    if (!group) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const getGroupTest = (next) => {
    ChatTesingGroupModel.findOne({ _id: group })
      .populate('questions.question')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(
            err || {
              code: CONSTANTS.CODE.WRONG_PARAMS,
            }
          );
        }
        groupInf = result;
        next();
      });
  };

  const getLastChat = (next) => {
    async.each(
      groupInf.questions,
      (question, callback) => {
        if (!question.conversation) {
          return callback();
        }
        ChatTesingResultModel.findOne({ conversation: question.conversation })
          .sort('-createdAt')
          .lean()
          .exec((err, message) => {
            if (err) {
              return callback(err);
            }
            question.testResult = message;
            callback();
          });
      },
      (err) => {
        if (err) {
          return next(err);
        }
        let returnData = {
          code: CONSTANTS.CODE.SUCCESS,
          data: groupInf,
        };
        next(returnData);
      }
    );
  };

  async.waterfall([checkParams, getGroupTest, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
