const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Conversation = require('../../../models/conversation');
const Chat = require('../../../models/chat');

module.exports = (req, res) => {
  const deviceId = req.body.deviceId;
  const futureTimestamp = Date.now() + 100000;
  const from = _.toSafeInteger(req.body.from) || futureTimestamp;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);

  let conversations = [];

  const checkParams = (next) => {
    if (!deviceId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const listConversation = (next) => {
    let query = {
      member: deviceId,
      updatedAt: {
        $lt: from,
      },
      inactive: {
        $ne: true,
      },
    };
    Conversation.find(query)
      .sort('-updatedAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        conversations = results;

        next();
      });
  };

  const getLastChat = (next) => {
    async.each(
      conversations,
      (conversation, callback) => {
        Chat.findOne({ conversation: conversation._id })
          .sort('-createdAt')
          .lean()
          .exec((err, message) => {
            if (err) {
              return callback(err);
            }

            if (!message && (from !== futureTimestamp || !_.isEqual(conversation, conversations[0]))) {
              _.remove(conversations, { _id: conversation._id });

              return callback();
            }

            if (!message) {
              conversation.newConversation = 1;
            }

            conversation.lastMessage = message && message.question ? message.question : 'Cuộc trò chuyện mới';
            callback();
          });
      },
      (err) => {
        if (err) {
          return next(err);
        }
        let returnData = {
          code: CONSTANTS.CODE.SUCCESS,
          data: conversations,
        };
        if (_.isEmpty(conversations) && req.body.from) {
          returnData.message = {
            head: 'Thông báo',
            body: 'Không còn cuộc trò chuyện nào',
          };
        }
        next(returnData);
      }
    );
  };

  async.waterfall([checkParams, listConversation, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
