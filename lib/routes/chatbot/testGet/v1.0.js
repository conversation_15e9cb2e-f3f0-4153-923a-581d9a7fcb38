const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Chat = require('../../../models/chat');

module.exports = (req, res) => {
  const _id = req.body._id;

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const get = (next) => {
    ChatTesingModel.findOne({_id})
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result,
        });
      });
  };

  async.waterfall([checkParams, get], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
