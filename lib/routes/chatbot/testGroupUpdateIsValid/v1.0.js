const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

module.exports = (req, res) => {
  const group = _.get(req, 'body.group');
  const question = _.get(req, 'body.question');
  const isValid = _.get(req, 'body.isValid');
  const userId = req.user.id;

  const checkParams = (next) => {
    if (!question || !group || typeof isValid !== 'boolean') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const update = (next) => {
    let query = {
      _id: group,
      'questions.question': question,
    };
    ChatTesingGroupModel.findOneAndUpdate(query, { $set: { 'questions.$.isValid': isValid } }, { new: true })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result,
        });
      });
  };

  async.waterfall([checkParams, update], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
