const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')

module.exports = (req, res) => {

  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let text = _.get(req,'body.text','');
  let response
  const messageError = MESSAGES.AI.MESSAGE_ERROR

  const checkParams = (next) => {
    if(!deviceId || !serverChatbot || !conversationId){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const checkConversationExists = (next) => {
    Conversation
    .findOne({
      _id: conversationId
    })
    .lean()
    .exec((err, data) => {
      if(err) {
        return next(err);
      }
      if(!data) {
        return next({
          code: 400
        })
      }
      conversation = data
      next();
    })
  }
  const ask = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/chat`,
        body: {
          "conversation_id": conversation.id,
          text
        },
        timeout: 25000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.conversation_id && result.response) {
            response = result.response
            next();
          } else {
            response = messageError
            next();
          }
        })
        .catch((error) => {
          response = messageError
          next();
        })
  }

  const createCollection = (next) => {
    Chat
    .create({
      member: deviceId,
      id: conversationId,
      conversation: conversation._id,
      question: text,
      answer: response
    },(err,result) => {
      return next({
        code: 200,
        data: result
      })
    })
  }


  async.waterfall([
    checkParams,
    checkConversationExists,
    ask,
    createCollection
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
