const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const axios = require('axios');


module.exports = (req, res) => {

  const conversationId = req.body.id;
  const deviceId = req.body.deviceId;

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let text = _.get(req, 'body.text', '');
  let response
  const createdAt = Date.now()
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR
  let error = null;
  const checkParams = (next) => {
    if (!deviceId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const checkConversationExists = (next) => {
    Conversation
      .findOne({
        _id: conversationId
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        if (!data) {
          return next({
            code: 400
          })
        }
        conversation = data
        ask()
        next(null, { code: 200 });
      })
  }
  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/chat_stream_for_leader`,
        responseType: 'stream',
        data: {
          "conversation_id": conversation.id,
          text
        },
        timeout:300000
      });

      const stream = resAxios.data;
      let buffer = '';
      let totalSend = ''
      resAxios.data.on('data', (chunk) => {

        const searchingArray = ['Đang tổng hợp thi thức...', 'Đang tìm kiếm thông tin', 'Đang tìm kiếm dữ liệu mở rộng'];
        if(searchingArray.includes(chunk.toString().trim())) {
          io.to(deviceId).emit('searching', { text: chunk.toString() });
        } else {
          response += chunk.toString();
          response = response.replace('undefined', '')
          response = response.replace('```markdown', '')
          response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

          io.to(deviceId).emit('newMessage', {
            _id,
            member: deviceId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: chunk.toString(),
            createdAt
          });
        }
      });
      stream.on('end', () => {
        io.to(deviceId).emit('endMessage', {
          _id,
          member: deviceId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt
        });
        createCollection()
      });
      stream.on('error', (err) => {
        error = err
        response = messageError

        io.to(deviceId).emit('endMessage', {
          _id,
          member: deviceId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt
        });
        createCollection()
      });
    } catch (err) {
      error = err
      response = messageError
      io.to(deviceId).emit('endMessage', {
        _id,
        member: deviceId,
        id: conversationId,
        conversation: conversation._id,
        question: text,
        answer: response,
        createdAt
      });

      createCollection()
    }
  }

  function splitIntoChunks(text, chunkSize) {
    const chunks = [];
    for (let i = 0; i < text.length; i += chunkSize) {
      chunks.push(text.slice(i, i + chunkSize));
    }
    return chunks;
  }
  const createCollection = (next) => {
    Chat
      .create({
        _id,
        member: deviceId,
        id: conversationId,
        conversation: conversation._id,
        question: text,
        answer: response,
        createdAt,
        error
      }, (err, result) => {
         Conversation
          .update({
            _id: conversation._id,
          },{
            updatedAt: Date.now(),
          },() =>{})
      })
  }

  async.waterfall([
    checkParams,
    checkConversationExists,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
