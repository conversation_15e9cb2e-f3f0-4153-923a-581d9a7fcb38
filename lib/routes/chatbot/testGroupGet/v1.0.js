const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Chat = require('../../../models/chat');

const getCategoryForQuestion = (questions, cb) => {
  let categorieId = Array.from(new Set(questions.map(({question}) => question.category)));
  ChatTesingCategoryModel.find({
    _id: {
      $in: categorieId,
    },
  })
    .lean()
    .exec((err, categories) => {
      if (err) {
        return cb(err);
      }
      questions.forEach(({question}) => {
        question.category = categories.find((category) => category._id.toString() === question.category.toString());
      });
      cb(questions);
    });
};

module.exports = (req, res) => {
  const _id = req.body._id;

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const get = (next) => {
    ChatTesingGroupModel.findOne({ _id })
      .populate('questions.question')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        getCategoryForQuestion(result.questions, (questions) => {
          result.questions = questions;
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result,
          });
        });
      });
  };

  async.waterfall([checkParams, get], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
