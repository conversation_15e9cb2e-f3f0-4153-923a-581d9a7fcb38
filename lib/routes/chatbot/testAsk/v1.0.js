const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const AskPublic = require('../../chatbotJson/askStream/v1.2');
const AskPreProduction = require('../../chatbotJson/askStream/v1.1');
const AskStaff = require('../../chatbotJson/askStream/v2.0');
const AskLeader = require('../../chatbotJson/askStream/v2.1');
const CreateConversation = require('../../chatbotJson/createConversation/v1.0');

module.exports = (req, res) => {
  const _id = req.body._id;
  const group = req.body.group;
  const userId = req.user.id;
  let testInf = {};
  let groupInf = {};
  let conversationId;
  let testedAt = Date.now();
  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const getTest = (next) => {
    ChatTesingModel.findOne({ _id })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(
            err || {
              code: CONSTANTS.CODE.WRONG_PARAMS,
            }
          );
        }
        testInf = result;
        next();
      });
  };

  const getGroupTest = (next) => {
    ChatTesingGroupModel.findOne({ _id: group })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(
            err || {
              code: CONSTANTS.CODE.WRONG_PARAMS,
            }
          );
        }
        groupInf = result;
        next();
      });
  };

  const createNewConversation = (next) => {
    CreateConversation(
      {
        ...req,
        body: {
          forceCreate: true,
          id: userId,
        },
      },
      {
        json: (response) => {
          if (response.code !== CONSTANTS.CODE.SUCCESS) {
            return next(response);
          }
          conversationId = response.data;
          next();
        },
      }
    );
  };

  const ask = (next) => {
    let AskTest = AskPublic;
    switch (groupInf.model) {
      case 'staff':
        AskTest = AskStaff;
        break;
      case 'leader':
        AskTest = AskLeader;
        break;
      case 'pre_production':
        AskTest = AskPreProduction;
        break;
    }

    AskTest(
      {
        ...req,
        body: {
          id: conversationId,
          text: testInf.question,
          testId: group,
          ...(groupInf.mode === 'check-answer' ? { expectedCheckAnswer: testInf.expectedCheckAnswer } : {}),
          ...(groupInf.mode === 'normal' ? { expectedAnswer: testInf.expectedAnswer } : {}),
          testMode: groupInf.mode,
          is_testing: groupInf.is_testing,
          disable_search_web: groupInf.disable_search_web,
        },
      },
      {
        json: (response) => {
          next(null, response);
        },
      }
    );

    ChatTesingGroupModel.updateOne(
      { _id: group, 'questions.question': testInf._id },
      {
        $set: {
          testedAt: Date.now(),
          'questions.$.conversation': conversationId,
          'questions.$.isTesting': true,
        }, // Cập nhật conversation
      }
    )
      .lean()
      .exec((err, result) => {});
  };

  async.waterfall([checkParams, getTest, getGroupTest, createNewConversation, ask], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
