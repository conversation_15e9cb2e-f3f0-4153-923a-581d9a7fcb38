const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Chat = require('../../../models/chat');

module.exports = (req, res) => {
  const page = req.body.page || 0;
  const limit = req.body.limit || 10;
  const title = _.get(req, 'body.title');
  const type = _.get(req, 'body.type');
  const mode = _.get(req, 'body.mode');
  const model = _.get(req, 'body.model');
  const is_testing = _.get(req, 'body.is_testing');
  const disable_search_web = _.get(req, 'body.disable_search_web');
  let query = {
    status: 1,
  };
  let count = 0;
  const checkParams = (next) => {
    if (title) {
      query.title = RegExp(title, 'ig');
    }
    if (type) {
      query.type = type;
    }
    if (mode) {
      query.mode = mode;
    }
    if (model) {
      query.model = model;
    }
    if (is_testing) {
      query.is_testing = is_testing;
    }
    if (disable_search_web) {
      query.disable_search_web = disable_search_web;
    }
    next();
  };

  const countTotal = (next) => {
    ChatTesingGroupModel.count(query).exec((err, total) => {
      if (err) {
        return next(err);
      }
      count = Math.ceil(total / limit);
      next(null);
    });
  };

  const getGroupTest = (next) => {
    ChatTesingGroupModel.find(query)
      .sort('-testedAt')
      .skip(page * limit)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err || !results) {
          return next(
            err || {
              code: CONSTANTS.CODE.WRONG_PARAMS,
            }
          );
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count
        });
      });
  };

  async.waterfall([checkParams, countTotal, getGroupTest], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
