const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')

module.exports = (req, res) => {
  
  const audio = req.body.audio;
  const serverChatbot = config.proxyRequestServer.serverChatBot;

  const checkParams = (next) => {
    if(!audio){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  
  const ask = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/speech-to-text-stream`,
        body: {
          "audio_base64": audio
        },
        timeout: 25000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && req.body.location) {
            result = result.replaceAll(",","")
            result = result.replaceAll(" trên ","/")
            result = result.replaceAll(" chên ","/")
            result = result.replaceAll("-","/")
            result = result.replaceAll(" gạch chéo ","/")
            result = result.replaceAll(" gạch tréo ","/")
            result = result.replaceAll(" gạch ","/")
            result = result.replaceAll(" xẹc ","/")
            result = result.replaceAll(" xẹt ","/")
            result = result.replaceAll(" sẹt ","/")
            result = result.replaceAll(" sẹc ","/")
            result = result.replaceAll(" sạc ","/")
          }
          next(null,{
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          });
        })
        .catch((error) => {
          next(error);
        })
  }

  async.waterfall([
    checkParams,
    ask,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
