const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

module.exports = (req, res) => {
  const _id = _.get(req, 'body._id', mongoose.Types.ObjectId());
  const questions = _.get(req, 'body.questions');
  const title = _.get(req, 'body.title');
  const type = _.get(req, 'body.type');
  const mode = _.get(req, 'body.mode', 'normal');
  const model = _.get(req, 'body.model', 'public');
  const is_testing = _.get(req, 'body.is_testing', true);
  const disable_search_web = _.get(req, 'body.disable_search_web', true);
  const userId = req.user.id;

  const checkParams = (next) => {
    if (!questions) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const add = (next) => {
    let query = {
      _id,
    };
    ChatTesingGroupModel.findOneAndUpdate(query, { questions, title, type, mode, model, is_testing, disable_search_web, updatedAt: Date.now() }, { upsert: true, new: true, setDefaultsOnInsert: true })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result,
        });
      });
  };

  async.waterfall([checkParams, add], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
