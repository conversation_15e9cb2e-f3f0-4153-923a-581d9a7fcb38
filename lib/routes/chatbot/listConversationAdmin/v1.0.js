const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const getConversationInfo = (conversation, callback) => {
  let info = {};
  if (!conversation) {
    return callback(info);
  }
  if (mongoose.isValidObjectId(conversation.member)) {
    UserModel.findOne({ _id: conversation.member })
      .select('name phone')
      .lean()
      .exec((err, user) => {
        if (err) {
          return callback();
        }
        if (user) {
          info = user;
          callback(info);
        }
      });
    MemberModel.findOne({ _id: conversation.member })
      .select('name phone')
      .lean()
      .exec((err, member) => {
        if (err) {
          return callback();
        }
        if (member) {
          info = member;
          callback(info);
        }
      });
  } else {
    TrackingActionModel.findOne({ 'otherInf.uniqueId': conversation.member })
      .select('otherInf member')
      .populate('member', 'name phone')
      .lean()
      .exec((err, tracking) => {
        if (err) {
          return callback();
        }
        if (tracking) {
          info = {
            name: _.get(tracking, 'otherInf.platform', '') + ' - ' + _.get(tracking, 'member.name', ''),
            phone: _.get(tracking, 'member.phone', ''),
          };
        }
        callback(info);
      });
  }
};

module.exports = (req, res) => {
  const deviceId = req.body.deviceId;
  const model = _.get(req, 'body.model', 'SocioEconomic');
  const futureTimestamp = Date.now() + 100000;
  const from = _.toSafeInteger(req.body.from) || futureTimestamp;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);

  let conversations = [];
  let Conversation = ConversationModel;
  let Chat = ChatModel;

  const checkParams = (next) => {
    if (!deviceId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    try {
      switch (model) {
        case 'ChatWithDocument':
          Conversation = ConversationChatWithDocumentModel;
          Chat = ChatWithDocumentModel;
          break;
        case 'DocumentSummary':
          Conversation = ConversationDocumentSummaryModel;
          Chat = ChatDocumentSummaryModel;
          break;
        case 'Email':
          Conversation = ConversationEmailModel;
          Chat = ChatEmailModel;
          break;
        case 'Evaluation':
          Conversation = ConversationEvaluationModel;
          Chat = ChatEvaluationModel;
          break;
        case 'ITT':
          Conversation = ConversationIttModel;
          Chat = ChatIttModel;
          break;
        case 'MakeDecision':
          Conversation = ConversationMakeDecisionModel;
          Chat = ChatMakeDecisionModel;
          break;
        // case 'ManageDocument':
        //   Conversation = ConversationManageDocumentModel;
        //   Chat = ChatManageDocumentModel;
        //   break;
        case 'NewspaperAnalysis':
          Conversation = ConversationNewspaperAnalysisModel;
          Chat = ChatNewspaperAnalysisModel;
          break;
        case 'Planning':
          Conversation = ConversationPlanningModel;
          Chat = ChatPlanningModel;
          break;
        case 'ReadNewspaper':
          Conversation = ConversationReadNewspaperModel;
          Chat = ChatReadNewspaperModel;
          break;
        case 'Report':
          Conversation = ConversationReportModel;
          Chat = ChatReportModel;
          break;
        case 'SocioEconomic':
          Conversation = ConversationSocioEconomicModel;
          Chat = ChatSocioEconomicModel;
          break;
        case 'Speech':
          Conversation = ConversationSpeechModel;
          Chat = ChatSpeechModel;
          break;
        case 'SpellCheck':
          Conversation = ConversationSpellCheckModel;
          Chat = ChatSpellCheckModel;
          break;
        // case 'SummaryManageDocument':
        //   Conversation = ConversationSummaryManageDocumentModel;
        //   Chat = ChatSummaryManageDocumentModel;
        //   break;
        case 'VideoSummary':
          Conversation = ConversationVideoSummaryModel;
          Chat = ChatVideoSummaryModel;
          break;
        case 'WorkSchedule':
          Conversation = ConversationWorkScheduleModel;
          Chat = ChatWorkScheduleModel;
          break;
        case 'WriteReport':
          Conversation = ConversationWriteReportModel;
          Chat = ChatWriteReportModel;
          break;
        case 'WriteReportWithDoc':
          Conversation = ConversationWriteReportWithDocModel;
          Chat = ChatWriteReportWithDocModel;
          break;
        case 'WriteSpeechWithDoc':
          Conversation = ConversationWriteSpeechWithDocModel;
          Chat = ChatWriteSpeechWithDocModel;
          break;
        default:
          break;
      }
    } catch (error) {
      console.log(error);
    }

    next();
  };

  const listConversation = (next) => {
    let query = {
      member: deviceId,
      updatedAt: {
        $lt: from,
      },
      inactive: {
        $ne: true,
      },
    };
    if (deviceId === 'all') {
      delete query.member;
    }
    Conversation.find(query)
      .populate('documents', 'name size type')
      .sort('-updatedAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        conversations = results;

        next();
      });
  };

  const getLastChat = (next) => {
    async.each(
      conversations,
      (conversation, callback) => {
        Chat.findOne({ conversation: conversation._id })
          .sort('-createdAt')
          .lean()
          .exec((err, message) => {
            if (err) {
              return callback(err);
            }

            if (!message && (from !== futureTimestamp || !_.isEqual(conversation, conversations[0]))) {
              _.remove(conversations, { _id: conversation._id });

              return callback();
            }

            if (!message) {
              conversation.newConversation = 1;
            }

            conversation.lastMessage = message && message.question ? message.question : '';
            getConversationInfo(conversation, (info) => {
              conversation.info = info;
              callback();
            });
          });
      },
      (err) => {
        if (err) {
          return next(err);
        }
        let returnData = {
          code: CONSTANTS.CODE.SUCCESS,
          data: conversations.filter((item) => item.lastMessage),
        };
        if (_.isEmpty(conversations) && req.body.from) {
          returnData.message = {
            head: 'Thông báo',
            body: 'Không còn cuộc trò chuyện nào',
          };
        }
        next(returnData);
      }
    );
  };

  async.waterfall([checkParams, listConversation, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
