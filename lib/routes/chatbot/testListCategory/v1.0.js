const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

module.exports = (req, res) => {
  const page = req.body.page || 0;
  const limit = req.body.limit || 0;

  let list = [];
  let count = 0;
  let query = {
    status: 1,
  };
  const checkParams = (next) => {
    next();
  };

  const countTotal = (next) => {
    ChatTesingCategoryModel.count(query).exec((err, total) => {
      if (err) {
        return next(err);
      }
      count = Math.ceil(total / limit);
      next(null);
    });
  };

  const listTest = (next) => {
    ChatTesingCategoryModel.find(query)
      .sort('-createdAt')
      .skip(page * limit)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        list = results;
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: list,
          count,
        });
      });
  };

  async.waterfall([checkParams, countTotal, listTest], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
