const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const Conversation = require('../../../models/conversation');
const Chat = require('../../../models/chat');

module.exports = (req, res) => {
  const textSearch = req.body.textSearch;

  let deviceIds = [];

  const checkParams = (next) => {
    if (!textSearch.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const findUser = (next) => {
    UserModel.find({
      $or: [
        { name: { $regex: textSearch.trim(), $options: 'gi' } },
        { email: { $regex: textSearch.trim(), $options: 'gi' } },
        { phone: { $regex: textSearch.trim(), $options: 'gi' } },
      ],
    })
      .select('_id name phone email')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        deviceIds = results;
        next();
      });
  };

  const findMember = (next) => {
    MemberModel.find({
      $or: [{ name: { $regex: textSearch.trim(), $options: 'gi' } }, { phone: { $regex: textSearch.trim(), $options: 'gi' } }],
    })
      .select('_id phone name')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        if (!results.length) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: deviceIds,
          });
        }
        async.eachSeries(
          results,
          (result, done) => {
            TrackingActionModel.findOne({ member: result._id })
              .sort('-createdAt')
              .lean()
              .exec((err, log) => {
                if (err) {
                  return done(err);
                }
                deviceIds.push({
                  _id: _.get(result, '_id'),
                  name: `${result.name}`,
                  phone: result.phone,
                });
                if (log) {
                  deviceIds.push({
                    _id: _.get(log, 'otherInf.uniqueId'),
                    name: `${_.get(log, 'otherInf.platform')} - ${result.name}`,
                    phone: result.phone,
                  });
                }
                done();
              });
          },
          (err) => {
            if (err) {
              return next(err);
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: deviceIds,
            });
          }
        );
      });
  };

  async.waterfall([checkParams, findUser, findMember], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
