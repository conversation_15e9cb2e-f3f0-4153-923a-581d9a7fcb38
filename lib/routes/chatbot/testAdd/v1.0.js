const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

module.exports = (req, res) => {
  const question = req.body.question;
  const expectedAnswer = req.body.expectedAnswer;
  const expectedCheckAnswer = req.body.expectedCheckAnswer;
  const category = req.body.category;
  const userId = req.user.id;
  let questions = question.split(/\||\n/);
  const checkParams = (next) => {
    if (!question) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const add = (next) => {
    async.mapLimit(
      questions,
      5,
      (question, callback) => {
        ChatTesingModel.findOneAndUpdate({ question }, { question, expectedAnswer, expectedCheckAnswer, category, status: 1, updatedAt: Date.now() }, { upsert: true, new: true })
          .lean()
          .exec((err, result) => {
            callback(err);
          });
      },
      (err, result) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
        });
      }
    );
  };

  async.waterfall([checkParams, add], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
