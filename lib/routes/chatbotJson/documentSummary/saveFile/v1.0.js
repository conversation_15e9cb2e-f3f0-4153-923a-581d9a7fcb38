const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationDocumentSummary');
const Document = require('../../../../models/document');
const ChatBotManager = require('../../../../job/chatStreamManager');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const name = _.get(req, 'body.name', '');

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document;
  ChatBotManager.answering(conversationId);

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getDocument = (next) => {
    Conversation
      .findOne({
        _id: conversationId,
        isFile: true,
        document: { $exists: true }
      }, document)
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data || !data.document) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }

        document = data.document;

        next();
      });
  };

  const saveDocument = (next) => {
    const objUpdate = { status: 1, updatedAt: Date.now() };
    if (name) {
      objUpdate.name = name;
    }

    Document
      .update({ _id: document }, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next();
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([checkParams, getDocument, saveDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
