const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatDocumentSummary');
const Conversation = require('../../../../models/conversationDocumentSummary');
const UserModel = require('../../../../models/user');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const PushNotifyManager = require('../../../../job/pushNotify');
const DocumentWaitingManager = require('../../../../job/documentWaitingManager');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const content = _.get(req, 'body.content', '');
  const isVideo = _.get(req, 'body.isVideo', '');
  const word = _.get(req, 'body.word', '');

  let text = '';
  let urlDownload = '';
  const page = _.get(req, 'body.page', 1);
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  ChatBotManager.answering(conversationId);
  const notification = {
    title: 'Thông báo',
    message: `Tóm tắt tài liệu đã hoàn thành.`,
    data: {
      link: '/document-summarization',
      extras: {
        id: conversationId
      }
    }
  }

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    text = content ? `Tóm tắt nội dung sau:\n${content}` : `Tóm tắt nội dung các file cho tôi`;

    if (page && typeof page == 'number') {
      let wordCount = page * 500

      if (content && content.length) {
        const wordLength = content.split(' ').length
        if (wordLength < page * 500 * 3) {
          wordCount = Math.round(wordLength / 3)
        }
      }
      text = content ? `Tóm tắt nội dung sau trong ${wordCount} từ:\n${content}` : `Tóm tắt nội dung các file cho tôi trong ${page * 500} từ`;
    }

    if (isVideo) {
      text = `Tóm tắt nội dung file cho tôi trong tối đa 200 từ`;
      if (word) {
        text = `Tóm tắt nội dung file cho tôi trong tối đa ${word} từ`;
      }
    }
    console.log('haha:documentSummary:text', text);

    next();
  };

  const getUserInf = (next) => {
    if (!userId) {
      return next();
    }

    UserModel.findOne({ _id: userId })
      .select('name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        next();
      });
  };

  const checkConversationExists = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .populate({
        path: 'documents',
        select: 'status name'
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }

        conversation = data;

        // Check if all documents are completed
        if (conversation.documents && conversation.documents.length > 0) {
          let allCompleted = true;
          const processingDocuments = [];

          for (const doc of conversation.documents) {
            if (doc.status !== 'COMPLETED') {
              allCompleted = false;
              processingDocuments.push(doc.name || 'Tài liệu đang xử lý');
            }
          }

          if (allCompleted) {
            ask();
            next(null, { code: CONSTANTS.CODE.SUCCESS });
          } else {
            // Store the request data in the waiting manager
            DocumentWaitingManager.addJob(conversationId, {
              userId,
              conversationId,
              documents: conversation.documents,
              requestData: {
                id: conversationId,
                ...req.body
              },
              type: 'document-summary'
            });

            logger.logInfo(`Added job to waiting queue for conversation ${conversationId} with ${conversation.documents.length} documents`);

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: `Đang xử lý tài liệu: ${processingDocuments.join(', ')}. Hệ thống sẽ tự động xử lý khi tài liệu sẵn sàng.`
              }
            });
          }
        } else {
          ask();
          next(null, { code: CONSTANTS.CODE.SUCCESS });
        }
      });
  };


  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_queue`,
        data: {
          conversation_id: conversation.id,
          text,
          callback_url: `${config.proxyRequestServer.currentServerAddr}/api/v1.0/callback/document-summary`
        },
        timeout: 60000,
      });

      // Get task_id and status from response
      const queueTaskId = resAxios.data.task_id;
      const initialStatus = resAxios.data.status || 'queued'; // Use status from chat_queue response
      const initialResult = null; // No result yet
      const initialError = null; // No error yet

      logger.logInfo(`Task ${queueTaskId} created with initial status: ${initialStatus} for conversation ${conversationId}`);

      // Create chat record with the initial status from API
      createCollection(queueTaskId, initialStatus, initialResult, initialError);

      // Schedule regular status checks
      setTimeout(() => {
        checkQueueStatus(queueTaskId);
      }, 5000);

      // Stop streaming since we're using queue API
      ChatBotManager.stop(conversationId);

      // Update status in ChatStreamManager
      ChatBotManager.answering(conversationId, {
        status: initialStatus,
        documentStatus: 'COMPLETED' // Documents are already processed at this point
      });

      // Only notify client about conversation update
      // Client will call getConversation to get the latest status
      PushNotifyManager.sendViaSocket(userId, 'conversation_update', notification.data, []);

      return;
    } catch (err) {
      logger.logError([err], 'documentSummary.ask', { conversationId });

      // Handle API call error
      error = err.message || 'Error calling chat_queue API';
      ChatBotManager.stop(conversationId);

      // Update status in ChatStreamManager to failed
      ChatBotManager.answering(conversationId, {
        status: 'failed',
        documentStatus: 'COMPLETED' // Documents are already processed at this point
      });

      // Only notify client about conversation update
      // Client will call getConversation to get the latest status
      PushNotifyManager.sendViaSocket(userId, 'conversation_update', notification.data, []);

      // Create chat record with error message and failed status
      createCollection(null, 'failed', null, error);
    }
  };

  // Function to check the status of a queued task
  const checkQueueStatus = async (queueTaskId) => {
    try {
      // Check if the task has already been processed
      const chat = await Chat.findOne({ _id }).lean().exec();
      if (!chat || !chat.queueTaskId) {
        logger.logInfo(`Task ${queueTaskId} not found, skipping status check`);
        return;
      }

      // Make a request to check the status
      const statusResponse = await axios({
        method: 'get',
        url: `${serverChatbot}/api/2.0.0/chat_queue_status/${queueTaskId}`,
        timeout: 10000,
      });

      logger.logInfo(`Status check for task ${queueTaskId}: ${JSON.stringify(statusResponse.data)}`);

      // Process the status response
      if (statusResponse.data) {
        const taskStatus = statusResponse.data.status;
        const taskResult = statusResponse.data.result;
        const taskError = statusResponse.data.error;

        // Update the chat with the exact status from API
        const updateData = {
          status: taskStatus,
          updatedAt: Date.now()
        };

        // Add result or error if available
        if (taskResult) {
          updateData.answer = taskResult.toString();

          // Extract download URL if present
          const lines = taskResult.trim().split('\n');
          const lastLine = lines[lines.length - 1].trim();
          const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);

          if (linkMatch) {
            updateData.urlDownload = linkMatch[2];
          }
        }

        if (taskError) {
          updateData.error = taskError;
        }

        // Update the chat record
        await Chat.updateOne({ _id }, updateData);

        // Update status in ChatStreamManager and notify the user if task is completed or failed
        if (taskStatus === 'success' || taskStatus === 'failed') {
          // Update status in ChatStreamManager
          ChatBotManager.answering(conversationId, {
            status: taskStatus,
            documentStatus: 'COMPLETED' // Documents are already processed at this point
          });

          if (taskStatus === 'success' && taskResult) {
            // For success with result, save the answer in ChatStreamManager
            ChatBotManager.saveAnswer(conversationId, taskResult);
          }

          // Only notify client about conversation update
          // Client will call getConversation to get the latest status
          PushNotifyManager.sendViaSocket(userId, 'conversation_update', notification.data, []);
        } else if (taskStatus === 'processing' || taskStatus === 'queued' || taskStatus === 'pending') {
          // Task is still processing, schedule another check
          logger.logInfo(`Task ${queueTaskId} is in ${taskStatus} status, scheduling another check`);
          setTimeout(() => {
            checkQueueStatus(queueTaskId);
          }, 5000);
        }
      }
    } catch (error) {
      logger.logError([error], 'documentSummary.checkQueueStatus', { queueTaskId });

      // If there's an error checking the status, schedule another attempt
      setTimeout(() => {
        checkQueueStatus(queueTaskId);
      }, 10000);
    }
  };

  const createCollection = (queueTaskId = null, status = 'queued', result = null, taskError = null) => {
    const chatData = {
      _id,
      member: userId,
      conversation: conversation._id,
      question: text,
      content,
      createdAt
    };

    // Set status from API or default
    chatData.status = status;

    // Only set answer if we have a result from AI
    if (result) {
      chatData.answer = result.toString();

      // Extract download URL if present
      const lines = result.trim().split('\n');
      const lastLine = lines[lines.length - 1].trim();
      const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);

      if (linkMatch) {
        chatData.urlDownload = linkMatch[2];
      }
    } else if (response) {
      // If we have a response from a non-queue API call
      chatData.answer = response.toString();
      chatData.urlDownload = urlDownload;
    }
    // If no result or response, leave answer field empty

    // Set error if available
    if (taskError) {
      chatData.error = taskError;
    } else {
      chatData.error = error;
    }

    // Store queue task ID for status checking
    if (queueTaskId) {
      chatData.queueTaskId = queueTaskId;
    }

    Chat.create(
      chatData,
      (err) => {
        if (err) {
          logger.logError([err], 'documentSummary.createCollection', { conversationId });
          return;
        }

        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => { }
        );
      }
    );
  };

  async.waterfall([checkParams, getUserInf, checkConversationExists], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
