const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationDocumentSummary');
const Chat = require('../../../../models/chatDocumentSummary');
const ChatBotManager = require('../../../../job/chatStreamManager');
const UserModel = require('../../../../models/user');
const TrackingActionModel = require('../../../../models/trackingAction');

const getConversationInfo = (conversation, callback) => {
  let info = {};
  if (!conversation) {
    return callback(info);
  }

  if (mongoose.isValidObjectId(conversation.member)) {
    UserModel.findOne({ _id: conversation.member })
      .select('name phone')
      .lean()
      .exec((err, user) => {
        if (err) {
          return callback();
        }

        if (user) {
          info = user;
          callback(info);
        }
      });
  } else {
    TrackingActionModel.findOne({ 'otherInf.uniqueId': conversation.member })
      .select('otherInf member')
      .populate('member', 'name phone')
      .lean()
      .exec((err, tracking) => {
        if (err) {
          return callback();
        }

        if (tracking) {
          info = {
            name: _.get(tracking, 'otherInf.platform', '') + ' - ' + _.get(tracking, 'member.name', ''),
            phone: _.get(tracking, 'member.phone', ''),
          };
        }

        callback(info);
      });
  }
};

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body._id', '');

  let conversation = {};

  const checkParams = (next) => {
    if (!userId || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getConversation = (next) => {
    let query = {
      _id: conversationId,
      member: userId,
      inactive: {
        $ne: true,
      }
    };

    if (userId === 'all') {
      delete query.member;
    }

    Conversation
      .findOne(query)
      .populate('documents')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SUCCESS,
          });
        }

        conversation = result;

        next();
      });
  };

  const getLastChat = (next) => {
    ChatBotManager.getAnswer(conversation._id, 'documentSummary')
      .then((answer) => {
        conversation.status = answer.status || 'PENDING';

        if (conversation.updatedAt <= conversation.createdAt && !conversation.hasMessage) {
          conversation.newConversation = 1;
          conversation.lastMessage = answer.bodyData && answer.bodyData.content ? answer.bodyData.content : 'Cuộc trò chuyện mới';
        }

        if (conversation.documents && conversation.documents.length && conversation.documents[0].name) {
          conversation.lastMessage = conversation.documents[0].name;

          getConversationInfo(conversation, (info) => {
            conversation.info = info;

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: conversation
            });
          });
        } else {
          if (conversation.newConversation) {
            getConversationInfo(conversation, (info) => {
              conversation.info = info;
              next({
                code: CONSTANTS.CODE.SUCCESS,
                data: conversation
              });
            });

            return;
          }

          Chat.findOne({ conversation: conversation._id })
            .sort('-createdAt')
            .lean()
            .exec((err, message) => {
              if (err) {
                return next(err);
              }

              conversation.lastMessage = message && message.content ? message.content : 'Cuộc trò chuyện mới';
              getConversationInfo(conversation, (info) => {
                conversation.info = info;

                next({
                  code: CONSTANTS.CODE.SUCCESS,
                  data: conversation
                });
              });
            });
        }
      })
  };

  async.waterfall([checkParams, getConversation, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
