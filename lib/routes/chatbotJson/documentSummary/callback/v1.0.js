const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const PushNotifyManager = require('../../../../job/pushNotify');
const UserNotifyModel = require('../../../../models/userNotify');
const Chat = require('../../../../models/chatDocumentSummary');
const Conversation = require('../../../../models/conversationDocumentSummary');
const ChatBotManager = require('../../../../job/chatStreamManager');

module.exports = (req, res) => {
  // Extract data from request body
  const taskId = _.get(req, 'body.task_id', '');
  const conversationId = _.get(req, 'body.conversation_id', '');
  const status = _.get(req, 'body.status', '');
  const result = _.get(req, 'body.result', '');
  const error = _.get(req, 'body.error', '');

  // Variables for processing
  let chat;
  let conversation;

  // Validate required parameters
  const checkParams = (next) => {
    if (!taskId || !conversationId || !status) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  // Find the chat entry
  const findChat = (next) => {
    Chat.findOne({ _id: taskId })
      .populate('conversation')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: `Chat with ID ${taskId} not found`
          });
        }

        chat = result;
        next();
      });
  };

  // Find the conversation
  const findConversation = (next) => {
    Conversation.findOne({ id: conversationId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: `Conversation with ID ${conversationId} not found`
          });
        }

        conversation = result;
        next();
      });
  };

  // Update the chat with the result or error
  const updateChat = (next) => {
    const updateData = {
      updatedAt: Date.now()
    };

    // Update with exact status from API
    updateData.status = status;

    // Process the result if available
    if (result) {
      // Check for download URL in the result
      let urlDownload = '';
      let processedResult = result;

      const lines = result.trim().split('\n');
      const lastLine = lines[lines.length - 1].trim();
      const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);

      if (linkMatch) {
        urlDownload = linkMatch[2];
        processedResult = lines.slice(0, -1).join('\n').trim();
      }

      updateData.answer = processedResult;
      if (urlDownload) {
        updateData.urlDownload = urlDownload;
      }
    }

    // Add error if available
    if (error) {
      updateData.error = error;
    }

    // Stop streaming if it's still active
    if (ChatBotManager.isStreaming(conversationId)) {
      ChatBotManager.stop(conversationId);
    }

    Chat.updateOne(
      { _id: taskId },
      updateData,
      (err, updateResult) => {
        if (err) {
          return next(err);
        }

        if (updateResult.nModified === 0) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Failed to update chat'
          });
        }

        // Update conversation to mark it as having messages
        Conversation.updateOne(
          { _id: chat.conversation },
          {
            hasMessage: true,
            updatedAt: Date.now()
          },
          (err) => {
            if (err) {
              logger.logError([err], 'documentSummary.callback.updateConversation', { conversationId });
            }
          }
        );

        next();
      }
    );
  };

  // Send notification to the user
  const sendNotification = (next) => {
    if (!chat || !chat.member) {
      return next();
    }

    try {
      // Determine notification message based on status
      let notificationTitle = 'Thông báo';
      let notificationMessage = '';

      if (status === 'success') {
        notificationMessage = 'Tóm tắt tài liệu của bạn đã được xử lý hoàn tất.';
      } else if (status === 'failed') {
        notificationMessage = 'Tóm tắt tài liệu của bạn không thể xử lý được.';
      } else {
        // No notification for other statuses
        return next();
      }

      // Send push notification
      PushNotifyManager.sendViaSocket(
        chat.member.toString(),
        'conversation_update',
        {data: {link: '', extras: {id: conversationId}}},
        []
      );

      // Emit socket event for real-time updates
      if (status === 'success') {
        io.to(conversationId).emit('endMessage', {
          _id: chat._id,
          member: chat.member,
          id: conversationId,
          conversation: chat.conversation,
          question: chat.question,
          answer: chat.answer,
          createdAt: chat.createdAt,
          urlDownload: chat.urlDownload
        });
      }

      // Create notification in database
      UserNotifyModel.create({
        user: chat.member,
        title: notificationTitle,
        message: notificationMessage,
        data: {
          link: `/conversation/documentSummary`,
          extras: {
            id: conversationId
          }
        }
      }, (err) => {
        if (err) {
          logger.logError([err], 'documentSummary.callback.sendNotification', { taskId, conversationId });
        }
      });

      next();
    } catch (error) {
      logger.logError([error], 'documentSummary.callback.sendNotification', { taskId, conversationId });
      next();
    }
  };

  // Execute the waterfall
  async.waterfall([
    checkParams,
    findChat,
    findConversation,
    updateChat,
    sendNotification
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    if (err) {
      return res.json(err);
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Document summary callback processed successfully'
    });
  });
};
