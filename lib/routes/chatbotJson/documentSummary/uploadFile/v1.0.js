const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatDocumentSummary');
const Conversation = require('../../../../models/conversationDocumentSummary');
const Document = require('../../../../models/document');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const ChatBotManager = require('../../../../job/chatStreamManager');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const file = _.get(req, 'file', '');

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  const serverUploadFile = config.proxyRequestServer.serverUploadFile;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  ChatBotManager.answering(conversationId);
  const text = 'Tóm tắt tài liệu';
  let documentId

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const checkConversationExists = (next) => {
    Conversation
      .findOne({
        _id: conversationId,
        isFile: true
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }

        conversation = data;

        next();
      });
  };

  const upload = (next) => {
    try {
      const formData = new FormData();

      formData.append('agent_impl_id', config.chatbotAgent.chatWithDoc);
      formData.append('conv_id', conversation.id);
      formData.append('user_id', userId);
      formData.append('category_id', 14);
      if (file) {
        formData.append('file', fs.createReadStream(file.path), file.originalname);
      }

      axios.post(`${serverUploadFile}/api/2.0.0/documents-conv/upload`, formData, {
        headers: {
          ...formData.getHeaders(),
        },
      })
        .then(response => {
          let objCreate = {
            id: response.data.id,
            name: response.data.name,
            nameAlias: tool.change_alias(response.data.name),
            type: response.data.file_type,
            size: response.data.size_in_bytes,
            url: `${serverChatbot}${response.data.link}`,
            urlPreview: `${serverChatbot}/app/documents-preview/${response.data.name}`,
            urlDownload: `${serverChatbot}/api/2.0.0/documents/download/${response.data.id}`,
            member: userId,
            status: 0
          }

          Document
            .create(objCreate, (err, result) => {
              if (err) {
                return next(err)
              }

              documentId = result._id

              fs.unlink(file.path, (unlinkErr) => {
                if (unlinkErr) {
                  console.error(`Failed to delete temporary file ${file.path}:`, unlinkErr);
                } else {
                  console.log(`Successfully deleted temporary file: ${file.path}`);
                }
              });

              if (documentId) {
                Conversation.update(
                  {
                    _id: conversation._id,
                  },
                  {
                    document: documentId,
                    updatedAt: Date.now()
                  }, () => { }
                );
              }

              ask();

              next(null, { code: CONSTANTS.CODE.SUCCESS, data: response.data });
            })
        })
        .catch(err => {
          next(err);
        })
    } catch (error) {
      next(error);
    }
  };

  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      const stream = resAxios.data;
      resAxios.data.on('data', (chunk) => {
        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt,
            });

            createCollection();
          }

          return;
        }

        try {
          const data = JSON.parse(chunk.toString());
          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          console.error("=============== err", chunk.toString(), e);
          let text = chunk.toString();
          if (text.startsWith(`{"event": "message", "message": "`)) {
            response += text
          }

          if (text.endsWith(`"}`)) {
            response += text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if (!response || !response.trim()) {
          response = messageError
          error = new Error('no response');
        }

        if (response && ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId);

          PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();

          if (response !== messageError) {
            Document.update(
              { _id: documentId },
              { summary: response, updatedAt: Date.now() },
              () => { }
            )
          }
        }
      });
      stream.on('error', (err) => {
        if (ChatBotManager.isStreaming(conversationId)) {
          error = err;
          response = messageError;
          ChatBotManager.stop(conversationId);

          PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();
        }
      });
    } catch (err) {
      if (ChatBotManager.isStreaming(conversationId)) {
        error = err;
        response = messageError;
        ChatBotManager.stop(conversationId);

        PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    }
  };

  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        document: documentId,
        createdAt,
      },
      (err, result) => {
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => { }
        );
      }
    );
  };

  async.waterfall([checkParams, checkConversationExists, upload], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
