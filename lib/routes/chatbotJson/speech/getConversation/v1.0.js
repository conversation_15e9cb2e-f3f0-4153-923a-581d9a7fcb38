const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationSpeech');
const Chat = require('../../../../models/chatSpeech');
const ChatBotManager = require('../../../../job/chatStreamManager');

const getConversationInfo = (conversation, callback) => {
  let info = {};
  if (!conversation) {
    return callback(info);
  }

  if (mongoose.isValidObjectId(conversation.member)) {
    UserModel.findOne({ _id: conversation.member })
      .select('name phone')
      .lean()
      .exec((err, user) => {
        if (err) {
          return callback();
        }

        if (user) {
          info = user;
          callback(info);
        }
      });
  } else {
    TrackingActionModel.findOne({ 'otherInf.uniqueId': conversation.member })
      .select('otherInf member')
      .populate('member', 'name phone')
      .lean()
      .exec((err, tracking) => {
        if (err) {
          return callback();
        }

        if (tracking) {
          info = {
            name: _.get(tracking, 'otherInf.platform', '') + ' - ' + _.get(tracking, 'member.name', ''),
            phone: _.get(tracking, 'member.phone', ''),
          };
        }

        callback(info);
      });
  }
};

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body._id', '');

  let conversation = {};

  const checkParams = (next) => {
    if (!userId || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getConversation = (next) => {
    let query = {
      _id: conversationId,
      member: userId,
      inactive: {
        $ne: true,
      }
    };

    if (userId === 'all') {
      delete query.member;
    }

    Conversation
      .findOne(query)
      .populate('documents')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SUCCESS,
          });
        }

        conversation = result;

        next();
      });
  };

  const getLastChat = (next) => {
    ChatBotManager.getAnswer(conversation._id, 'speech')
      .then((answer) => {
        conversation.status = answer.status || 'PENDING';

        if (conversation.updatedAt <= conversation.createdAt && !conversation.hasMessage) {
          conversation.newConversation = 1;
          conversation.lastMessage = getLastMessage(answer.bodyData, conversation);

          getConversationInfo(conversation, (info) => {
            conversation.info = info;

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: conversation
            });
          });

          return;
        }

        Chat.findOne({ conversation: conversation._id })
          .sort('-createdAt')
          .lean()
          .exec((err, message) => {
            if (err) {
              return next(err);
            }

            conversation.lastMessage = getLastMessage(message, conversation);

            getConversationInfo(conversation, (info) => {
              conversation.info = info;

              next({
                code: CONSTANTS.CODE.SUCCESS,
                data: conversation
              });
            });
          });
      })
  };

  const getLastMessage = (message, conversation) => {
    if (!message) {
      return 'Cuộc trò chuyện mới';
    }

    let lastMessage = 'Cuộc trò chuyện mới';
    const { listener, content } = message;

    if (conversation.documents && conversation.documents.length && conversation.documents[0].name) {
      const docs = conversation.documents.map(doc => doc.name).join(', ');
      lastMessage = `Dựa vào file: ${docs}, hãy viết bài phát biểu với các thông tin sau đây:\nNgười nghe: ${listener}\nNội dung: ${content}`;
    } else {
      lastMessage = `Viết bài phát biểu với các thông tin sau đây:\nNgười nghe: ${listener}\nNội dung: ${content}`;
    }

    return lastMessage;
  };

  async.waterfall([checkParams, getConversation, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
