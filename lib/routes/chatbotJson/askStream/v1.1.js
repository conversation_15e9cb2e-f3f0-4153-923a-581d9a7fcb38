const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Chat = require('../../../models/chat')
const Conversation = require('../../../models/conversation')
const axios = require('axios');
const ChatBotManager = require('../../../job/chatStreamManager')
const { checkStringByCondition } = require('../../../util/tool');
const ConfigModel = require('../../../models/config');

module.exports = (req, res) => {

  const conversationId = req.body.id;
  const userId = req.user.id;
  const testId = req.body.testId;
  const testMode = req.body.testMode;
  const expectedAnswer = req.body.expectedAnswer;
  const expectedCheckAnswer = req.body.expectedCheckAnswer;
  const is_testing = _.get(req, 'body.is_testing', false);
  const disable_search_web = _.get(req, 'body.disable_search_web', false);
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let text = _.get(req, 'body.text', '');
  let response
  const createdAt = Date.now()
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR
  let error = null;
  let answeredAt
  let urlChatbot = `/api/2.0.0/chat_stream_pre_prod`;

  ChatBotManager.answering(conversationId)
  const checkParams = (next) => {
    if(!userId || !serverChatbot || !conversationId){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const getConfigMaintain = (next) => {
    ConfigModel
      .findOne({ type: CONSTANTS.CONFIG_TYPE.CHATBOT_MAINTAIN }, 'config')
      .lean()
      .exec((err, result) => {
        if (result && result.config && result.config.maintain) {
          if (ChatBotManager.isStreaming(conversationId)) {
            error = err;
            response = result.config.message || messageError;
            ChatBotManager.stop(conversationId);

            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversationId,
              question: text,
              answer: response,
              createdAt,
            });

            createCollection();
          }

          return next({ code: CONSTANTS.CODE.SUCCESS });
        }

        next();
      });
  };

  const checkConversationExists = (next) => {
    Conversation
    .findOne({
      _id: conversationId
    })
    .lean()
    .exec((err, data) => {
      if(err) {
        return next(err);
      }
      if(!data) {
        return next({
          code: 400
        })
      }
      conversation = data
      if(!_.has(conversation, 'is_dev')) {
        urlChatbot = `/api-v2/chat_stream_public`;
      }
      ask()
      next(null,{code:200});
    })
  }
  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
  }
  const  ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}${urlChatbot}`,
        responseType: 'stream',
        data: {
          "conversation_id": conversation.id,
          text,
          is_testing,
          disable_search_web,
        },
        timeout:300000
      });

      const stream = resAxios.data;
      let buffer = '';
      let totalSend = ''
      resAxios.data.on('data',(chunk) => {
        if (!response) {
          answeredAt = Date.now();
        }

        if(!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if(response) {
            io.to(conversationId).emit('endMessage',{
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt
            });
            createCollection();
          }
          return;
        }
        try {
          const data = JSON.parse(chunk.toString())
          const searchingArray = ['on_search','on_handle_process'];
          if(searchingArray.includes(data.event)) {
            // io.to(conversationId).emit('searching', { text: data.message });
          } else if(data.event === 'message') {
            response += data.message;
            response = response.replace('undefined','')
            response = response.replace('```markdown','')
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt
            });
          }
        } catch(e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if(response && ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId)
          io.to(conversationId).emit('endMessage',{
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt
          });
          createCollection()
        }
      });
      stream.on('error', (err) => {
        if(ChatBotManager.isStreaming(conversationId)) {
          error = err
          response = messageError
          ChatBotManager.stop(conversationId)
          io.to(conversationId).emit('endMessage',{
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt
          });
          createCollection()
        }
      });
    } catch (err) {
      if(ChatBotManager.isStreaming(conversationId)) {
        error = err
        response = messageError
        ChatBotManager.stop(conversationId)
        io.to(conversationId).emit('endMessage',{
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt
        });

        createCollection()
      }
    }
  }

  function splitIntoChunks(text, chunkSize) {
      const chunks = [];
      for (let i = 0; i < text.length; i += chunkSize) {
          chunks.push(text.slice(i, i + chunkSize));
      }
      return chunks;
  }
  const createCollection = (next) => {
    Chat
    .create({
      _id,
      member: userId,
      id: conversationId,
      conversation: conversationId,
      question: text,
      answer: response,
      answeredAt,
      createdAt,
      error,
      urlChatbot
    },(err,result) => {
      Conversation
        .update({
          _id: conversationId,
        },{
          hasMessage: true,
          updatedAt: Date.now(),
        },() =>{})

        if (testId) {
          ChatTesingResultModel.create({
            _id,
            member: userId,
            id: conversationId,
            conversation: conversationId,
            question: text,
            answer: response,
            answeredAt,
            error,
            group: testId,
          });
          checkAnswer(conversationId, text, response, expectedCheckAnswer, (isValid, explain) => {
            let checkedResponse = checkStringByCondition(response, expectedAnswer)
            ChatTesingGroupModel.updateOne(
              { _id: testId, 'questions.conversation': conversationId },
              {
                $set: {
                  'questions.$.isValid': testMode !== 'normal' ? isValid : checkedResponse.isValid,
                  'questions.$.explain': testMode !== 'normal' ? explain : checkedResponse.explain,
                  'questions.$.isTesting': false,
                  'questions.$.testedAt': Date.now(),
                  'questions.$.urlChatbot': urlChatbot,
                },
              }
            )
              .lean()
              .exec((err, result) => {
                res.json({ code: CONSTANTS.CODE.SUCCESS });
              });
          });
        }
    })
  }


  const checkAnswer = async (conversation_id, question, checking_answer, correct_answer, cb) => {
    if (!correct_answer) {
      return cb(null, 'Điều kiện không xác định');
    }
    const checkAnswerResponse = await axios({
      method: 'post',
      url: `${serverChatbot}/api-common/check-answer`,
      responseType: 'apllication/json',
      data: {
        conversation_id,
        question,
        checking_answer,
        correct_answer,
      },
      timeout:300000,
    });
    let textData = _.get(checkAnswerResponse, 'data.data');
    const isValid = textData.includes('Đúng');
    const explain = textData
    return cb(isValid, explain);
  };


  async.waterfall([
    checkParams,
    getConfigMaintain,
    checkConversationExists,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    if (!testId) {
      res.json(data || err);
    }
  })
}
