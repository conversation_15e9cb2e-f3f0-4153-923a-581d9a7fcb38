const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatPlanning');
const Conversation = require('../../../../models/conversationPlanning');
const UserModel = require('../../../../models/user');
const UserNotifyModel = require('../../../../models/userNotify');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const PushNotifyManager = require('../../../../job/pushNotify');
const DocumentWaitingManager = require('../../../../job/documentWaitingManager');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const role = _.get(req, 'body.role', '');
  const goal = _.get(req, 'body.goal', '');
  const page = _.get(req, 'body.page', 1);
  const type = _.get(req, 'body.type', '');
  const startDate = _.get(req, 'body.startDate', '');
  const endDate = _.get(req, 'body.endDate', '');
  const isFile = _.get(req, 'body.isFile', false);

  let text = '';
  let urlDownload = '';

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  let hasNotifiedClient = false;
  req.body.modelType = 'planning';
  req.body.member = userId;
  ChatBotManager.answering(conversationId, req.body);
  const typeArr = ['custom','month','quarter','year']
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId || !goal || !role  || !type ) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    if(!typeArr.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    if(type === 'custom' && (!endDate || !startDate)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    let timeText = '';
    if(type == 'custom') {
      timeText = `trong khoảng thời gian từ ngày ${startDate} đến ngày ${endDate}`
    }

    const currentYear = new Date().getFullYear();
    const month = new Date().getMonth();
    const currentMonth = month + 1;
    const currentQuarter = Math.floor(month / 3) + 1;

    if( type === 'month') {
      timeText = `của từng tuần trong tháng ${currentMonth} năm ${currentYear}`
    }

    if( type === 'quarter') {
      timeText = `của từng tháng trong quý ${currentQuarter} năm ${currentYear}`
    }

    if( type === 'year') {
      timeText = `của từng quý trong năm ${currentYear}`
    }

    if (isFile) {
      text = `Dựa vào file, lập kế hoạch trong ${page * 500} từ cho tôi với các thông tin sau đây:\nVai trò: ${role}\nMục tiêu: ${goal} ${timeText}`;
    } else {
      text = `Lập kế hoạch trong ${page * 500} từ cho tôi với các thông tin sau đây:\nVai trò: ${role}\nMục tiêu: ${goal} ${timeText}`;
    }

    console.log('haha:text', text)
    next();
  };

  const getUserInf = (next) => {
    if (!userId) {
      return next();
    }

    UserModel.findOne({ _id: userId })
      .select('name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        next();
      });
  };

  const checkConversationExists = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .populate('documents', 'name status')
      .lean()
      .exec((err, data) => {
        console.log('haha:err', err, data, conversationId);
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }

        conversation = data;
        next();
      });
  };

  const checkDocumentsStatus = (next) => {
    if (!conversation.documents || conversation.documents.length === 0) {
      ask();
      return next(null, { code: CONSTANTS.CODE.SUCCESS });
    }

    // Check if all documents are completed
    let allCompleted = true;
    const processingDocuments = [];

    for (const doc of conversation.documents) {
      if (doc.status !== 'COMPLETED') {
        allCompleted = false;
        processingDocuments.push(doc.name || 'Tài liệu đang xử lý');
      }
    }

    if (allCompleted) {
      ask();
      next(null, { code: CONSTANTS.CODE.SUCCESS });
    } else {
      // Store the request data in the waiting manager
      DocumentWaitingManager.addJob(conversationId, {
        userId,
        conversationId,
        documents: conversation.documents,
        requestData: {
          id: conversationId,
          ...req.body
        },
        type: 'planning'
      });

      logger.logInfo(`Added job to waiting queue for conversation ${conversationId} with ${conversation.documents.length} documents`);

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: `Đang xử lý tài liệu: ${processingDocuments.join(', ')}. Hệ thống sẽ tự động xử lý khi tài liệu sẵn sàng.`
        }
      });
    }
  };
  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
  }
  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      const stream = resAxios.data;
      resAxios.data.on('data', (chunk) => {
        console.log('haha:chunk', chunk.toString());
        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            if(!response || !response.trim()) {
              response = messageError;
              error = new Error('no response');
            }
            const lines = response.trim().split('\n');
            const lastLine = lines.pop().trim();
            response = lines.join('\n').trim();
            const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
            urlDownload = linkMatch ? linkMatch[2] : '';

            if(!urlDownload) {
              response += '\n' + lastLine
            }

            pushNotify(error);
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt,
              urlDownload
            });

            createCollection();
          }

          return;
        }

        try {
          const data = JSON.parse(chunk.toString());
          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            if (!hasNotifiedClient) {
              PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
              hasNotifiedClient = true;
            }

            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if(ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId);
          if(!response || !response.trim()) {
            response = messageError;
            error = new Error('no response');
          }
          const lines = response.trim().split('\n');
          const lastLine = lines.pop().trim();
          response = lines.join('\n').trim();
          const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
          urlDownload = linkMatch ? linkMatch[2] : '';

          if(!urlDownload) {
            response += '\n' + lastLine
          }

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
            urlDownload
          });

          createCollection();
        }
      });
      stream.on('error', (err) => {
        if (ChatBotManager.isStreaming(conversationId)) {
          error = err;
          response = messageError;
          ChatBotManager.stop(conversationId);

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();
        }
      });
    } catch (err) {
      if (ChatBotManager.isStreaming(conversationId)) {
        error = err;
        response = messageError;
        console.log("message stop with error",messageError)
        ChatBotManager.stop(conversationId);

        pushNotify(error);
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    }
  };

  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        role,
        goal,
        page,
        createdAt,
        urlDownload,
        type,
        startDate,
        endDate
      },
      (err, result) => {
        console.log('haha:createCollection', err, result);
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => {}
        );
      }
    );
  };

  const pushNotify = (error) => {
    const notification = {
      title: 'Thông báo',
      message: `Lập kế hoạch ${error ? 'thất bại' : 'đã hoàn thành'}.`,
      data: {
        link: '/work-plan',
        extras: {
          id: conversationId
        }
      }
    }

    PushNotifyManager.sendToMemberAssistant(userId.toString(), notification.title, notification.message, notification.data, 'conversation_update');
    UserNotifyModel
      .create({
        user: userId,
        title: notification.title || '',
        message: notification.message || '',
        data: notification.data || {},
      })
  }

  async.waterfall([checkParams, getUserInf, checkConversationExists, checkDocumentsStatus], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
