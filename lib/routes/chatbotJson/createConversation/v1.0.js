const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Conversation = require('../../../models/conversation')
const Chat = require('../../../models/chat')
const AgentManager = require('../../../job/agentManager')

module.exports = (req, res) => {

  const forceCreate = req.body.forceCreate;
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversationId, oldConversationId
  const oneHourInMs = ms('15m');
  const id = _.get(req.body, 'id', '');
  const checkParams = (next) => {
    if(!serverChatbot){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(!id) {
      return next({
        code: CONSTANTS.CODE.SUCCESS
      })
    }
    next();
  }

  const getLastConversation = (next) => {
    Conversation
      .findOne({
        member: id,
        inactive:{
          $ne: true
        }
      }, 'updatedAt')
      .sort({ updatedAt: -1 })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (data) {
          oldConversationId = data._id;
        }

        const now = Date.now();
        if (data && data.updatedAt && !forceCreate) {
          let diff = now - data.updatedAt;
          if (diff < oneHourInMs) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: data._id
            })
          }
        }

        next();
      })
  }

  const getLastChat = (next) => {
    if (!oldConversationId) {
      return next();
    }

    Chat
      .findOne({
        member: id,
        conversation: oldConversationId
      }, 'conversation updatedAt')
      .sort({ updatedAt: -1 })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: oldConversationId
          })
        }

        const now = Date.now();
        if (data && data.updatedAt && !forceCreate) {
          let diff = now - data.updatedAt;
          if (diff < oneHourInMs) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: data.conversation
            })
          }
        }

        next();
      })
  }

  const createConversation = (next) => {
      const options = {
        method: "POST",
        uri: `${serverChatbot}/api/2.0.0/create-conversation`,
        body: {
          "chatbot_id": config.chatbot.id,
          user_id: id,
          agents_impl_id: AgentManager.getAgentValue('chatPublic', false),
          is_dev: AgentManager.isAgentDev('chatPublic')
        },
        timeout: 14000,
        json: true, // Automatically stringifies the body to JSON
      }
      rp(options)
        .then((result) => {
          if(result && result.id) {
            conversationId = result.id
            next();
          } else {
            return next({
              code: 400
            })
          }
        })
        .catch((error) => {
          return next({
            code: 400,
            error: error
          })
        })
  }

  const createCollection = (next) => {
    Conversation
    .create({
      id: conversationId,
      member: id,
      agents_impl_id: AgentManager.getAgentValue('chatPublic', false),
      is_dev: AgentManager.isAgentDev('chatPublic')
    },(err,result) => {
      return next({
        code: 200,
        data: result._id
      })
    })
  }


  async.waterfall([
    checkParams,
    // getLastConversation,
    // getLastChat,
    createConversation,
    createCollection
  ], (err, data) => {
    console.log(err, data)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
