const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationMakeDecision');
const Chat = require('../../../../models/chatMakeDecision');
const ChatBotManager = require('../../../../job/chatStreamManager');
const UserModel = require('../../../../models/user');
const TrackingActionModel = require('../../../../models/trackingAction');

const getConversationInfo = (conversation, callback) => {
  let info = {};
  if (!conversation) {
    return callback(info);
  }

  if (mongoose.isValidObjectId(conversation.member)) {
    UserModel.findOne({ _id: conversation.member })
      .select('name phone')
      .lean()
      .exec((err, user) => {
        if (err) {
          return callback();
        }

        if (user) {
          info = user;
          callback(info);
        }
      });
  } else {
    TrackingActionModel.findOne({ 'otherInf.uniqueId': conversation.member })
      .select('otherInf member')
      .populate('member', 'name phone')
      .lean()
      .exec((err, tracking) => {
        if (err) {
          return callback();
        }

        if (tracking) {
          info = {
            name: _.get(tracking, 'otherInf.platform', '') + ' - ' + _.get(tracking, 'member.name', ''),
            phone: _.get(tracking, 'member.phone', ''),
          };
        }

        callback(info);
      });
  }
};

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body._id', '');

  let conversation = {};

  const checkParams = (next) => {
    if (!userId || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getConversation = (next) => {
    let query = {
      _id: conversationId,
      member: userId,
      inactive: {
        $ne: true,
      }
    };

    if (userId === 'all') {
      delete query.member;
    }

    Conversation
      .findOne(query)
      .populate('documents')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SUCCESS,
          });
        }

        conversation = result;

        next();
      });
  };

  const getLastChat = (next) => {
    ChatBotManager.getAnswer(conversation._id, 'makeDecision')
      .then((answer) => {
        conversation.status = answer.status || 'PENDING';

        if (conversation.updatedAt <= conversation.createdAt && !conversation.hasMessage) {
          conversation.newConversation = 1;
          conversation.lastMessage = getLastMessage(answer.bodyData, conversation);

          getConversationInfo(conversation, (info) => {
            conversation.info = info;

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: conversation
            });
          });

          return;
        }

        Chat.findOne({ conversation: conversation._id })
          .sort('-createdAt')
          .lean()
          .exec((err, message) => {
            if (err) {
              return next(err);
            }

            conversation.lastMessage = getLastMessage(message, conversation);

            getConversationInfo(conversation, (info) => {
              conversation.info = info;

              next({
                code: CONSTANTS.CODE.SUCCESS,
                data: conversation
              });
            });
          });
      })
  };

  const getLastMessage = (message, conversation) => {
    if (!message) {
      return 'Cuộc trò chuyện mới';
    }

    let lastMessage = 'Cuộc trò chuyện mới';
    const { field, topic, position, type, timeline, specificTimeline, year, specificTimelineEnd, yearEnd } = message;

    if (conversation.documents && conversation.documents.length && conversation.documents[0].name) {
      const docs = conversation.documents.map(doc => doc.name).join(', ');
      lastMessage = `Dựa vào nội dung file: ${docs}\nVới cương vị là: ${position}. \nHãy đưa ra quyết định để phát triển ${topic}.`;
    } else {
      if (field && topic && position && type && timeline && specificTimeline && year) {
        lastMessage = `Dựa vào kết quả tình hình ${field} [topicData] \nVới cương vị là: ${position}. \nHãy đưa ra quyết định để phát triển ${topic}`;
        if (type === 'Dữ liệu từ khoảng thời gian xác định') {
          lastMessage = lastMessage.replace('[topicData]', `từ ${specificTimeline === 12 ? `` : `${timeline} ${specificTimeline} `}năm ${year} đến ${specificTimelineEnd === 12 ? `` : `${timeline} ${specificTimelineEnd} `}năm ${yearEnd} tại Hải Phòng`);
        } else {
          lastMessage = lastMessage.replace('[topicData]', `từ ${specificTimeline === 12 ? `` : `${timeline} ${specificTimeline} `}năm ${year} tại Hải Phòng`);
        }
      }
    }

    return lastMessage;
  };

  async.waterfall([checkParams, getConversation, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
