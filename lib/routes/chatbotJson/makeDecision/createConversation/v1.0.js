const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const rp = require('request-promise')
const Conversation = require('../../../../models/conversationMakeDecision')
const Chat = require('../../../../models/chatMakeDecision')
const AgentManager = require('../../../../job/agentManager')

module.exports = (req, res) => {

  const userId = req.user.id;
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversationId
  const isFile = req.body.isFile;
  const checkParams = (next) => {
    if (!userId || !serverChatbot) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const createConversation = (next) => {
    const options = {
      method: "POST",
      uri: `${serverChatbot}/api/2.0.0/create-conversation`,
      body: {
        agents_impl_id: isFile ? AgentManager.getAgentValue('makeDecision', true) : AgentManager.getAgentValue('makeDecision', false),
        is_dev: AgentManager.isAgentDev('makeDecision'),
        user_id: userId
      },
      timeout: 14000,
      json: true, // Automatically stringifies the body to JSON
    }

    rp(options)
      .then((result) => {
        if (result && result.id) {
          conversationId = result.id
          next();
        } else {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
          })
        }
      })
      .catch((error) => {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
          error: error
        })
      })
  }

  const createCollection = (next) => {
    Conversation
      .create({
        id: conversationId,
        member: userId,
        isFile,
        agents_impl_id: isFile ? AgentManager.getAgentValue('makeDecision', true) : AgentManager.getAgentValue('makeDecision', false),
        is_dev: AgentManager.isAgentDev('makeDecision')
      }, (err, result) => {
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          data: result._id
        })
      })
  }

  async.waterfall([
    checkParams,
    createConversation,
    createCollection
  ], (err, data) => {

    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
