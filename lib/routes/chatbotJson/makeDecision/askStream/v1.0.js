const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatMakeDecision');
const Conversation = require('../../../../models/conversationMakeDecision');
const UserModel = require('../../../../models/user');
const UserNotifyModel = require('../../../../models/userNotify');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const PushNotifyManager = require('../../../../job/pushNotify');
const DocumentWaitingManager = require('../../../../job/documentWaitingManager');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const position = _.get(req, 'body.position', '');
  const field = _.get(req, 'body.field', '');
  const topic = _.get(req, 'body.topic', '');
  const type = _.get(req, 'body.type', '');
  const timeline = _.get(req, 'body.timeline', '');
  const specificTimeline = _.get(req, 'body.specificTimeline', '');
  const year = _.get(req, 'body.year', '');
  const specificTimelineEnd = _.get(req, 'body.specificTimelineEnd', '');
  const yearEnd = _.get(req, 'body.yearEnd', '');
  const isFile = _.get(req, 'body.isFile', false);
  let urlDownload = '';

  let text = '';

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response = '';
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  let hasNotifiedClient = false;
  req.body.modelType = 'makeDecision';
  req.body.member = userId;
  ChatBotManager.answering(conversationId, req.body);

  const checkParams = (next) => {
    if(!isFile) {
      if (!userId || !serverChatbot || !conversationId || !position || !field || !topic || !type || !timeline || !specificTimeline || !year) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
        });
      }

      text = `Dựa vào kết quả tình hình ${field} [topicData] \nVới cương vị là: ${position}. \nHãy đưa ra quyết định để phát triển ${topic}`;
      if (type === 'Dữ liệu từ khoảng thời gian xác định') {
        text = text.replace('[topicData]', `từ ${specificTimeline === 12 ? `` : `${timeline} ${specificTimeline} `}năm ${year} đến ${specificTimelineEnd === 12 ? `` : `${timeline} ${specificTimelineEnd} `}năm ${yearEnd} tại Hải Phòng`);
      } else {
        text = text.replace('[topicData]', `từ ${specificTimeline === 12 ? `` : `${timeline} ${specificTimeline} `}năm ${year} tại Hải Phòng`);
      }
      text+= '. Trả về kết quả trong tối đa 1500 từ, đưa số liệu cụ thể nếu có.'

    } else {
      if (!userId || !serverChatbot || !conversationId || !position || !topic) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
        });
      }
      text = `Dựa vào nội dung file \nVới cương vị là: ${position}. \nHãy đưa ra quyết định ${topic}. Trả về kết quả trong tối đa 1500 từ.`;
    }

    next();
  };

  const checkConversationExists = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .populate('documents', 'name status')
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }

        conversation = data;

        next();
      });
  };

  const checkDocumentsStatus = (next) => {
    if (!conversation.documents || conversation.documents.length === 0) {
      ask();
      return next(null, { code: CONSTANTS.CODE.SUCCESS });
    }

    let allCompleted = true;
    let processingDocuments = [];

    async.each(conversation.documents, (document, callback) => {
      if (!document || !document.status) {
        return callback();
      }

      if (document.status !== 'COMPLETED') {
        allCompleted = false;
        processingDocuments.push(document.name || `Tài liệu ${document._id}`);
      }

      callback();
    }, (err) => {
      if (err) {
        return next(err);
      }

      if (allCompleted) {
        ask();
        next(null, { code: CONSTANTS.CODE.SUCCESS });
      } else {
        // Store the request data in the waiting manager
        DocumentWaitingManager.addJob(conversationId, {
          userId,
          conversationId,
          documents: conversation.documents,
          requestData: {
            id: conversationId,
            ...req.body
          },
          type: 'make-decision'
        });

        logger.logInfo(`Added job to waiting queue for conversation ${conversationId} with ${conversation.documents.length} documents`);

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: `Đang xử lý tài liệu: ${processingDocuments.join(', ')}. Hệ thống sẽ tự động xử lý khi tài liệu sẵn sàng.`
          }
        });
      }
    });
  };

  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
  }
  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      const stream = resAxios.data;
      resAxios.data.on('data', (chunk) => {
        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            if(!response || !response.trim()) {
              response = messageError;
              error = new Error('no response');
            }
            const lines = response.trim().split('\n');
            const lastLine = lines.pop().trim();
            response = lines.join('\n').trim();
            const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
            urlDownload = linkMatch ? linkMatch[2] : '';

            if(!urlDownload) {
              response += '\n' + lastLine
            }

            pushNotify(error);
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt,
              urlDownload
            });

            createCollection();
          }

          return;
        }

        try {
          const data = JSON.parse(chunk.toString());
          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            if (!hasNotifiedClient) {
              PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
              hasNotifiedClient = true;
            }

            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if (ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId);
          if(!response || !response.trim()) {
            response = messageError;
            error = new Error('no response');
          }
          const lines = response.trim().split('\n');
          const lastLine = lines.pop().trim();
          response = lines.join('\n').trim();
          const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
          urlDownload = linkMatch ? linkMatch[2] : '';

          if(!urlDownload) {
            response += '\n' + lastLine
          }

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
            urlDownload
          });

          createCollection();
        }
      });
      stream.on('error', (err) => {
        if (ChatBotManager.isStreaming(conversationId)) {
          error = err;
          response = messageError;
          ChatBotManager.stop(conversationId);

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();
        }
      });
    } catch (err) {
      if(ChatBotManager.isStreaming(conversationId)) {
        error = err;
        response = messageError;
        ChatBotManager.stop(conversationId);

        pushNotify('Hỗ trợ ra quyết định thất bại.');
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    }
  };

  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        position,
        field,
        topic,
        type,
        timeline,
        specificTimeline,
        year,
        specificTimelineEnd,
        yearEnd,
        createdAt,
        urlDownload
      },
      (err, result) => {
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => {}
        );
      }
    );
  };

  const pushNotify = (error) => {
    const notification = {
      title: 'Thông báo',
      message: `Hỗ trợ ra quyết định ${error ? 'thất bại' : 'đã hoàn thành'}.`,
      data: {
        link: '/decision-support',
        extras: {
          id: conversationId
        }
      }
    }

    PushNotifyManager.sendToMemberAssistant(userId.toString(), notification.title, notification.message, notification.data, 'conversation_update');
    UserNotifyModel
      .create({
        user: userId,
        title: notification.title || '',
        message: notification.message || '',
        data: notification.data || {},
      })
  }

  async.waterfall([checkParams, checkConversationExists, checkDocumentsStatus], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
