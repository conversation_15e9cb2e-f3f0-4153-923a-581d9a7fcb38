const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const PushNotifyManager = require('../../../job/pushNotify');
const UserNotifyModel = require('../../../models/userNotify');
const mongoose = require('mongoose');

// Import all Chat models
const Chat = require('../../../models/chat');
const ChatMakeDecision = require('../../../models/chatMakeDecision');
const ChatSpeech = require('../../../models/chatSpeech');
const ChatITT = require('../../../models/chatITT');
const ChatTextToSpeech = require('../../../models/chatTextToSpeech');
const ChatTestimony = require('../../../models/chatTestimony');
const ChatWriteSpeechWithDoc = require('../../../models/chatWriteSpeechWithDoc');
const ChatVideoSummary = require('../../../models/chatVideoSummary');
const ChatDocumentSummary = require('../../../models/chatDocumentSummary');
const ChatPlanning = require('../../../models/chatPlanning');
const ChatSpellCheck = require('../../../models/chatSpellCheck');

// Map task types to their corresponding Chat models
const CHAT_MODELS = {
  'chat': Chat,
  'makeDecision': ChatMakeDecision,
  'speech': ChatSpeech,
  'imageToText': ChatITT,
  'textToSpeech': ChatTextToSpeech,
  'testimony': ChatTestimony,
  'writeSpeechWithDocument': ChatWriteSpeechWithDoc,
  'videoSummary': ChatVideoSummary,
  'documentSummary': ChatDocumentSummary,
  'planning': ChatPlanning,
  'spellCheck': ChatSpellCheck
};

module.exports = (req, res) => {
  // Extract data from request body
  const taskId = _.get(req, 'body.task_id', '');
  const conversationId = _.get(req, 'body.conversation_id', '');
  const status = _.get(req, 'body.status', '');
  const result = _.get(req, 'body.result', '');
  const error = _.get(req, 'body.error', '');
  const taskType = _.get(req, 'body.task_type', '');

  // Variables for processing
  let chat;
  let chatModel;

  // Validate required parameters
  const checkParams = (next) => {
    if (!taskId || !conversationId || !status || !taskType) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Check if we have a valid chat model for this task type
    chatModel = CHAT_MODELS[taskType];
    if (!chatModel) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: `Invalid task type: ${taskType}`
      });
    }

    next();
  };

  // Find the chat entry
  const findChat = (next) => {
    chatModel.findOne({ _id: taskId })
      .populate('conversation')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: `Chat with ID ${taskId} not found`
          });
        }

        chat = result;
        next();
      });
  };

  // Update the chat with the result or error
  const updateChat = (next) => {
    const updateData = {
      updatedAt: Date.now()
    };

    // Update based on status
    if (status === 'COMPLETED') {
      updateData.answer = result;
    } else if (status === 'FAILED') {
      updateData.error = error;
    }

    chatModel.updateOne(
      { _id: taskId },
      updateData,
      (err, updateResult) => {
        if (err) {
          return next(err);
        }

        if (updateResult.nModified === 0) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Failed to update chat'
          });
        }

        next();
      }
    );
  };

  // Send notification to the user
  const sendNotification = (next) => {
    if (!chat || !chat.member) {
      return next();
    }

    try {
      // Determine notification message based on status
      let notificationTitle = 'Thông báo';
      let notificationMessage = '';

      if (status === 'COMPLETED') {
        notificationMessage = 'Yêu cầu của bạn đã được xử lý hoàn tất.';
      } else if (status === 'FAILED') {
        notificationMessage = 'Yêu cầu của bạn không thể xử lý được.';
      } else {
        // No notification for other statuses
        return next();
      }

      // Send push notification
      PushNotifyManager.sendViaSocket(
        chat.member.toString(),
        'conversation_update',
        {data: {link: '', extras: {id: conversationId}}},
        []
      );

      // Create notification in database
      UserNotifyModel.create({
        user: chat.member,
        title: notificationTitle,
        message: notificationMessage,
        data: {
          link: `/conversation/${taskType}`,
          extras: {
            id: conversationId
          }
        }
      }, (err) => {
        if (err) {
          logger.logError([err], 'callback.sendNotification', { taskId, conversationId });
        }
      });

      next();
    } catch (error) {
      logger.logError([error], 'callback.sendNotification', { taskId, conversationId });
      next();
    }
  };

  // Execute the waterfall
  async.waterfall([
    checkParams,
    findChat,
    updateChat,
    sendNotification
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    if (err) {
      return res.json(err);
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Callback processed successfully'
    });
  });
};
