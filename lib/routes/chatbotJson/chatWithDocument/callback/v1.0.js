const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const DocumentWaitingManager = require('../../../../job/documentWaitingManager');
const DocumentStatusChecker = require('../../../../job/documentStatusChecker');
const axios = require('axios');
const PushNotifyManager = require('../../../../job/pushNotify');
const UserNotifyModel = require('../../../../models/userNotify');

module.exports = (req, res) => {
  const docId = _.get(req, 'body.id', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document
  let conversationId
  const checkParams = (next) => {
    if ( !serverChatbot || !docId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getDocument = (next) => {
    Document
      .findOne({ id: docId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        document = result;
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }
        next()
      })
  }


  const getStatusDocument = (next) => {
    try {
      axios.get(`${serverChatbot}/api/2.0.0/documents/status/${document.id}`)
        .then(function (response) {
          if(response && response.data) {
            let status = document.status;
            if(response.data && response.data.data && response.data.data.status) {
              status = response.data.data.status
              if(status === 'PENDING' || status === 'CHUNKED') {
                status = 'PROCESSING'
              }
            }
            Document
              .update({
                _id: document._id
              },{
                status,
                docCategory: response.data.data.doc_category || '',
                summary: response.data.data.doc_summary || '',
                title: response.data.data.doc_title || '',
                subtitle: response.data.data.subtitle || '',
                highlight: response.data.data.highlight || '',
                aiName: response.data.data.name || '',
              }, (err, result) => {
                if (err) {
                  return next(err);
                }

                if (document.status !== status) {
                  // Notify user about document status change
                  const notification = {
                    title: 'Thông báo',
                    message: `Tài liệu ${document.name} đã được xử lý xong.`,
                    data: {
                      link: '/manage-document',
                      extras: {
                        id: document._id
                      }
                    }
                  }
                  PushNotifyManager.sendToMemberAssistant(document.member.toString(), notification.title, notification.message, notification.data, 'document_update');
                  UserNotifyModel
                    .create({
                      user: document.member,
                      title: notification.title || '',
                      message: notification.message || '',
                      data: notification.data || {},
                    })

                  // Let DocumentWaitingManager handle finding and checking conversations
                  DocumentWaitingManager.checkDocumentInConversations(document._id, status);
                }

                if (status !== 'PROCESSING') {
                  // If status changed to COMPLETED or FAILED, make sure it's removed from the checker
                  console.log(`Removing document ${document._id} from status checker queue as it's now ${status}`);
                  DocumentStatusChecker.removeDocumentJob(document._id.toString());
                }

                return next({
                  code: CONSTANTS.CODE.SUCCESS
                })
              })
          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
        })
        .catch(function (err) {
          // Nếu API trả về lỗi 404, cập nhật trạng thái thành FAILED
          if (err.response && err.response.status === 404) {
            console.log(`Document ${document.id} not found on server (404), marking as FAILED`);

            // Cập nhật trạng thái document thành FAILED
            Document.update(
              { _id: document._id },
              { status: 'FAILED' },
              (updateErr, result) => {
                if (updateErr) {
                  console.error('Failed to update document status to FAILED:', updateErr);
                  return next(updateErr);
                }

                console.log(`Successfully marked document ${document._id} as FAILED due to 404 error`);

                // Xóa document khỏi hàng đợi kiểm tra
                DocumentStatusChecker.removeDocumentJob(document._id.toString());

                // Thông báo cho DocumentWaitingManager
                try {
                  DocumentWaitingManager.checkDocumentInConversations(document._id, 'FAILED');
                  console.log(`Notified DocumentWaitingManager about document ${document._id} failure`);

                  // Thông báo cho người dùng
                  const notification = {
                    title: 'Thông báo',
                    message: `Tài liệu ${document.name} không thể xử lý được.`,
                    data: {
                      link: '/manage-document',
                      extras: {
                        id: document._id
                      }
                    }
                  }
                  PushNotifyManager.sendToMemberAssistant(
                    document.member.toString(),
                    notification.title,
                    notification.message,
                    notification.data,
                    'document_update'
                  );
                  UserNotifyModel
                    .create({
                      user: document.member,
                      title: notification.title || '',
                      message: notification.message || '',
                      data: notification.data || {},
                    })
                } catch (notifyError) {
                  console.error('Failed to notify DocumentWaitingManager:', notifyError);
                }

                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  message: {
                    head: 'Thông báo',
                    body: 'Tài liệu không thể xử lý được'
                  }
                });
              }
            );
          } else {
            return next(err);
          }
        })
    } catch (error) {
      next(error);
    }
  }

  async.waterfall([checkParams, getDocument, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
