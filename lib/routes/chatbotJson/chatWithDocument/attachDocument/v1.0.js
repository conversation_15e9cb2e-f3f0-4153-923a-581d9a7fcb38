const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const ConversationITTModel = require('../../../../models/conversationITT');
const createConversationHandle = require('../createConversation/v1.0');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body._id', '');
  const type = _.get(req, 'body.type', '');
  const conversationId = _.get(req, 'body.conversationId', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document;
  let conversation;
  let Conversation;
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId || !type || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }
    switch (type) {
      case 'chat-with-document':
        Conversation = ConversationChatWithDocumentModel;
        break;
      case 'write-report':
        Conversation = ConversationWriteReportModel;
        break;
      case 'document-summary':
        Conversation = ConversationDocumentSummaryModel;
        break;
      case 'make-decision':
        Conversation = ConversationMakeDecisionModel;
        break;
      case 'speech':
        Conversation = ConversationSpeechModel;
        break;
      case 'itt':
        Conversation = ConversationITTModel;
        break;
      case 'spell-check':
        Conversation = ConversationSpellCheckModel;
        break;
      case 'video-summary':
        Conversation = ConversationVideoSummaryModel;
        break;
      case 'video-excerpt':
        Conversation = ConversationVideoExcerptModel;
        break;
      case 'meeting-summary':
        Conversation = ConversationMeetingSummaryModel;
        break;
      case 'meeting-synthesis':
        Conversation = ConversationMeetingSynthesisModel;
        break;
      case 'planning':
        Conversation = ConversationPlanningModel;
        break;
      case 'write-article':
        Conversation = ConversationWriteArticleModel;
        break;
      case 'text-to-speech':
        Conversation = ConversationTextToSpeechModel;
        break;
      case 'write-script':
        Conversation = ConversationWriteScriptModel;
        break;
      case 'testimony':
        Conversation = ConversationTestimonyModel;
        break;
      default:
        break;
    }

    next();
  };

  const getDocument = (next) => {
    Document.findOne({ _id: docId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR,
          });
        }
        document = result;
        // if (document.status !== 'COMPLETED') {
        //   return next({
        //     code: CONSTANTS.CODE.SYSTEM_ERROR,
        //     message: {
        //       head: 'Thông báo',
        //       body: `Tài liệu \"${document.name}\" chưa được xử lý thành công. Bạn vui lòng đợi trong giây lát và thử lại sau`,
        //     },
        //   });
        // }
        next();
      });
  };

  const findConversation = (next) => {
    Conversation.findById(conversationId)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR,
          });
        }
        conversation = result;

        next();
      });
  };

  const attachDocument = (next) => {
    try {
      const formData = new FormData();
      formData.append('document_id', document.id);
      formData.append('con_id', conversation.id);
      formData.append('agent_impl_id', conversation.agents_impl_id);

      axios
        .post(`${serverChatbot}/api/2.0.0/documents-conv/attach`, formData, {
          headers: {
            ...formData.getHeaders(),
          },
        })
        .then((response) => {
          if (!response || response.status !== 200) {
            return next({ code: response.status });
          }

          Conversation.findOneAndUpdate({ _id: conversationId }, { $addToSet: { documents: document._id }, updatedAt: Date.now() })
            .lean()
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next({
                code: CONSTANTS.CODE.SUCCESS,
                data: document,
              });
            });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([checkParams, getDocument, findConversation, attachDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
