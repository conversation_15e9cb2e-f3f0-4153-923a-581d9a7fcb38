const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const Conversation = require('../../../../models/conversationChatWithDocument');
const createConversationHandle = require('../createConversation/v1.0');
const FormData = require('form-data');
const axios = require('axios');
const User = require('../../../../models/user');
const Unit = require('../../../../models/unit');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body._id', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document
  let conversationId
  let userUnits = [];

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getUserUnitsAndDescendants = (next) => {
    User.findById(userId)
      .select('units')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.UNAUTHORIZED,
            message: MESSAGES.AUTH.INVALID_TOKEN,
          });
        }

        const directUnits = user.units.map(unit => unit.toString());
        userUnits.push(...directUnits);

        if (directUnits.length > 0) {
          Unit.find({ parentPath: { $in: directUnits } })
            .select('_id')
            .lean()
            .exec((err, descendantUnits) => {
              if (err) {
                return next(err);
              }
              descendantUnits.forEach(unit => {
                if (!userUnits.includes(unit._id.toString())) {
                  userUnits.push(unit._id.toString());
                }
              });
              next();
            });
        } else {
          next();
        }
      });
  };

  const getDocument = (next) => {
    const accessConditions = [];

    accessConditions.push({ member: userId });

    if (userUnits.length > 0) {
      accessConditions.push({
        isShared: true,
        allowedUnits: { $in: userUnits },
      });
    }

    Document.findOne({
      _id: docId,
      $or: accessConditions,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        document = result;
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        next()
      })
  }


  const getStatusDocument = (next) => {
    try {
      axios.get(`${serverChatbot}/api/2.0.0/documents/status/${document.id}`)
        .then(function (response) {
          if(response && response.data) {
            let status = document.status;
            if(response.data && response.data.data && response.data.data.status) {
              status = response.data.data.status
              if(status === 'PENDING' || status === 'CHUNKED') {
                status = 'PROCESSING'
              }
            }
            Document
              .findOneAndUpdate({
                _id: docId
              },{
                status,
                docCategory: response.data.data.doc_category || '',
                summary: response.data.data.doc_summary || '',
                title: response.data.data.doc_title || '',
                subtitle: response.data.data.subtitle || '',
                highlight: response.data.data.highlight || '',
                aiName: response.data.data.name || '',
              },{
                new: true
              },(err,data) => {
                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  data
                })
              })
          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            })
          }

        })
        .catch(function (err) {
          return next(err)
        })
    } catch (error) {
      next(error);
    }
  }

  async.waterfall([checkParams, getUserUnitsAndDescendants, getDocument, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
