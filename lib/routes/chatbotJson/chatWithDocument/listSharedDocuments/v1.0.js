const _ = require('lodash');
const async = require('async');
const config = require('config');
const moment = require('moment');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const User = require('../../../../models/user');
const Unit = require('../../../../models/unit');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const name = _.get(req, 'body.name', '');
  const type = _.get(req, 'body.type', '');
  const startTime = _.get(req, 'body.startTime', '');
  const endTime = _.get(req, 'body.endTime', '');
  let query = { isShared: true };

  let count = 0;
  let userUnits = [];

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getUserUnitsAndDescendants = (next) => {
    User.findById(userId)
      .select('units')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.UNAUTHORIZED,
            message: MESSAGES.AUTH.INVALID_TOKEN,
          });
        }

        const directUnits = user.units.map(unit => unit.toString());
        userUnits.push(...directUnits);

        if (directUnits.length > 0) {
          Unit.find({ parentPath: { $in: directUnits } })
            .select('_id')
            .lean()
            .exec((err, descendantUnits) => {
              if (err) {
                return next(err);
              }

              descendantUnits.forEach(unit => {
                if (!userUnits.includes(unit._id.toString())) {
                  userUnits.push(unit._id.toString());
                }
              });

              next();
            });
        } else {
          next();
        }
      });
  };

  const buildQuery = (next) => {
    if (userUnits.length === 0) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: [],
        count: 0,
      });
    }

    if (name) {
      query['$or'] = [
        {
          nameAlias: new RegExp(tool.change_alias(name), 'gi'),
        },
        {
          code: name,
        },
        {
          summary: new RegExp(name, 'gi'),
        },
        {
          title: new RegExp(name, 'gi'),
        },
      ];
    }

    if (startTime && endTime) {
      query.updatedAt = {
        $gte: moment(startTime).valueOf(),
        $lte: moment(endTime).valueOf(),
      };
    }

    if (type) {
      query.type = _.isArray(type) ? { $in: type } : type;
    }

    query.status = {
      $ne: 'DELETED'
    };
    query.selectedUnits = { $in: userUnits };

    next();
  };

  const countQuery = (next) => {
    Document.countDocuments(query)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);

        next();
      });
  };

  const listDocument = (next) => {
    Document.find(query)
      .sort({ updatedAt: -1 })
      .skip(limit * page)
      .limit(limit)
      .populate('selectedUnits', 'name nameAlias icon parent parentPath')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([
    checkParams,
    getUserUnitsAndDescendants,
    buildQuery,
    countQuery,
    listDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
