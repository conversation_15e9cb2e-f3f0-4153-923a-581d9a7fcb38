const _ = require('lodash');
const async = require('async');
const config = require('config');
const axios = require('axios');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const keyword = _.get(req, 'body.keyword', '');
  const type = _.get(req, 'body.type', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;

  let count = 0;
  let documents;
  const checkParams = (next) => {
    if (!userId || !keyword) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const aiSearch = (next) => {
    axios
      .post(`${serverChatbot}/api-rag/search_with_document`, {
        user_heyu_id: userId,
        query: keyword.trim(),
        page: page + 1,
        size: limit,
        top_k: 10
      })
      .then((response) => {
        let data = response.data;
        if (!data) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: documents,
            count,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy file nào phù hợp nội dung tìm kiếm',
            },
          });
        }
        documents = _.get(data, 'documents', []);
        next();
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: {
              head: 'Thông báo',
              body: _.get(err.response, 'data.message', MESSAGES.SYSTEM.ERROR),
            },
          });
        }
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });
      });
  };

  const listDocument = (next) => {
    if (_.isEmpty(documents)) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: documents,
        count,
        message: {
          head: 'Thông báo',
          body: 'Không tìm thấy file nào phù hợp nội dung tìm kiếm',
        },
      });
    }
    let query = {
      member: userId,
      id: { $in: documents.map((doc) => doc.id) },
    };
    if (type) {
      query.type = _.isArray(type) ? { $in: type } : type;
    }
    Document.find(query)
      .sort({ updatedAt: -1 })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, aiSearch, listDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
