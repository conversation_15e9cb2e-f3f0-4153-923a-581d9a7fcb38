const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const axios = require('axios');
const DocumentWaitingManager = require('../../../../job/documentWaitingManager');
const DocumentStatusChecker = require('../../../../job/documentStatusChecker');

/**
 * API endpoint to manually check document status
 * This is useful in case the callback doesn't work
 */
module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body.docId', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document;

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getDocument = (next) => {
    Document
      .findOne({ _id: docId, member: userId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        document = result;
        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        next();
      });
  };

  const getStatusDocument = (next) => {
    logger.logInfo(`Manually checking status for document ${document.id}`);

    try {
      axios.get(`${serverChatbot}/api/2.0.0/documents/status/${document.id}`)
        .then(function (response) {
          if (response && response.data) {
            let status = document.status;
            if (response.data && response.data.data && response.data.data.status) {
              status = response.data.data.status;
              if (status === 'PENDING' || status === 'CHUNKED') {
                status = 'PROCESSING';
              }
            }

            Document
              .findOneAndUpdate({
                _id: docId
              }, {
                status,
                docCategory: response.data.data.doc_category || '',
                summary: response.data.data.doc_summary || '',
                title: response.data.data.doc_title || '',
                subtitle: response.data.data.subtitle || '',
                highlight: response.data.data.highlight || '',
                aiName: response.data.data.name || '',
              }, {
                new: true
              }, (err, updatedDocument) => {
                if (err) {
                  return next(err);
                }

                // If status is not COMPLETED or FAILED, add to the document status checker
                if (status !== 'COMPLETED' && status !== 'FAILED') {
                  DocumentStatusChecker.addDocumentJob(docId);
                } else if (document.status !== 'COMPLETED' && document.status !== 'FAILED') {
                  // If status changed to COMPLETED or FAILED, notify DocumentWaitingManager
                  try {
                    DocumentWaitingManager.checkDocumentInConversations(document._id, status);
                    console.log(`Notified DocumentWaitingManager about document ${document._id} status change to ${status}`);
                  } catch (error) {
                    console.error('checkDocumentStatus.notifyManager:', error);
                  }
                }

                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  data: {
                    document: updatedDocument,
                    previousStatus: document.status,
                    newStatus: status,
                    inCheckQueue: status !== 'COMPLETED' && status !== 'FAILED'
                  }
                });
              });
          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
        })
        .catch(function (err) {
          // Nếu API trả về lỗi 404, cập nhật trạng thái thành FAILED
          if (err.response && err.response.status === 404) {
            console.log(`Document ${document.id} not found on server (404), marking as FAILED`);

            // Cập nhật trạng thái document thành FAILED
            Document.findOneAndUpdate(
              { _id: docId },
              { status: 'FAILED' },
              { new: true },
              (updateErr, updatedDocument) => {
                if (updateErr) {
                  console.error('Failed to update document status to FAILED:', updateErr);
                  return next(updateErr);
                }

                if (!updatedDocument) {
                  console.error('Document not found when trying to mark as FAILED:', docId);
                  return next({
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGES.SYSTEM.ERROR
                  });
                }

                console.log(`Successfully marked document ${docId} as FAILED due to 404 error`);

                // Xóa document khỏi hàng đợi kiểm tra
                DocumentStatusChecker.removeDocumentJob(docId);

                // Thông báo cho DocumentWaitingManager
                try {
                  DocumentWaitingManager.checkDocumentInConversations(docId, 'FAILED');
                  console.log(`Notified DocumentWaitingManager about document ${docId} failure`);
                } catch (notifyError) {
                  console.error('Failed to notify DocumentWaitingManager:', notifyError);
                }

                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  data: {
                    document: updatedDocument,
                    previousStatus: document.status,
                    newStatus: 'FAILED',
                    inCheckQueue: false,
                    message: 'Document not found on server, marked as FAILED'
                  }
                });
              }
            );
          } else {
            return next(err);
          }
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([checkParams, getDocument, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      if (typeof MailUtil !== 'undefined') {
        MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
      }
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
