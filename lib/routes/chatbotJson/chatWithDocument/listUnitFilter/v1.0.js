const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit');
const User = require('../../../../models/user');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const textSearch = _.get(req, 'body.textSearch', '').trim();
  const userId = _.get(req, 'user.id', '');
  let userUnits = [];

  // Check input parameters
  const checkParams = (next) => {
    if (!textSearch) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tham số tìm kiếm không được để trống.',
        },
      });
    }

    next();
  };

  // Get user units and all related units (ancestors + descendants)
  const getUserUnitsAndRelated = (next) => {
    User.findById(userId)
      .select('units')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        if (!user) {
          return next({
            code: CONSTANTS.CODE.UNAUTHORIZED,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông tin người dùng.',
            },
          });
        }

        const directUnits = user.units.map(unit => unit.toString());
        userUnits.push(...directUnits);

        if (directUnits.length > 0) {
          // Get user's direct units with their parentPath to find ancestors
          Unit.find({ _id: { $in: directUnits } })
            .select('_id parentPath')
            .lean()
            .exec((err, userDirectUnits) => {
              if (err) {
                return next(err);
              }

              // Collect all ancestor unit IDs from parentPath
              const ancestorUnits = [];
              userDirectUnits.forEach(unit => {
                if (unit.parentPath && unit.parentPath.length > 0) {
                  unit.parentPath.forEach(ancestorId => {
                    const ancestorIdStr = ancestorId.toString();
                    if (!userUnits.includes(ancestorIdStr) && !ancestorUnits.includes(ancestorIdStr)) {
                      ancestorUnits.push(ancestorIdStr);
                    }
                  });
                }
              });

              // Add ancestor units to userUnits
              userUnits.push(...ancestorUnits);

              // Find all descendant units (units that have user's units in their parentPath)
              Unit.find({ parentPath: { $in: directUnits } })
                .select('_id')
                .lean()
                .exec((err, descendantUnits) => {
                  if (err) {
                    return next(err);
                  }

                  descendantUnits.forEach(unit => {
                    const unitIdStr = unit._id.toString();
                    if (!userUnits.includes(unitIdStr)) {
                      userUnits.push(unitIdStr);
                    }
                  });

                  next();
                });
            });
        } else {
          next();
        }
      });
  };

  // Search units by nameAlias within user's scope
  const searchUnits = (next) => {
    // Chuyển đổi query thành nameAlias để tìm kiếm chính xác hơn
    const queryAlias = tool.change_alias(textSearch);

    // Tìm kiếm đơn vị theo nameAlias trong phạm vi units của user
    const searchConditions = {
      status: 1, // Chỉ tìm các unit đang hoạt động
      _id: { $in: userUnits } // Chỉ tìm trong units mà user có quyền
    };

    if (textSearch) {
      searchConditions.nameAlias = { $regex: queryAlias };
    }

    Unit.find(searchConditions)
      .select('_id name nameAlias parent parentPath')
      .populate('parent', 'name')
      .lean()
      .exec((err, units) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: units,
          // message: {
          //   head: 'Thông báo',
          //   body: `Tìm thấy ${units.length} đơn vị phù hợp trong phạm vi của bạn.`,
          // },
        });
      });
  };

  // Handle errors and send response
  const handleResponse = (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  };

  // Execute waterfall
  async.waterfall([
    // checkParams,
    getUserUnitsAndRelated,
    searchUnits
  ], handleResponse);
};
