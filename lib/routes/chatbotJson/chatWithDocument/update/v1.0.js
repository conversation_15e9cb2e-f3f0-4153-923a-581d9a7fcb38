const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const Conversation = require('../../../../models/conversationChatWithDocument');
const createConversationHandle = require('../createConversation/v1.0');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body._id', '');
  const code = _.get(req, 'body.code', '');
  const name = _.get(req, 'body.name', '');

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId ) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    if(!name && !code) {
      return next({
        code: CONSTANTS.CODE.SUCCESS
      });
    }
    next();
  };

  const getDocument = (next) => {
    console.log(userId)
    Document
      .findOne({ _id: docId, member: userId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        document = result;
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        next()
      })
  }

  const checkCodeExists = (next) => {
    if (!code) {
      return next();
    }
    Document.count({
      code: code,
      _id: {
        $ne: docId
      }
    })
      .limit(1)
      .exec((err, count) => {
        if (err) {
          return next(err);
        }
        if (count) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mã tài liệu đã tồn tại, vui lòng nhập mã khác',
            },
          });
        }
        next();
      });
  };

  const getStatusDocument = (next) => {
    try {

      let objUpdate = {
        doc_id: document.id,
        update_data: {}
      }
      if(name) {
        objUpdate.update_data.name = name
      }
      if(code) {
        objUpdate.update_data.doc_code = code
      }
      axios.post(`${serverChatbot}/api/2.0.0/documents/update`,objUpdate)
        .then(function (response) {
          if(response && response.data) {
            let objUpdateDoc = {

            }
            if(response.data.name) {
              objUpdateDoc.name = response.data.name
            }
            if(response.data.doc_code) {
              objUpdateDoc.code = response.data.doc_code
            }
            Document
              .update({
                _id: docId
              },
              objUpdateDoc
              ,(err,data) => {
                return next({
                  code: CONSTANTS.CODE.SUCCESS
                })
              })

          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            })
          }
          
        })
        .catch(function (err) {
          return next(err)
        })
    } catch (error) {
      next(error);
    }
  }

  async.waterfall([checkParams, getDocument, checkCodeExists, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
