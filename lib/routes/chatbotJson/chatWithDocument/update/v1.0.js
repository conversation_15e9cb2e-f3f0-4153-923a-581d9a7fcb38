const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const Conversation = require('../../../../models/conversationChatWithDocument');
const createConversationHandle = require('../createConversation/v1.0');
const User = require('../../../../models/user');
const Unit = require('../../../../models/unit');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body._id', '');
  const code = _.get(req, 'body.code', '');
  const name = _.get(req, 'body.name', '');
  const isShared = _.get(req, 'body.isShared', false);
  const selectedUnits = _.get(req, 'body.selectedUnits', []);

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId ) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    if(!name && !code) {
      return next({
        code: CONSTANTS.CODE.SUCCESS
      });
    }

    if (isShared && (!Array.isArray(selectedUnits) || selectedUnits.length === 0)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Nếu tài liệu được chia sẻ, phải cung cấp danh sách các đơn vị được phép truy cập.',
        },
      });
    }

    next();
  };

  const getDocument = (next) => {
    console.log(userId)
    Document
      .findOne({ _id: docId, member: userId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        document = result;
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        next()
      })
  }

  const checkCodeExists = (next) => {
    if (!code) {
      return next();
    }
    Document.count({
      code: code,
      _id: {
        $ne: docId
      }
    })
      .limit(1)
      .exec((err, count) => {
        if (err) {
          return next(err);
        }
        if (count) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mã tài liệu đã tồn tại, vui lòng nhập mã khác',
            },
          });
        }
        next();
      });
  };

  const checkPermissionsAndUnits = (next) => {
    if (!isShared) {
      return next();
    }

    User.findById(userId)
      .populate('permissions')
      .populate({
        path: 'groupPermissions',
        populate: {
          path: 'permissions'
        }
      })
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.UNAUTHORIZED,
            message: MESSAGES.AUTH.INVALID_TOKEN,
          });
        }

        let hasPermission = false;
        const permissionCodes = ['admin-all', 'document-manager'];
        // Kiểm tra quyền trực tiếp
        if (user.permissions && user.permissions.some(p => permissionCodes.includes(p.code))) {
          hasPermission = true;
        }

        // Kiểm tra quyền thông qua nhóm quyền
        if (!hasPermission && user.groupPermissions) {
          for (const gp of user.groupPermissions) {
            if (gp.permissions && gp.permissions.some(p => permissionCodes.includes(p.code))) {
              hasPermission = true;
              break;
            }
          }
        }

        if (!hasPermission) {
          return next({
            code: CONSTANTS.CODE.FORBIDDEN,
            message: {
              head: 'Thông báo',
              body: 'Bạn không có quyền chia sẻ tài liệu.',
            },
          });
        }

        Unit.find({ _id: { $in: selectedUnits } })
          .select('_id')
          .lean()
          .exec((err, existingUnits) => {
            if (err) {
              return next(err);
            }

            if (existingUnits.length !== selectedUnits.length) {
              const foundIds = existingUnits.map(unit => unit._id.toString());
              const invalidUnits = selectedUnits.filter(unitId => !foundIds.includes(unitId));
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: {
                  head: 'Thông báo',
                  body: `Các đơn vị sau không tồn tại hoặc không hợp lệ: ${invalidUnits.join(', ')}`,
                },
              });
            }

            next();
          });
      });
  };

  const getStatusDocument = (next) => {
    try {

      let objUpdate = {
        doc_id: document.id,
        update_data: {}
      }
      if(name) {
        objUpdate.update_data.name = name
      }
      if(code) {
        objUpdate.update_data.doc_code = code
      }
      axios.post(`${serverChatbot}/api/2.0.0/documents/update`,objUpdate)
        .then(function (response) {
          if(response && response.data) {
            let objUpdateDoc = {
              isShared: isShared,
              selectedUnits: selectedUnits,
            }
            if(response.data.name) {
              objUpdateDoc.name = response.data.name
            }
            if(response.data.doc_code) {
              objUpdateDoc.code = response.data.doc_code
            }
            Document
              .update({
                _id: docId
              },
              objUpdateDoc
              ,(err,data) => {
                return next({
                  code: CONSTANTS.CODE.SUCCESS
                })
              })

          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            })
          }

        })
        .catch(function (err) {
          return next(err)
        })
    } catch (error) {
      next(error);
    }
  }

  async.waterfall([checkParams, getDocument, checkCodeExists, checkPermissionsAndUnits, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
