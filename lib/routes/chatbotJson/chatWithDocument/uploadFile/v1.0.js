const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatWithDocument');
const Conversation = require('../../../../models/conversationChatWithDocument');
const Document = require('../../../../models/document');
const User = require('../../../../models/user');
const Unit = require('../../../../models/unit');
const Permission = require('../../../../models/permission');
const GroupPermission = require('../../../../models/groupPermission');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const ChatBotManager = require('../../../../job/chatStreamManager');
const DocumentStatusChecker = require('../../../../job/documentStatusChecker');
const tool = require('../../../../util/tool');
const path = require('path');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  let name = _.get(req, 'body.name', '');
  const file = _.get(req, 'file', '');
  let code = _.get(req, 'body.code', '');
  const isShared = _.get(req, 'body.isShared', false);
  const allowedUnits = _.get(req, 'body.allowedUnits', []);
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  const serverUploadFile = config.proxyRequestServer.serverUploadFile;

  const currentServerAddr = config.proxyRequestServer.currentServerAddr;
  let conversation;
  let documentId;
  let data;
  let response;
  let error = null;

  function normalizeFilename(originalName) {
    const ext = path.extname(originalName);
    const rawName = path.basename(originalName, ext);

    let safe = rawName
      .replace(/\s+/g, '_')
      .toLowerCase();
      safe = safe.toLowerCase();
      safe = safe.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
      safe = safe.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
      safe = safe.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
      safe = safe.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
      safe = safe.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
      safe = safe.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
      safe = safe.replace(/đ/g, 'd');
      safe = safe.replace(/[^a-zA-Z0-9_]/g, '');
    return `${safe}${ext}`;
  }
  const checkParams = (next) => {
    const MAX_SIZE_VIDEO_AUDIO = 500 * 1024 * 1024; // 500MB
    const MAX_SIZE_OTHER_FILES = 100 * 1024 * 1024; // 100MB
    if (file && file.mimetype) {
      file.originalname = normalizeFilename(file.originalname)
      const isVideoOrAudio = file.mimetype.startsWith('video/') || file.mimetype.startsWith('audio/');
      const maxSize = isVideoOrAudio ? MAX_SIZE_VIDEO_AUDIO : MAX_SIZE_OTHER_FILES;

      if (file.size > maxSize) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Dung lượng tệp vượt quá giới hạn cho phép (${isVideoOrAudio ? '500MB' : '100MB'}), vui lòng kiểm tra lại`,
          },
        });
      }
    }
    if (!file || !file.path || !file.originalname) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tệp tải lên không hợp lệ, vui lòng kiểm tra lại',
        },
      });
    }

    if (!name) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên tài liệu không được để trống',
        },
      });
    }

    if (!userId || !serverChatbot) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    if (isShared && (!Array.isArray(allowedUnits) || allowedUnits.length === 0)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Nếu tài liệu được chia sẻ, phải cung cấp danh sách các đơn vị được phép truy cập.',
        },
      });
    }

    next();
  };

  const checkCodeExists = (next) => {
    if (!code) {
      return next();
    }
    Document.count({
      code: code,
    })
      .limit(1)
      .exec((err, count) => {
        if (err) {
          return next(err);
        }
        if (count) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mã tài liệu đã tồn tại, vui lòng nhập mã khác',
            },
          });
        }
        next();
      });
  };

  const checkPermissionsAndUnits = (next) => {
    if (!isShared) {
      return next();
    }

    User.findById(userId)
      .populate('permissions')
      .populate({
        path: 'groupPermissions',
        populate: {
          path: 'permissions'
        }
      })
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.UNAUTHORIZED,
            message: MESSAGES.AUTH.INVALID_TOKEN,
          });
        }

        let hasPermission = false;
        // Kiểm tra quyền trực tiếp
        if (user.permissions && user.permissions.some(p => p.code === 'document-manager')) {
          hasPermission = true;
        }

        // Kiểm tra quyền thông qua nhóm quyền
        if (!hasPermission && user.groupPermissions) {
          for (const gp of user.groupPermissions) {
            if (gp.permissions && gp.permissions.some(p => p.code === 'document-manager')) {
              hasPermission = true;
              break;
            }
          }
        }

        if (!hasPermission) {
          return next({
            code: CONSTANTS.CODE.FORBIDDEN,
            message: {
              head: 'Thông báo',
              body: 'Bạn không có quyền chia sẻ tài liệu.',
            },
          });
        }

        Unit.find({ _id: { $in: allowedUnits } })
          .select('_id')
          .lean()
          .exec((err, existingUnits) => {
            if (err) {
              return next(err);
            }

            if (existingUnits.length !== allowedUnits.length) {
              const foundIds = existingUnits.map(unit => unit._id.toString());
              const invalidUnits = allowedUnits.filter(unitId => !foundIds.includes(unitId));
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: {
                  head: 'Thông báo',
                  body: `Các đơn vị sau không tồn tại hoặc không hợp lệ: ${invalidUnits.join(', ')}`,
                },
              });
            }

            next();
          });
      });
  };

  const upload = (next) => {
    try {
      const formData = new FormData();

      formData.append('user_id', userId);
      formData.append('category_id', 14);
      if(code) {
        formData.append('doc_code', code);
      }
      if (file) {
        formData.append('file', fs.createReadStream(file.path), file.originalname);
      }

      axios
        .post(`${serverUploadFile}/api/2.0.0/documents-conv/upload`, formData, {
          headers: {
            ...formData.getHeaders(),
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        })
        .then((response) => {
          name = name || response.data.name;
          let objCreate = {
            id: response.data.id,
            name,
            nameAlias: tool.change_alias(name),
            type: response.data.file_type,
            size: response.data.size_in_bytes,
            url: `${serverChatbot}${response.data.link}`,
            urlPreview: `${serverChatbot}/app/documents-preview/${response.data.name}`,
            urlDownload: `${serverChatbot}/api/2.0.0/documents/download/${response.data.id}`,
            member: userId,
            isShared: isShared,
            allowedUnits: allowedUnits,
          };
          if (code) {
            objCreate.code = code;
          }
          Document.create(objCreate, (err, result) => {
            if (err) {
              return next(err);
            }

            documentId = result._id;
            data = response.data;
            data.code = result.code;
            data._id = documentId;

            // Add document to status checker queue
            logger.logInfo(`Adding document ${documentId} to status checker queue from upload`);
            DocumentStatusChecker.addDocumentJob(documentId.toString());

            fs.unlink(file.path, (unlinkErr) => {
              if (unlinkErr) {
                console.error(`Failed to delete temporary file ${file.path}:`, unlinkErr);
              } else {
                console.log(`Successfully deleted temporary file: ${file.path}`);
              }
            });

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data,
              message: {
                head: 'Thông báo',
                body: `Tài liệu \"${result.name}\" đã được tải lên thành công`,
              },
            });
          });
        })
        .catch((err) => {
          next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Lỗi: ' + err.message,
            },
          });
        });
    } catch (error) {
      next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thông báo',
          body: 'Lỗi: ' + error.message,
        },
      });
    }
  };

  async.waterfall([
    checkParams,
    checkCodeExists,
    checkPermissionsAndUnits,
    upload,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
