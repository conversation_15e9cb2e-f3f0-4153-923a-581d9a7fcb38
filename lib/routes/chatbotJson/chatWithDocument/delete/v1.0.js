const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const Conversation = require('../../../../models/conversationChatWithDocument');
const createConversationHandle = require('../createConversation/v1.0');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body._id', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document
  let conversationId
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getDocument = (next) => {
    Document
      .findOne({ _id: docId, member: userId  })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        document = result;
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        next()
      })
  }


  const getStatusDocument = (next) => {
    try {
      axios.post(`${serverChatbot}/api/2.0.0/documents/delete`,{
        doc_id: document.id
      })
        .then(function (response) {
          if(response && response.data && response.data.message) {
            Document
              .update({
                _id: docId
              },{
                status: 'DELETED'
              },() => {
                next(null,{
                  code: CONSTANTS.CODE.SUCCESS
                })
              })
          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            })
          }
          
        })
        .catch(function (err) {
          return next(err)
        })
    } catch (error) {
      next(error);
    }
  }

  async.waterfall([checkParams, getDocument, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
