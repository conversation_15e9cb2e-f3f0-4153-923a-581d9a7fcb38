const _ = require('lodash');
const async = require('async');
const config = require('config');
const moment = require('moment');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const query = { member: userId };
  const name = _.get(req, 'body.name', '');
  const type = _.get(req, 'body.type', '');
  const startTime = _.get(req, 'body.startTime', '');
  const endTime = _.get(req, 'body.endTime', '');

  let count = 0;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    if (name) {
      query['$or'] = [
        {
          nameAlias: new RegExp(tool.change_alias(name), 'gi'),
        },
        {
          code: name,
        },
        {
          summary: new RegExp(name, 'gi'),
        },
        {
          title: new RegExp(name, 'gi'),
        },
      ];
    }
    if (startTime && endTime) {
      query.updatedAt = {
        $gte: moment(startTime).valueOf(),
        $lte: moment(endTime).valueOf(),
      };
    }
    if (type) {
      query.type = _.isArray(type) ? { $in: type } : type;
    }
    query.status = {
      $ne: 'DELETED'
    }
    next();
  };

  const countQuery = (next) => {
    Document.count(query)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);

        next();
      });
  };

  const listDocument = (next) => {
    Document.find(query)
      .sort({ updatedAt: -1 })
      .skip(limit * page)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, countQuery, listDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
