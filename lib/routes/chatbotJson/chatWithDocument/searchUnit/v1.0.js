const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit'); // Import Unit model

module.exports = (req, res) => {
  const query = _.get(req, 'body.query', ''); // Lấy query từ body

  async.waterfall([
    (next) => {
      if (!query) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tham số tìm kiếm không được để trống.',
          },
        });
      }
      next();
    },
    (next) => {
      // Tìm kiếm đơn vị theo tên, không phân biệt hoa thường
      Unit.find({ name: { $regex: query, $options: 'i' } })
        .select('_id name parent parentPath') // Chỉ trả về các trường cần thiết
        .lean()
        .exec((err, units) => {
          if (err) {
            return next(err);
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: units,
            message: {
              head: 'Thông báo',
              body: `Tìm thấy ${units.length} đơn vị phù hợp.`,
            },
          });
        });
    },
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
