const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit'); // Import Unit model
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const query = _.get(req, 'body.query', ''); // Lấy query từ body

  async.waterfall([
    (next) => {
      if (!query) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tham số tìm kiếm không được để trống.',
          },
        });
      }
      next();
    },
    (next) => {
      // Chuyển đổi query thành nameAlias để tìm kiếm chính xác hơn
      const queryAlias = tool.change_alias(query);

      // Tìm kiếm đơn vị theo nameAlias (đ<PERSON> được chuẩn hóa)
      const searchConditions = {
        nameAlias: { $regex: queryAlias },
        status: 1 // Chỉ tìm các unit đang hoạt động
      };

      Unit.find(searchConditions)
        .select('_id name nameAlias parent parentPath') // Thêm nameAlias vào kết quả
        .lean()
        .exec((err, units) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: units,
            message: {
              head: 'Thông báo',
              body: `Tìm thấy ${units.length} đơn vị phù hợp.`,
            },
          });
        });
    },
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
