const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit');
const User = require('../../../../models/user');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const textSearch = _.get(req, 'body.textSearch', '').trim();
  const userId = _.get(req, 'user.id', '');
  let userUnits = [];

  // Check input parameters
  const checkParams = (next) => {
    if (!textSearch) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'T<PERSON> số tìm kiếm không được để trống.',
        },
      });
    }

    if (!userId) {
      return next({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: {
          head: 'Thông báo',
          body: 'Không tìm thấy thông tin người dùng.',
        },
      });
    }

    next();
  };

  // Get user units and their descendants
  const getUserUnitsAndDescendants = (next) => {
    User.findById(userId)
      .select('units')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        if (!user) {
          return next({
            code: CONSTANTS.CODE.UNAUTHORIZED,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông tin người dùng.',
            },
          });
        }

        const directUnits = user.units.map(unit => unit.toString());
        userUnits.push(...directUnits);

        if (directUnits.length > 0) {
          // Find all descendant units
          Unit.find({ parentPath: { $in: directUnits } })
            .select('_id')
            .lean()
            .exec((err, descendantUnits) => {
              if (err) {
                return next(err);
              }

              descendantUnits.forEach(unit => {
                if (!userUnits.includes(unit._id.toString())) {
                  userUnits.push(unit._id.toString());
                }
              });

              next();
            });
        } else {
          next();
        }
      });
  };

  // Search units by nameAlias within user's scope
  const searchUnits = (next) => {
    // Chuyển đổi query thành nameAlias để tìm kiếm chính xác hơn
    const queryAlias = tool.change_alias(textSearch);

    // Tìm kiếm đơn vị theo nameAlias trong phạm vi units của user
    const searchConditions = {
      nameAlias: { $regex: queryAlias },
      status: 1, // Chỉ tìm các unit đang hoạt động
      _id: { $in: userUnits } // Chỉ tìm trong units mà user có quyền
    };

    Unit.find(searchConditions)
      .select('_id name nameAlias parent parentPath')
      .lean()
      .exec((err, units) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: units,
          message: {
            head: 'Thông báo',
            body: `Tìm thấy ${units.length} đơn vị phù hợp trong phạm vi của bạn.`,
          },
        });
      });
  };

  // Handle errors and send response
  const handleResponse = (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  };

  // Execute waterfall
  async.waterfall([
    checkParams,
    getUserUnitsAndDescendants,
    searchUnits
  ], handleResponse);
};
