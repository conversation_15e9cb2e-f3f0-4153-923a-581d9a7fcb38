const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit'); // Import Unit model
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const query = _.get(req, 'body.query', ''); // Lấy query từ body

  async.waterfall([
    (next) => {
      if (!query) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tham số tìm kiếm không được để trống.',
          },
        });
      }
      next();
    },
    (next) => {
      // Chuyển đổi query thành nameAlias để tìm kiếm chính xác hơn
      const queryAlias = tool.change_alias(query);

      // Tìm kiếm đơn vị theo nameAlias và name (để đảm bảo tìm được cả hai trường hợp)
      const searchConditions = {
        $or: [
          { nameAlias: { $regex: queryAlias } }, // Không cần 'i' vì nameAlias luôn lowercase
          { name: { $regex: query, $options: 'i' } } // Vẫn cần 'i' cho name vì có thể có chữ hoa
        ],
        status: 1 // Chỉ tìm các unit đang hoạt động
      };

      Unit.find(searchConditions)
        .select('_id name nameAlias parent parentPath') // Thêm nameAlias vào kết quả
        .lean()
        .exec((err, units) => {
          if (err) {
            return next(err);
          }

          // Sắp xếp kết quả: ưu tiên nameAlias khớp chính xác trước
          const sortedUnits = units.sort((a, b) => {
            const aNameAliasMatch = a.nameAlias && a.nameAlias.includes(queryAlias);
            const bNameAliasMatch = b.nameAlias && b.nameAlias.includes(queryAlias);

            if (aNameAliasMatch && !bNameAliasMatch) return -1;
            if (!aNameAliasMatch && bNameAliasMatch) return 1;
            return 0;
          });

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: sortedUnits,
            message: {
              head: 'Thông báo',
              body: `Tìm thấy ${sortedUnits.length} đơn vị phù hợp.`,
            },
          });
        });
    },
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
