const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationChatWithDocument');
const Chat = require('../../../../models/chatWithDocument');

module.exports = (req, res) => {
  const userId = req.user.id;
  const futureTimestamp = Date.now() + 100000;
  const from = _.toSafeInteger(req.body.from) || futureTimestamp;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);

  let conversations = [];

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const listConversation = (next) => {
    let query = {
      member: userId,
      updatedAt: {
        $lt: from,
      },
      inactive: {
        $ne: true,
      },
      documents: {
        $nin: [null, []],
      },
    };

    Conversation.find(query)
      .populate('documents')
      .sort('-updatedAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        conversations = results;

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: conversations,
        });
      });
  };

  async.waterfall([checkParams, listConversation], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
