const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatWithDocument');
const Conversation = require('../../../../models/conversationChatWithDocument');

module.exports = (req, res) => {
  const from = _.toSafeInteger(req.body.from) || Date.now() + 100000;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
  const conversation = req.body.conversation;
  let conversationInf;
  const checkParams = (next) => {
    if (!conversation) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const getConversation = (next) => {
    Conversation.findOne({
      _id: conversation,
    })
      .populate('documents')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(
            err || {
              code: CONSTANTS.CODE.WRONG_PARAMS,
            }
          );
        }
        conversationInf = result;
        next();
      });
  };

  const getChat = (next) => {
    Chat.find({
      conversation: conversation,
      error: null,
      createdAt: {
        $lt: from,
      },
    })
      .sort('-createdAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, results);
      });
  };

  async.waterfall([checkParams, getConversation, getChat], (err, results) => {
    if (err) {
      return res.json(err);
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: results,
      conversationInf,
    });
  });
};
