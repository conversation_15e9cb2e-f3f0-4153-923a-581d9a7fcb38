const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatWriteReport');
const Conversation = require('../../../../models/conversationWriteReport');
const UserNotifyModel = require('../../../../models/userNotify');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const PushNotifyManager = require('../../../../job/pushNotify');

// This is an internal API that doesn't require authentication
// It's used by the DocumentWaitingManager to process waiting jobs
module.exports = (req, res) => {
  // Get data from request
  const jobData = req.body;
  const userId = jobData.userId;
  const conversationId = jobData.conversationId;
  const style = jobData.style;
  const content = jobData.content;
  const page = jobData.page || 1;
  const numberWord = page * 500;
  const linkPushNoti = _.get(jobData, 'linkPushNoti', '/write-report');

  // Validate required fields
  if (!userId || !conversationId || !style || !content) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: 'Missing required fields'
    });
  }

  // Log the internal request
  logger.logInfo(`Processing internal request for conversation ${conversationId} from DocumentWaitingManager`);

  // Set up variables needed for processing
  let text = '';
  const isFile = true; // Since we're processing documents, this should be true
  text = `Dựa vào file, viết cho tôi báo cáo **chi tiết** khoảng ${numberWord} từ với các thông tin sau đây:\nNội dung báo cáo: ${content}\nVăn phong: ${style}`;

  let urlDownload = '';
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  let hasNotifiedClient = false;

  // Start the answering process
  ChatBotManager.answering(conversationId, jobData);
  PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);

  // Get the conversation data
  const getConversation = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .populate({
        path: 'documents',
        select: 'status name'
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: 'Conversation not found'
          });
        }

        conversation = data;
        next();
      });
  };

  // Check if all documents are completed
  const checkDocumentsStatus = (next) => {
    if (!conversation.documents || conversation.documents.length === 0) {
      return next({
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });
    }

    // Check if all documents are completed
    let allCompleted = true;

    for (const doc of conversation.documents) {
      if (doc.status !== 'COMPLETED') {
        allCompleted = false;
        logger.logError(
          ['Document not completed'],
          'processWaitingJob',
          { conversationId, documentId: doc._id, status: doc.status }
        );
        break;
      }
    }

    if (!allCompleted) {
      return next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Not all documents are completed'
      });
    }

    // Start the ask process
    ask();

    next({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Processing started'
    });
  };

  // Function to handle the ask process
  const ask = () => {
    response = '';
    const documentIds = conversation.documents ? conversation.documents.map(doc => doc._id) : [];
    const documentNames = conversation.documents ? conversation.documents.map(doc => doc.name).join(', ') : '';

    // Prepare the request options
    const options = {
      method: 'POST',
      url: `${serverChatbot}/api/2.0.0/chat_stream`,
      data: {
        conversation_id: conversation.id,
        text: text,
        documents: documentIds,
        user_id: userId,
      },
      responseType: 'stream',
    };

    // Make the request
    axios(options)
      .then((response) => {
        handleResponse(response);
      })
      .catch((err) => {
        handleError(err);
      });
  };

  // Handle the streaming response
  const handleResponse = (axiosResponse) => {
    axiosResponse.data.on('data', (chunk) => {
      if (chunk.toString() === 'data: [DONE]') {
        if (ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId);

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();
        }

        return;
      }

      try {
        const data = JSON.parse(chunk.toString());
        console.log('haha:data', data);
        const searchingArray = ['on_search', 'on_handle_process'];
        if (searchingArray.includes(data.event)) {
          io.to(conversationId).emit('searching', { text: data.message });
        } else if (data.event === 'message') {
          response += data.message;
          response = response.replace('undefined', '');
          response = response.replace('```markdown', '');
          response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

          ChatBotManager.saveAnswer(conversationId, response);
          if (!hasNotifiedClient) {
            PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
            hasNotifiedClient = true;
          }

          io.to(conversationId).emit('newMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: data.message,
            createdAt,
          });
        }
      } catch (err) {
        logger.logError([err], 'processWaitingJob:handleResponse', { conversationId, chunk: chunk.toString() });
      }
    });

    axiosResponse.data.on('end', () => {
      if (ChatBotManager.isStreaming(conversationId)) {
        if (!response || !response.trim()) {
          response = messageError;
          error = new Error('no response');
        }
        ChatBotManager.stop(conversationId);

        pushNotify(error);
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    });

    axiosResponse.data.on('error', (err) => {
      handleError(err);
    });
  };

  // Handle errors
  const handleError = (err) => {
    if (ChatBotManager.isStreaming(conversationId)) {
      error = err;
      response = messageError;
      ChatBotManager.stop(conversationId);

      pushNotify(error);
      io.to(conversationId).emit('endMessage', {
        _id,
        member: userId,
        id: conversationId,
        conversation: conversation._id,
        question: text,
        answer: response,
        createdAt,
      });

      createCollection();
    }
  };

  // Create the chat collection entry
  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        content,
        page,
        style,
        createdAt,
        urlDownload
      },
      (err, result) => {
        console.log('haha:writeReport:createCollection', err, result);
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => { }
        );
      }
    );
  };

  const pushNotify = (error) => {
    const notification = {
      title: 'Thông báo',
      message: `Lập báo cáo ${error ? 'thất bại' : 'đã hoàn thành'}.`,
      data: {
        link: linkPushNoti,
        extras: {
          id: conversationId
        }
      }
    }

    PushNotifyManager.sendToMemberAssistant(userId.toString(), notification.title, notification.message, notification.data, 'conversation_update');
    UserNotifyModel
      .create({
        user: userId,
        title: notification.title || '',
        message: notification.message || '',
        data: notification.data || {},
      })
  }

  // Execute the process
  async.waterfall([getConversation, checkDocumentsStatus], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    // Return success immediately
    res.json(data || err);
  });
};
