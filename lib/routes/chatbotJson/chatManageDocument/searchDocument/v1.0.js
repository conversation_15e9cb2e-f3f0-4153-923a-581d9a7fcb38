const _ = require('lodash');
const async = require('async');
const config = require('config');
const axios = require('axios');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const keyword = _.get(req, 'body.keyword', '');
  const type = _.get(req, 'body.type', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;

  let count = 0;
  let documents;
  const checkParams = (next) => {
    if (!userId || !keyword) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const aiSearch = (next) => {
    axios
      .post(`${serverChatbot}/api-rag/search_with_document`, {
        user_heyu_id: userId,
        query: keyword.trim(),
        page: page + 1,
        size: limit,
        category_id: 16,
        top_k: 10
      })
      .then((response) => {
        let data = response.data;
        if (!data) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: documents,
            count,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy file nào phù hợp nội dung tìm kiếm',
            },
          });
        }
        documents = _.get(data, 'documents', []);
        next(null,
          {
            code: CONSTANTS.CODE.SUCCESS,
            data: documents,
            count
          }
        );
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: {
              head: 'Thông báo',
              body: _.get(err.response, 'data.message', MESSAGES.SYSTEM.ERROR),
            },
          });
        }
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });
      });
  };

  
  async.waterfall([checkParams, aiSearch], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
