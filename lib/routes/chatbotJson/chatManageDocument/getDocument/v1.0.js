const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const Conversation = require('../../../../models/conversationChatWithDocument');
const createConversationHandle = require('../createConversation/v1.0');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body.docId', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };


  const getStatusDocument = (next) => {
    try {
      axios.get(`${serverChatbot}/api/2.0.0/documents/status/${docId}`)
        .then(function (response) {
          if(response && response.data) {
          
            document = response.data.data;
            if(document) {
              document.link = `${config.proxyRequestServer.serverChatBot}${document.link}`
            }
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: document
            })
            
          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            })
          }
          
        })
        .catch(function (err) {
          return next(err)
        })
    } catch (error) {
      next(error);
    }
  }

  async.waterfall([checkParams, getStatusDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
