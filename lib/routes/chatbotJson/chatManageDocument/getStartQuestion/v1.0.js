const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const Conversation = require('../../../../models/conversationManageDocument');
const Chat = require('../../../../models/chatManageDocument');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.conversation', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation, question;
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    next();
  };

  const getConversation = (next) => {
    Conversation.findById(conversationId)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR,
          });
        }
        conversation = result;
        if (!conversation.documents || !conversation.documents.length) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: {
              head: 'Thông báo',
              body: 'Không thể lấy câu hỏi mở đầu do trò chuyện chưa được đính kèm Tài liệu',
            },
          });
        }
        next();
      });
  };

  const checkChatExist = (next) => {
    Chat.findOne({ conversation: conversationId })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (result) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
          });
        }
        next();
      });
  };

  const getStatusDocument = (next) => {
    try {
      axios
        .post(`${serverChatbot}/api/2.0.0/get-start-question`, {
          conversion_id: conversation.id,
        })
        .then((response) => {
          if (response && response.data && response.data.start_question) {
            question = response.data.start_question;
            return next();
          }
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const createFirstChat = (next) => {
    Chat.create({ conversation: conversationId, answer: question }, (err, result) => {
      if (err) {
        return next(err);
      }
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: result,
      });
    });
  };

  async.waterfall([checkParams, getConversation, checkChatExist, getStatusDocument, createFirstChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
