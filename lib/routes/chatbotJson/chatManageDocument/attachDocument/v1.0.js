const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const createConversationHandle = require('../createConversation/v1.0');
const FormData = require('form-data');
const axios = require('axios');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const docId = _.get(req, 'body.docId', '');
  const type = _.get(req, 'body.type', '');
  const conversationId = _.get(req, 'body.conversationId', '');
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let document;
  let conversation;
  let Conversation;
  const checkParams = (next) => {
    if (!userId || !serverChatbot || !docId || !type || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }
    switch (type) {
      case 'chat-manage-document':
        Conversation = ConversationManageDocumentModel;
        break;
      case 'summary-manage-document':
        Conversation = ConversationSummaryManageDocumentModel;
        break;
      case 'speech-manage-document':
        Conversation = ConversationSpeechManageDocumentModel;
        break;
      case 'write-report-manage-document':
        Conversation = ConversationWriteReportManageDocumentModel;
        break;
      default:
        break;
    }
    next();
  };

  const getDocument = (next) => {
     try {
      axios.get(`${serverChatbot}/api/2.0.0/documents/status/${docId}`)
        .then(function (response) {
          if(response && response.data) {
            // if(response.data && response.data.data && response.data.data.status && response.data.data.status !== 'COMPLETED') {
            //   return next({
            //     code: CONSTANTS.CODE.SYSTEM_ERROR,
            //     message: {
            //       head: 'Thông báo',
            //       body: `Tài liệu \"${response.data.data.name}\" chưa được xử lý thành công. Bạn vui lòng đợi trong giây lát và thử lại sau`,
            //     },
            //   });
            // }
            document = response.data.data
            next();
          } else {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            })
          }
        })
        .catch(function (err) {
          return next(err)
        })
    } catch (error) {
      next(error);
    }
  };

  const findConversation = (next) => {
    Conversation.findById(conversationId)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        console.log(err, result)

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR,
          });
        }
        conversation = result;

        next();
      });
  };

  const attachDocument = (next) => {
    try {
      const formData = new FormData();
      formData.append('document_id', document.id);
      formData.append('con_id', conversation.id);
      formData.append('agent_impl_id', conversation.agents_impl_id);

      axios
        .post(`${serverChatbot}/api/2.0.0/documents-conv/attach`, formData, {
          headers: {
            ...formData.getHeaders(),
          },
        })
        .then((response) => {
          if (!response || response.status !== 200) {
            return next({ code: response.status });
          }

          Conversation.findOneAndUpdate({ _id: conversationId }, { $addToSet: { documents: document.id }, updatedAt: Date.now() })
            .lean()
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next({
                code: CONSTANTS.CODE.SUCCESS,
                data: document,
              });
            });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([checkParams, getDocument, findConversation, attachDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
