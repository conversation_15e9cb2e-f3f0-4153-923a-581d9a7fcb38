const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationManageDocument');
const axios = require('axios')
module.exports = (req, res) => {
  const userId = req.user.id;
  const futureTimestamp = Date.now() + 100000;
  const from = _.toSafeInteger(req.body.from) || futureTimestamp;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
  const serverChatbot = config.proxyRequestServer.serverChatBot;

  let conversations = [];

  const checkParams = (next) => {
    if (!userId || !serverChatbot) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const listConversation = (next) => {
    let query = {
      member: userId,
      updatedAt: {
        $lt: from,
      },
      inactive: {
        $ne: true,
      },
      documents: {
        $nin: [null, []],
      },
    };

    Conversation.find(query)
      .sort('-updatedAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        conversations = results;

        next();
      });
  };

  const findFile = (next) => {
    async.eachSeries(conversations,(conversation, done) => {
      if(!conversation.documents || !conversation.documents.length) {
        return done()
      }
      conversation.documentArr = []
      async.eachSeries(conversation.documents, (doc, cb) => {
        try {
          axios.get(`${serverChatbot}/api/2.0.0/documents/status/${doc}`)
            .then(function (response) {
              if(response && response.data) {
                conversation.documentArr.push(response.data.data)
              } 
              cb();
            })
            .catch(function (err) {
              return cb(err)
            })
        } catch (error) {
          return cb(error);
        }
      },(err) => {
        if(err) {
          return done(err)
        }
        conversation.documents = conversation.documentArr;
        delete conversation.documentArr
        done()
      })
    },(err) => {
      if(err) {
        return next(err)
      }
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: conversations,
      });
    })
  }

  async.waterfall([checkParams, listConversation, findFile], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
