const _ = require('lodash');
const async = require('async');
const axios = require('axios');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatManageDocument');
const Conversation = require('../../../../models/conversationManageDocument');

module.exports = (req, res) => {
  const from = _.toSafeInteger(req.body.from) || Date.now() + 100000;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
  const conversation = req.body.conversation;
  const serverChatbot = config.proxyRequestServer.serverChatBot;

  let conversationInf;
  let data;
  const checkParams = (next) => {
    if (!conversation) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const getConversation = (next) => {
    Conversation.findOne({
      _id: conversation,
    })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(
            err || {
              code: CONSTANTS.CODE.WRONG_PARAMS,
            }
          );
        }
        conversationInf = result;
        next();
      });
  };

  const findFile = (next) => {
    conversationInf.documentArr = [];
    async.eachSeries(conversationInf.documents, (doc, cb) => {
      try {
        axios.get(`${serverChatbot}/api/2.0.0/documents/status/${doc}`)
          .then(function (response) {
            if(response && response.data) {
              conversationInf.documentArr.push(response.data.data)
            }
            cb();
          })
          .catch(function (err) {
            return cb(err)
          })
      } catch (error) {
        return cb(error);
      }
    },(err) => {
      if(err) {
        return next(err)
      }
      conversationInf.documents = conversationInf.documentArr;
      delete conversationInf.documentArr
      next()
    })
  }

  const getChat = (next) => {
    Chat.find({
      conversation: conversation,
      error: null,
      createdAt: {
        $lt: from,
      },
    })
      .sort('-createdAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        data = results;
        next(null);
      });
  };

  async.waterfall([checkParams, getConversation, findFile, getChat], (err, results) => {

    if (err) {
      return res.json(err);
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data,
      conversationInf,
    });
  });
};
