const _ = require('lodash');
const async = require('async');
const config = require('config');
const moment = require('moment');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Document = require('../../../../models/document');
const tool = require('../../../../util/tool');
const axios = require('axios');

module.exports = (req, res) => {
  // const userId = 'test' || _.get(req, 'user.id', '');
  let userId =  _.get(req, 'user.id', '');
  if(['67bc4c0f8c3c692c1d88ab90','677245dd8291841c0637b060', '676aaf7997c723498bc9a069', '677748e2138ffd10202ec6da'].includes(userId)) {
    userId = 'test'
  }
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', '');
  const name = _.get(req, 'body.name', '');
  const code = _.get(req, 'body.code', '');
  const startTime = _.get(req, 'body.startTime', '');
  const endTime = _.get(req, 'body.endTime', '');

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    if(!page || typeof page !== 'number' || page && page <= 0 ) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const listDocument = (next) => {
    let body = {
      user_id: userId,
      category_id: 16,
      page: page,
      per_page: limit
    }
    if(name) {
      body.name = name
    }
    if(code) {
      body.doc_code = code
    }
    if(startTime && endTime) {
      body.create_at_from = moment(startTime).format('YYYY-MM-DD');
      body.create_at_to = moment(endTime).format('YYYY-MM-DD');
    }
    axios.post(`${config.proxyRequestServer.serverChatBot}/api/2.0.0/documents-by-user-category`, body)
    .then(function (response) {
      if(response && response.data && response.data.documents) {
        response.data.documents.map(doc => {
          if(doc.link) {
            doc.link = `${config.proxyRequestServer.serverChatBot}${doc.link}`
          }
        })
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          data: response.data.documents,
          totalPage: response.data.total_pages || 0
        })
      }
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR
      })
    })
    .catch(function (error) {
      next(error)
    })
  };

  async.waterfall([checkParams, listDocument], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
