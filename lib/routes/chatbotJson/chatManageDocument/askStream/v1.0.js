const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const rp = require('request-promise');
const Chat = require('../../../../models/chatManageDocument');
const Conversation = require('../../../../models/conversationManageDocument');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const { checkStringByCondition } = require('../../../../util/tool');

module.exports = (req, res) => {
  const conversationId = req.body.id;
  const userId = req.user.id;
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let text = _.get(req, 'body.text', '');
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  let answeredAt;
  const urlChatbot = `/api/2.0.0/chat_stream`;

  ChatBotManager.answering(conversationId);
  const checkParams = (next) => {
    console.log('conversationId', userId, serverChatbot, conversationId);

    if (!userId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    next();
  };

  const checkConversationExists = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        if (!data) {
          return next({
            code: 400,
          });
        }
        conversation = data;
        ask();
        next(null, { code: 200 });
      });
  };

  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}${urlChatbot}`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      const stream = resAxios.data;
      let buffer = '';
      let totalSend = '';
      resAxios.data.on('data', (chunk) => {
        if (!response) {
          answeredAt = Date.now();
        }

        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt,
            });
            createCollection();
          }
          return;
        }
        try {
          const data = JSON.parse(chunk.toString());
          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          console.error('=============== err', chunk.toString(), e);
          let text = chunk.toString();
          if (text.startsWith(`{"event": "message", "message": "`)) {
            response += text;
          }

          if (text.endsWith(`"}`)) {
            response += text;
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if (ChatBotManager.isStreaming(conversationId)) {
          if (!response || !response.trim()) {
            response = messageError;
            error = new Error('no response');
          }
          ChatBotManager.stop(conversationId);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });
          createCollection();
        }
      });
      stream.on('error', (err) => {
        if (ChatBotManager.isStreaming(conversationId)) {
          error = err;
          response = messageError;
          ChatBotManager.stop(conversationId);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });
          createCollection();
        }
      });
    } catch (err) {
      if (ChatBotManager.isStreaming(conversationId)) {
        error = err;
        response = messageError;
        ChatBotManager.stop(conversationId);
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    }
  };

  const createCollection = (next) => {
    Chat.create(
      {
        _id,
        member: userId,
        id: conversationId,
        conversation: conversation._id,
        question: text,
        answer: response,
        answeredAt,
        createdAt,
        error,
        urlChatbot,
      },
      (err, result) => {
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => {}
        );
      }
    );
  };

  async.waterfall([checkParams, checkConversationExists], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });
    res.json(data || err);
  });
};
