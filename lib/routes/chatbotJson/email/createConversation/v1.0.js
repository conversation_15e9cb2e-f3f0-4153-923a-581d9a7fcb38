const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const rp = require('request-promise')
const Conversation = require('../../../../models/conversationEmail')
const Chat = require('../../../../models/chatEmail')
const AgentManager = require('../../../../job/agentManager')

module.exports = (req, res) => {

  const userId = req.user.id;
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversationId
  let oldConversationId
  const oneHourInMs = ms('1h');

  const checkParams = (next) => {
    if (!userId || !serverChatbot) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const getLastConversation = (next) => {
    Conversation
      .findOne({
        member: userId,
        inactive: {
          $ne: true
        }
      }, 'updatedAt')
      .sort({ updatedAt: -1 })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        // if (data && data.updatedAt) {
        //   const now = Date.now();
        //   let diff = now - data.updatedAt;
        //   if (diff < oneHourInMs) {
        //     return next({
        //       code: CONSTANTS.CODE.SUCCESS,
        //       data: data._id
        //     })
        //   }
        // }

        if (data) {
          oldConversationId = data._id;
        }

        next();
      })
  }

  const getLastChatConversation = (next) => {
    if (!oldConversationId) {
      return next();
    }

    Chat
      .findOne({
        member: userId,
        conversation: oldConversationId
      }, '_id')
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: oldConversationId
          })
        }

        next();
      })
  }

  const createConversation = (next) => {
    const options = {
      method: "POST",
      uri: `${serverChatbot}/api/2.0.0/create-conversation`,
      body: {
        user_id: userId,
        agents_impl_id: AgentManager.getAgentValue('email', false),
        is_dev: AgentManager.isAgentDev('email')
      },
      timeout: 14000,
      json: true, // Automatically stringifies the body to JSON
    }

    rp(options)
      .then((result) => {
        if (result && result.id) {
          conversationId = result.id
          next();
        } else {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
          })
        }
      })
      .catch((error) => {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
          error: error
        })
      })
  }

  const createCollection = (next) => {
    Conversation
      .create({
        id: conversationId,
        member: userId,
        agents_impl_id: AgentManager.getAgentValue('email', false),
        is_dev: AgentManager.isAgentDev('email')
      }, (err, result) => {
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          data: result._id
        })
      })
  }

  async.waterfall([
    checkParams,
    getLastConversation,
    getLastChatConversation,
    createConversation,
    createCollection
  ], (err, data) => {

    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
