const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Chat = require('../../../../models/chatEmail')
const Conversation = require('../../../../models/conversationEmail')
const UserModel = require('../../../../models/user')
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const role = _.get(req, 'body.role', '');
  const position = _.get(req, 'body.position', '');
  const name = _.get(req, 'body.name', '');
  const title = _.get(req, 'body.title', '');
  const content = _.get(req, 'body.content', '');
  const numberWord = _.get(req, 'body.numberWord', '');

  let text = '';

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response
  const createdAt = Date.now()
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR
  let error = null;
  ChatBotManager.answering(conversationId)

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId || !role || !position || !name || !title || !content || !numberWord) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    text = `Viết cho tôi email với các thông tin sau đây:\nTiêu đề email: ${title}\nNội dung email: ${content}\nTên người nhận: ${name}\nVai trò: ${role}\nChức vụ: ${position}\nSố lượng từ: ${numberWord} từ`;
    console.log('haha:text', text)

    next();
  }

  const getUserInf = (next) => {
    if (!userId) {
      return next();
    }

    UserModel
      .findOne({ _id: userId })
      .select('name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        if (user && user.name) {
          text += `\nNgười gửi: ${user.name}`;
        }

        next();
      });
  }

  const checkConversationExists = (next) => {
    Conversation
      .findOne({
        _id: conversationId
      })
      .lean()
      .exec((err, data) => {
        console.log('haha:err', err, data, conversationId)
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
          })
        }

        conversation = data

        ask();

        next(null, { code: CONSTANTS.CODE.SUCCESS });
      })
  }

  function decodeUnicode(str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, (match, group) =>
        String.fromCharCode(parseInt(group, 16))
    );
  }

  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          "conversation_id": conversation.id,
          text
        },
        timeout:300000
      });

      const stream = resAxios.data;
      resAxios.data.on('data', (chunk) => {
        console.log('haha:chunk', chunk.toString())
        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt
            });

            createCollection();
          }

          return;
        }

        try {
          const data = JSON.parse(chunk.toString())
          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '')
            response = response.replace('```markdown', '')
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt
            });
          }
        } catch (e) {
          console.error("=============== err",chunk.toString(),e);
          let text = chunk.toString();
          if(text.startsWith(`{"event": "message", "message": "`)) {
            response+= text
          }

          if(text.endsWith(`"}`)) {
            response+= text
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if (ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId)

          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt
          });

          createCollection()
        }
      });
      stream.on('error', (err) => {
        if(ChatBotManager.isStreaming(conversationId)) {
          error = err
          response = messageError
          ChatBotManager.stop(conversationId)

          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt
          });

          createCollection()
        }
      });
    } catch (err) {
      if (ChatBotManager.isStreaming(conversationId)) {
        error = err
        response = messageError
        ChatBotManager.stop(conversationId)

        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt
        });

        createCollection()
      }
    }
  }

  const createCollection = () => {
    Chat
      .create({
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        title,
        content,
        numberWord,
        role,
        name,
        position,
        createdAt
      }, (err, result) => {
        console.log('haha:createCollection', err, result)
        Conversation
          .update({
            _id: conversation._id,
          }, {
            hasMessage: true,
            updatedAt: Date.now(),
          }, () => { })
      })
  }

  async.waterfall([
    checkParams,
    getUserInf,
    checkConversationExists,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
