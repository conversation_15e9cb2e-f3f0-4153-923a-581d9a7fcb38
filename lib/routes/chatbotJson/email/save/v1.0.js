const _ = require('lodash')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Conversation = require('../../../../models/conversationEmail')

module.exports = (req, res) => {
  const _id = req.body._id;
  const userId = req.user.id;

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const deleteConversation = (next) => {
    Conversation
      .update({
        _id,
        member: userId
      }, {
        inactive: false
      }, (err, result) => {
        next({
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }


  async.waterfall([
    checkParams,
    deleteConversation
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
