const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatEvaluation');
const Conversation = require('../../../../models/conversationEvaluation');
const UserModel = require('../../../../models/user');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const rp = require('request-promise');
const AgentManager = require('../../../../job/agentManager')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  let conversationId;

  let text = '';
  let urlDownload = '';
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  const type = _.get(req, 'body.type', '');
  const result = _.get(req, 'body.result', '');
  const typeArr = [
    {
      variable: 'staff',
      name: 'nhân viên',
    },
    {
      variable: 'leader',
      name: 'lãnh đạo',
    },
    {
      variable: 'department',
      name: 'phòng ban',
    },
  ];
  let typeSelect;

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !type || !result) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    typeSelect = typeArr.find((item) => item.variable === type);
    if (!typeSelect) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn lọc theo cán bộ/lãnh đạo/phòng ban trước khi đánh giá',
        },
      });
    }

    text = `Hãy đánh giá kết quả làm việc xử lý của các ${typeSelect.name} theo nội dung sau: ${result}`;

    console.log('haha:text', text);

    next();
  };

  const createConversation = (next) => {
    const options = {
      method: 'POST',
      uri: `${serverChatbot}/api/2.0.0/create-conversation`,
      body: {
        agents_impl_id: AgentManager.getAgentValue('evaluation', false),
        is_dev: AgentManager.isAgentDev('evaluation'),
        user_id: userId,
      },
      timeout: 14000,
      json: true, // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        if (result && result.id) {
          const objCreate = {
            id: result.id,
            member: userId,
            agents_impl_id: AgentManager.getAgentValue('evaluation', false),
            is_dev: AgentManager.isAgentDev('evaluation')
          };

          Conversation.create(objCreate, (err, res) => {
            if (err) {
              return next(err);
            }
            if (!res) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
              });
            }
            conversationId = res._id;
            conversation = res;
            ChatBotManager.answering(conversationId);
            ask();
            next(null, { code: CONSTANTS.CODE.SUCCESS });
          });
        } else {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }
      })
      .catch((error) => {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          error: error,
        });
      });
  };

  const ask = async (next) => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      const stream = resAxios.data;
      resAxios.data.on('data', (chunk) => {
        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            if(!response || !response.trim()) {
              response = messageError;
              error = new Error('no response');
            }
            const lines = response.trim().split('\n');
            const lastLine = lines.pop().trim();
            response = lines.join('\n').trim();
            const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
            urlDownload = linkMatch ? linkMatch[2] : '';

            if (!urlDownload) {
              response += '\n' + lastLine;
            }
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt,
              urlDownload,
            });

            createCollection();
          }

          return;
        }

        try {
          const data = JSON.parse(chunk.toString());

          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            console.log('ahihi data', data.message);

            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          console.error('=============== err', chunk.toString(), e);
          let text = chunk.toString();
          if (text.startsWith(`{"event": "message", "message": "`)) {
            response += text;
          }

          if (text.endsWith(`"}`)) {
            response += text;
            response = response.replace('undefined', '');
            response = _.get(JSON.parse(response), 'message', '');
          }
        }
      });
      stream.on('end', () => {
        if (ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId);
          if(!response || !response.trim()) {
            response = messageError;
            error = new Error('no response');
          }
          const lines = response.trim().split('\n');
          const lastLine = lines.pop().trim();
          response = lines.join('\n').trim();
          const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
          urlDownload = linkMatch ? linkMatch[2] : '';

          if (!urlDownload) {
            response += '\n' + lastLine;
          }
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
            urlDownload,
          });

          createCollection();
        }
      });
      stream.on('error', (err) => {
        if (ChatBotManager.isStreaming(conversationId)) {
          error = err;
          response = messageError;
          ChatBotManager.stop(conversationId);

          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();
        }
      });
    } catch (err) {
      if (ChatBotManager.isStreaming(conversationId)) {
        error = err;
        response = messageError;
        ChatBotManager.stop(conversationId);

        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    }
  };

  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        urlDownload,
        createdAt,
      },
      (err, result) => {
        console.log('haha:createCollection', err, result);
        res.json(
          err || {
            code: CONSTANTS.CODE.SUCCESS,
            data: result,
          }
        );
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => {}
        );
      }
    );
  };

  async.waterfall([checkParams, createConversation], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    // res.json(data || err);
  });
};
