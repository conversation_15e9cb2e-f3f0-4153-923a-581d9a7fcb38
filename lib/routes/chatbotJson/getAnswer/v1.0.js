const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ChatBotManager = require('../../../job/chatStreamManager')

module.exports = (req, res) => {
  const conversationId = req.body.id;

  const checkParams = (next) => {
    if (!conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getAnswer = (next) => {
    ChatBotManager.getAnswer(conversationId)
      .then((response) => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: response
        });
      })
      .catch((err) => {
        next(err);
      })
  }

  async.waterfall([
    checkParams,
    getAnswer,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
