const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Conversation = require('../../../../models/conversationITT');
const Chat = require('../../../../models/chatITT');
const ChatBotManager = require('../../../../job/chatStreamManager');

const getConversationInfo = (conversation, callback) => {
  let info = {};
  if (!conversation) {
    return callback(info);
  }

  if (mongoose.isValidObjectId(conversation.member)) {
    UserModel.findOne({ _id: conversation.member })
      .select('name phone')
      .lean()
      .exec((err, user) => {
        if (err) {
          return callback();
        }

        if (user) {
          info = user;
          callback(info);
        }
      });
  } else {
    TrackingActionModel.findOne({ 'otherInf.uniqueId': conversation.member })
      .select('otherInf member')
      .populate('member', 'name phone')
      .lean()
      .exec((err, tracking) => {
        if (err) {
          return callback();
        }

        if (tracking) {
          info = {
            name: _.get(tracking, 'otherInf.platform', '') + ' - ' + _.get(tracking, 'member.name', ''),
            phone: _.get(tracking, 'member.phone', ''),
          };
        }

        callback(info);
      });
  }
};

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const futureTimestamp = Date.now() + 100000;
  const from = _.toSafeInteger(req.body.from) || futureTimestamp;
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
  const page = _.toSafeInteger(req.body.page) || 0;

  let conversations = [];
  let total = 0;
  let query = {
    member: userId,
    inactive: {
      $ne: true,
    },
  };

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const listConversation = (next) => {
    if (userId === 'all') {
      delete query.member;
      query.updatedAt = { $lt: from };
    }

    // First, get the newest conversation regardless of whether it has messages
    Conversation
      .findOne({...query})
      .populate('documents')
      .sort('-updatedAt')
      .lean()
      .exec((err, newestConversation) => {
        if (err) {
          return next(err);
        }

        // Now get conversations with messages (updatedAt > createdAt)
        const messageQuery = {
          ...query,
          $expr: { $gt: ["$updatedAt", "$createdAt"] }
        };

        let skip = page * limit;
        if (page !== 0 && newestConversation && newestConversation.updatedAt <= newestConversation.createdAt && !newestConversation.hasMessage) {
          skip -= 1;
        }

        Conversation
          .find(messageQuery)
          .populate('documents')
          .sort('-updatedAt')
          .limit(limit)
          .skip(skip)
          .lean()
          .exec((err, results) => {
            if (err) {
              return next(err);
            }

            // If we have a newest conversation and it's not in the results, add it
            if (newestConversation && page === 0) {
              // Check if the newest conversation is already in the results
              const newestConversationInResults = results.some(conv =>
                conv._id.toString() === newestConversation._id.toString()
              );

              if (!newestConversationInResults) {
                // Add the newest conversation at the beginning
                results.unshift(newestConversation);

                // If we've exceeded the limit, remove the last item
                if (results.length > limit) {
                  results.pop();
                }
              }
            }

            conversations = results;
            next();
          });
      });
  };

  const countConversationsWithChatHistory = (next) => {
    if (userId === 'all') {
      return next();
    }

    // First, check if the newest conversation has messages
    Conversation
      .findOne({...query})
      .sort('-updatedAt')
      .lean()
      .exec((err, newestConversation) => {
        if (err) {
          return next(err);
        }

        // Count conversations where updatedAt > createdAt (has messages)
        Conversation.countDocuments({
          ...query,
          $expr: { $gt: ["$updatedAt", "$createdAt"] }
        }, (err, count) => {
          if (err) {
            return next(err);
          }

          // If the newest conversation doesn't have messages (updatedAt <= createdAt),
          // we need to add 1 to the total count since we're including it in the results
          if (newestConversation && newestConversation.updatedAt <= newestConversation.createdAt) {
            total = count + 1;
          } else {
            total = count;
          }

          next();
        });
      });
  };

  const getLastChat = (next) => {
    async.each(
      conversations,
      (conversation, callback) => {
        ChatBotManager.getAnswer(conversation._id, 'imageToText')
          .then((answer) => {
            conversation.status = answer.status || 'PENDING';

            if (conversation.updatedAt <= conversation.createdAt && !conversation.hasMessage) {
              conversation.newConversation = 1;
              conversation.lastMessage = answer.bodyData && answer.bodyData.topic ? answer.bodyData.topic : 'Cuộc trò chuyện mới';
            }

            if (conversation.documents && conversation.documents.length && conversation.documents[0].name) {
              conversation.lastMessage = conversation.documents[0].name;

              getConversationInfo(conversation, (info) => {
                conversation.info = info;
                callback();
              });
            } else {
              if (conversation.newConversation) {
                getConversationInfo(conversation, (info) => {
                  conversation.info = info;
                  callback();
                });

                return;
              }

              Chat.findOne({ conversation: conversation._id })
                .sort('-createdAt')
                .lean()
                .exec((err, message) => {
                  if (err) {
                    return callback(err);
                  }

                  conversation.lastMessage = message && message.content ? message.content : 'Cuộc trò chuyện mới';
                  getConversationInfo(conversation, (info) => {
                    conversation.info = info;
                    callback();
                  });
                });
            }
          });
      },
      (err) => {
        if (err) {
          return next(err);
        }

        let returnData = {
          code: CONSTANTS.CODE.SUCCESS,
          data: conversations,
          total
        };

        if (_.isEmpty(conversations) && req.body.from) {
          returnData.message = {
            head: 'Thông báo',
            body: 'Không còn cuộc trò chuyện nào',
          };
        }

        next(returnData);
      }
    );
  };

  async.waterfall([checkParams, listConversation, countConversationsWithChatHistory, getLastChat], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
