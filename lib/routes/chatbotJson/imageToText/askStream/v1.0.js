const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatITT');
const Conversation = require('../../../../models/conversationITT');
const UserModel = require('../../../../models/user');
const UserNotifyModel = require('../../../../models/userNotify');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const PushNotifyManager = require('../../../../job/pushNotify');
const DocumentWaitingManager = require('../../../../job/documentWaitingManager');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const conversationId = _.get(req, 'body.id', '');
  const content = _.get(req, 'body.content', '');
  const linkPushNoti = _.get(req, 'body.linkPushNoti', '/convert-image-to-text');
  let text = '';

  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  let hasNotifiedClient = false;
  req.body.modelType = 'imageToText';
  req.body.member = userId;
  ChatBotManager.answering(conversationId, req.body);
  let urlDownload = '';

  const checkParams = (next) => {
    if (!userId || !serverChatbot || !conversationId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    text = `Chuyển file thành văn bản cho tôi.`;

    next();
  };

  const getUserInf = (next) => {
    if (!userId) {
      return next();
    }

    UserModel.findOne({ _id: userId })
      .select('name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        next();
      });
  };

  const checkConversationExists = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .populate('documents')
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
          });
        }

        conversation = data;

        // Check if there are documents and if they are all completed
        if (conversation.documents && conversation.documents.length > 0) {
          let allCompleted = true;
          const processingDocuments = [];

          for (const doc of conversation.documents) {
            if (doc.status !== 'COMPLETED') {
              allCompleted = false;
              processingDocuments.push(doc.name || 'Tài liệu đang xử lý');
            }
          }

          if (allCompleted) {
            ask();
            next(null, { code: CONSTANTS.CODE.SUCCESS });
          } else {
            // Store the request data in the waiting manager
            DocumentWaitingManager.addJob(conversationId, {
              userId,
              conversationId,
              documents: conversation.documents,
              requestData: {
                id: conversationId,
                ...req.body
              },
              type: 'image-to-text'
            });

            logger.logInfo(`Added job to waiting queue for conversation ${conversationId} with ${conversation.documents.length} documents`);

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: `Đang xử lý tài liệu: ${processingDocuments.join(', ')}. Hệ thống sẽ tự động xử lý khi tài liệu sẵn sàng.`
              }
            });
          }
        } else {
          ask();
          next(null, { code: CONSTANTS.CODE.SUCCESS });
        }
      });
  };

  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      const stream = resAxios.data;
      let partialBuffer = '';

      resAxios.data.on('data', (chunk) => {
        const text = chunk.toString();
        partialBuffer += text;

        try {
          // Thử parse toàn bộ buffer
          const data = JSON.parse(partialBuffer);
          partialBuffer = ''; // reset lại buffer nếu parse thành công

          if (!ChatBotManager.isStreaming(conversationId)) {
            resAxios.data.destroy();
            if (response) {
              if(!response || !response.trim()) {
                response = messageError;
                error = new Error('no response');
              }

              pushNotify(error);
              io.to(conversationId).emit('endMessage', {
                _id,
                member: userId,
                id: conversationId,
                conversation: conversation._id,
                question: text,
                answer: response,
                createdAt
              });

              createCollection();
            }

            return;
          }

          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            if (!hasNotifiedClient) {
              PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
              hasNotifiedClient = true;
            }

            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          console.error("=============== err",e);
          // Nếu chưa parse được, chờ thêm data ở lần sau
          // Tuy nhiên, nếu dữ liệu quá dài hoặc sai format, cần reset sau N lần/lần timeout
          if (partialBuffer.length > 2000 || /^(#|\*)/.test(text)) {
            // Có thể là markdown hoặc lỗi → cứ nối vào luôn nếu rõ ràng không phải JSON
            response += decodeUnicode(text) + '\n';
            partialBuffer = '';
          }
        }
      });
      stream.on('end', () => {
        if(ChatBotManager.isStreaming(conversationId)) {
          ChatBotManager.stop(conversationId);
          if(!response || !response.trim()) {
            response = messageError;
            error = new Error('no response');
          }
          const lines = response.trim().split('\n');
          const lastLine = lines.pop().trim();
          response = lines.join('\n').trim();
          const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
          urlDownload = linkMatch ? linkMatch[2] : '';

          if(!urlDownload) {
            response += '\n' + lastLine
          }

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
            urlDownload
          });

          createCollection();
        }
      });
      stream.on('error', (err) => {
        if (ChatBotManager.isStreaming(conversationId)) {
          error = err;
          response = messageError;
          ChatBotManager.stop(conversationId);

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
          });

          createCollection();
        }
      });
    } catch (err) {
      if (ChatBotManager.isStreaming(conversationId)) {
        error = err;
        response = messageError;
        ChatBotManager.stop(conversationId);

        pushNotify(error);
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
      }
    }
  };

  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        content,
        createdAt,
        urlDownload
      },
      (err, result) => {
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => {}
        );
      }
    );
  };

  const pushNotify = (error) => {
    const notification = {
      title: 'Thông báo',
      message: `Chuyển hình ảnh thành văn bản ${error ? 'thất bại' : 'đã hoàn thành'}.`,
      data: {
        link: linkPushNoti,
        extras: {
          id: conversationId
        }
      }
    }

    PushNotifyManager.sendToMemberAssistant(userId.toString(), notification.title, notification.message, notification.data, 'conversation_update');
    UserNotifyModel
      .create({
        user: userId,
        title: notification.title || '',
        message: notification.message || '',
        data: notification.data || {},
      })
  }

  async.waterfall([checkParams, getUserInf, checkConversationExists], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
