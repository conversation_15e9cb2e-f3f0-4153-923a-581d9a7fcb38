const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Chat = require('../../../../models/chatITT');
const Conversation = require('../../../../models/conversationITT');
const UserNotifyModel = require('../../../../models/userNotify');
const axios = require('axios');
const ChatBotManager = require('../../../../job/chatStreamManager');
const PushNotifyManager = require('../../../../job/pushNotify');

// This is an internal API that doesn't require authentication
// It's used by the DocumentWaitingManager to process waiting jobs
module.exports = (req, res) => {
  // Get data from request
  const jobData = req.body;
  const userId = jobData.userId;
  const conversationId = jobData.conversationId;
  const content = _.get(jobData, 'content', '');
  const linkPushNoti = _.get(jobData, 'linkPushNoti', '/convert-image-to-text');

  // Log the internal request
  logger.logInfo(`Processing internal request for conversation ${conversationId} from DocumentWaitingManager`);

  // Set up variables needed for processing
  let text = `Chuyển file thành văn bản cho tôi.`;
  let urlDownload = '';
  const serverChatbot = config.proxyRequestServer.serverChatBot;
  let conversation;
  let response;
  const createdAt = Date.now();
  const _id = mongoose.Types.ObjectId();
  const messageError = MESSAGES.AI.MESSAGE_ERROR;
  let error = null;
  let hasNotifiedClient = false;

  // Start the answering process
  ChatBotManager.answering(conversationId, jobData);
  PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);

  // Get the conversation
  const getConversation = (next) => {
    Conversation.findOne({
      _id: conversationId,
    })
      .populate('documents')
      .lean()
      .exec((err, data) => {
        if (err || !data) {
          return next(err || new Error('Conversation not found'));
        }

        conversation = data;
        next();
      });
  };

  // Check if all documents are completed
  const checkDocumentsStatus = (next) => {
    if (!conversation.documents || conversation.documents.length === 0) {
      return next({
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });
    }

    // Check if all documents are completed
    let allCompleted = true;
    const processingDocuments = [];

    for (const doc of conversation.documents) {
      if (doc.status !== 'COMPLETED') {
        allCompleted = false;
        processingDocuments.push(doc.name || 'Tài liệu đang xử lý');
        logger.logError(
          ['Document not completed'],
          'processWaitingJob',
          { conversationId, documentId: doc._id, status: doc.status }
        );
      }
    }

    if (!allCompleted) {
      return next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Not all documents are completed'
      });
    }

    // Start the ask process
    ask();

    next({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Processing started'
    });
  };

  // Function to make the actual request to the chatbot
  const ask = async () => {
    try {
      const resAxios = await axios({
        method: 'post',
        url: `${serverChatbot}/api/2.0.0/chat_stream`,
        responseType: 'stream',
        data: {
          conversation_id: conversation.id,
          text,
        },
        timeout:300000,
      });

      response = '';
      const stream = resAxios.data;
      resAxios.data.on('data', (chunk) => {
        if (!ChatBotManager.isStreaming(conversationId)) {
          resAxios.data.destroy();
          if (response) {
            if(!response || !response.trim()) {
              response = messageError;
              error = new Error('no response');
            }
            const lines = response.trim().split('\n');
            const lastLine = lines.pop().trim();
            response = lines.join('\n').trim();
            const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
            urlDownload = linkMatch ? linkMatch[2] : '';

            if(!urlDownload) {
              response += '\n' + lastLine
            }

            pushNotify(error);
            io.to(conversationId).emit('endMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: response,
              createdAt,
              urlDownload
            });

            createCollection();
          }

          return;
        }

        try {
          const data = JSON.parse(chunk.toString());
          const searchingArray = ['on_search', 'on_handle_process'];
          if (searchingArray.includes(data.event)) {
            io.to(conversationId).emit('searching', { text: data.message });
          } else if (data.event === 'message') {
            response += data.message;
            response = response.replace('undefined', '');
            response = response.replace('```markdown', '');
            response = response.startsWith('markdown') ? response.replace('markdown', '') : response;

            ChatBotManager.saveAnswer(conversationId, response);
            if (!hasNotifiedClient) {
              PushNotifyManager.sendViaSocket(userId, 'conversation_update', {data: {link: '', extras: {id: conversationId}}}, []);
              hasNotifiedClient = true;
            }

            io.to(conversationId).emit('newMessage', {
              _id,
              member: userId,
              id: conversationId,
              conversation: conversation._id,
              question: text,
              answer: data.message,
              createdAt,
            });
          }
        } catch (e) {
          logger.logError([e], 'processWaitingJob', { conversationId, chunk: chunk.toString() });
        }
      });

      resAxios.data.on('end', () => {
        if (response) {
          if(!response || !response.trim()) {
            response = messageError;
            error = new Error('no response');
          }
          const lines = response.trim().split('\n');
          const lastLine = lines.pop().trim();
          response = lines.join('\n').trim();
          const linkMatch = lastLine.match(/\[(.*?)\]\((.*?)\)/);
          urlDownload = linkMatch ? linkMatch[2] : '';

          if(!urlDownload) {
            response += '\n' + lastLine
          }

          pushNotify(error);
          io.to(conversationId).emit('endMessage', {
            _id,
            member: userId,
            id: conversationId,
            conversation: conversation._id,
            question: text,
            answer: response,
            createdAt,
            urlDownload
          });

          createCollection();
        }

        ChatBotManager.stop(conversationId);
      });

      resAxios.data.on('error', (err) => {
        logger.logError([err], 'processWaitingJob', { conversationId });
        error = err;
        response = messageError;

        pushNotify(error);
        io.to(conversationId).emit('endMessage', {
          _id,
          member: userId,
          id: conversationId,
          conversation: conversation._id,
          question: text,
          answer: response,
          createdAt,
        });

        createCollection();
        ChatBotManager.stop(conversationId);
      });
    } catch (err) {
      logger.logError([err], 'processWaitingJob', { conversationId });
      error = err;
      response = messageError;

      pushNotify(error);
      io.to(conversationId).emit('endMessage', {
        _id,
        member: userId,
        id: conversationId,
        conversation: conversation._id,
        question: text,
        answer: response,
        createdAt,
      });

      createCollection();
      ChatBotManager.stop(conversationId);
    }
  };

  // Create the chat collection entry
  const createCollection = () => {
    Chat.create(
      {
        _id,
        member: userId,
        conversation: conversation._id,
        question: text,
        answer: response,
        error,
        content,
        createdAt,
        urlDownload
      },
      (err, result) => {
        Conversation.update(
          {
            _id: conversation._id,
          },
          {
            hasMessage: true,
            updatedAt: Date.now(),
          },
          () => { }
        );
      }
    );
  };

  const pushNotify = (error) => {
    const notification = {
      title: 'Thông báo',
      message: `Chuyển hình ảnh thành văn bản ${error ? 'thất bại' : 'đã hoàn thành'}.`,
      data: {
        link: linkPushNoti,
        extras: {
          id: conversationId
        }
      }
    }

    PushNotifyManager.sendToMemberAssistant(userId.toString(), notification.title, notification.message, notification.data, 'conversation_update');
    UserNotifyModel
      .create({
        user: userId,
        title: notification.title || '',
        message: notification.message || '',
        data: notification.data || {},
      })
  }

  // Execute the process
  async.waterfall([
    getConversation,
    checkDocumentsStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], 'processWaitingJob', { conversationId, userId });
    }

    res.json(data || {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: err && err.message ? err.message : MESSAGES.SYSTEM.ERROR
    });
  });
};
