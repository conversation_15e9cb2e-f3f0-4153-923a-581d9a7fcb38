const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const rp = require('request-promise')
const Conversation = require('../../../models/conversation')
const Chat = require('../../../models/chat')

module.exports = (req, res) => {

  const from = _.toSafeInteger(req.body.from) || (Date.now() + 100000);
  const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
  const conversation = req.body.conversation;

  Chat
    .find({
      conversation: conversation,
      // createdAt: {
      //   $lt: from
      // }
    }, "-conversation")
    .sort("createdAt")
    // .limit(limit)
    .lean()
    .exec((err, results) => {
      if (err) {
        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: results
      })
    });
}
