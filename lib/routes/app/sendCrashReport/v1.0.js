const _ = require('lodash');
const config = require('config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const CrashLog = require('../../../models/crashLog');
const redisConnections = require('../../../connections/redis')

module.exports = (req, res) => {
  res.json({
    code: CONSTANTS.CODE.SUCCESS
  })

  const token = _.get(req, 'headers.token', '');
  let appName = '';
  if (req.body && req.body.appName) {
    appName = req.body.appName
  }

  if (token) {
    let stringToken = `user:${token}`
    if (appName) {
      stringToken = `${appName}:${token}`
    }

    redisConnections('master').getConnection().get(stringToken, (err, result) => {
      if (result) {
        const objSign = JSON.parse(result);
        CrashLog
          .create({
            error: req.body.error,
            device: req.body.device,
            appState: req.body.appState,
            appName: req.body.appName || '',
            versionCodePush: req.body.versionCodePush || '',
            member: objSign.id || ''
          })
      } else {
        CrashLog
          .create({
            error: req.body.error,
            device: req.body.device,
            appState: req.body.appState,
            appName: req.body.appName || '',
            versionCodePush: req.body.versionCodePush || ''
          })
      }
    })
  } else {
    CrashLog
      .create({
        error: req.body.error,
        device: req.body.device,
        appState: req.body.appState,
        appName: req.body.appName || '',
        versionCodePush: req.body.versionCodePush || ''
      })
  }
};
