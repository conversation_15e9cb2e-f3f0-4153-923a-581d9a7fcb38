const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const SavedNotification = require('../../../../models/savedNotification')
const tool = require('../../../../util/tool')
const PushNotifyManager = require('../../../../job/pushNotify')


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let title = req.body.title || '';
  let messageTitle = req.body.messageTitle || '';
  let message = req.body.message || '';
  // let serviceId = req.body.serviceId || '';
  let link = _.get(req, 'body.data.link');
  let image = _.get(req, 'body.data.image');
  let extras = _.get(req, 'body.data.extras');
  let pushNotification = _.get(req, 'body.pushNotification');
  let category = _.get(req, 'body.category');
  let updatedData = {};
  const checkParams = (next) => {
    if (!id || !title || !image || (!extras.content && !extras.url) || !category || !category.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const modifySavedNotification = (next) => {
    let objModify = {
      title,
      messageTitle,
      message,
      category,
      pushNotification: false,
      data: {
        link,
        extras,
        image
      }
    }

    SavedNotification
      .update({ _id: id }, objModify)
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (pushNotification) {
          if (!message) {
            message = 'Vui lòng bấm để biết thêm chi tiết!';
            if (serviceId === '5fbc8b5e4de205666e8b23df') {
              message = 'Vui lòng bấm để biết thêm chi tiết phòng tránh hình thức lừa đảo này!';
            } else if (serviceId === '5fbc8a4f4de205666e8b23ce') {
              message = 'Vui lòng bấm để biết thêm chi tiết thông tin cảnh báo này!';
            }
          }

          PushNotifyManager
            .sendAll(messageTitle, message, { link, extras: {_id: id} })
        }
        updatedData = result
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'modify_notify',
        description: 'Cập nhật thông báo',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    modifySavedNotification,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
