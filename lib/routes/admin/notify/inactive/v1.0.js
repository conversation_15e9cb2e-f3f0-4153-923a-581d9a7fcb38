const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const SavedNotification = require('../../../../models/savedNotification')
const MESSAGES = require('../../../../message');


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  const status = _.get(req, 'body.status', 0);
  let updatedData = {};
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const inactiveSavedNotification = (next) => {
    SavedNotification
      .update({ _id: id }, { status })
      .exec((err, result) => {
        updatedData = result;
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: status ? 'active_notify' : 'inactive_notify',
        description: status ? 'Mở thông báo' : 'Xóa thông báo',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    inactiveSavedNotification,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}