const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/savedNotification'); //Chung tên cho dễ nhân bản

module.exports = (req, res) => {
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const sort = _.get(req, 'body.sort', 0);
  const textSearch = _.get(req, 'body.textSearch', '');
  const service = _.get(req, 'body.service', '');
  const isFilter = _.get(req, 'body.isFilter', 0);
  const status = _.get(req, 'body.status');
  let query = {
  };
  let count = 0;

  const checkParams = (next) => {
    if (textSearch.trim() && textSearch.length > 0) {
      query = {
        $or: [
          { title: new RegExp(textSearch, 'gi') },
          { message: new RegExp(textSearch, 'gi') },
        ],
      };
    }

    if (_.isNumber(status)) {
      query.status = status;
    }
    if (service) {
      query.service = service;
    }
    next();
  };

  const countQuery = (next) => {
    Model.count(query)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const list = (next) => {
    let filterOptions = {
      limit,
      skip: limit * page,
      sort: sort === 1 ? 'updatedAt' : '-updatedAt',
    };

    Model.find(query, '-nameAlias', filterOptions)
      .populate('service', 'name')
      .populate('category', 'name')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, countQuery, list], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
