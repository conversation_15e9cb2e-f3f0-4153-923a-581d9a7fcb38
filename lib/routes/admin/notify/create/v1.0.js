const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const SavedNotification = require('../../../../models/savedNotification')
const PushNotifyManager = require('../../../../job/pushNotify')
const MESSAGES = require('../../../../message');

const checkService = (service) => {
  switch (service) {
    case '5fbc8b5e4de205666e8b23df':
      return 'warning';
    break;
    case '5fbc8a4f4de205666e8b23ce':
      return 'alert';
    break;
    default:
      return 'notify';
    break;
  }
}

module.exports = (req, res) => {
  let title = req.body.title || '';
  let messageTitle = req.body.messageTitle || '';
  let message = req.body.message || '';
  let serviceId = req.body.serviceId || '';
  let link = _.get(req, 'body.data.link');
  let image = _.get(req, 'body.data.image');
  let extras = _.get(req, 'body.data.extras');
  let pushNotification = _.get(req, 'body.pushNotification');
  let category = _.get(req, 'body.category');
  let updatedData = {}

  const checkParams = (next) => {
    if (!title || !image || (!extras.content && !extras.url)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const createSavedNotification = (next) => {

    let objCreate = {
      title,
      messageTitle,
      message,
      category,
      pushNotification: false,
      data: {
        link,
        extras,
        image
      }
    }

    if (serviceId) {
      objCreate.service = serviceId;
    }

    SavedNotification
      .create(objCreate, (err, result) => {
        if (err) {
          return next(err);
        }

        if (pushNotification) {
          if (!message) {
            message = 'Vui lòng bấm để biết thêm chi tiết!';
            if (checkService(serviceId) === 'warning') {
              message = 'Vui lòng bấm để biết thêm chi tiết phòng tránh hình thức lừa đảo này!';
            } else if (checkService(serviceId) === 'alert') {
              message = 'Vui lòng bấm để biết thêm chi tiết thông tin cảnh báo này!';
            }
          }

          PushNotifyManager
            .sendAll(messageTitle, message, { link, extras: {_id: result._id} })
        }
        updatedData = result
        next()
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'create_notify',
        description: 'Tạo thông báo mới',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    createSavedNotification,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}