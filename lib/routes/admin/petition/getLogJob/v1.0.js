const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')

module.exports = (req, res) => {

  const _id = _.get(req, "body._id", '');
  let currentUser = ''
  const checkParams = (next) => {
    if(!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getCurrentUser = (next) => {
    Petition
      .findOne({
        _id
      })
      .populate('user', 'name')
      .select('user')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(result && result.user && result.user.name) {
          currentUser = result.user.name
        }

        next();
      })
  }

  const getLogJob = (next) => {
    PetitionJobLogModel
      .find({
        petition: _id
      })
      .populate('user', 'name')
      .populate({
        path: "user",
        select: "name units",
        populate: {
          path: "units",
          select: "name",
        },
      })
      .select('-petition -member -oldPetitionInf -newPetitionInf')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          currentUser
        });
      })
  }


  async.waterfall([
    checkParams,
    getCurrentUser,
    getLogJob
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}