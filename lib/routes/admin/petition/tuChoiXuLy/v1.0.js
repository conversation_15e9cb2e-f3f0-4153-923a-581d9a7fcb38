const _ = require('lodash')
const async = require('async')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const NotifyManager = require('../../../../job/notifyManager');
const ms = require('ms')

//Từ chối xử lý phản ánh
/*
  input: userId, _id, content,
  output: Từ chối xử lý phản ánh, phản ánh được chuyển lên cấp trên, nhập nội dung từ chối
*/
// chưa có handle push
/*
  STATUS: không thay đổi
  STATUS_JOB: CHO_TN_PA, CHO_XU_LY

  Cơ bản trạng thái Job không thay đổi, chỉ thay đổi user nhận job hoặc unit
  Trạng thái job chỉ thay đổi khi UBND quận/huyện từ chối xử lý  CHO_XU_LY => CHO_PHAN_PHOI(không có user nhận job và unitPath.length === 2)
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const content = _.get(req.body,'content', '');
  const permissions = _.get(req.user, 'permissions', [])

  let petition;
  let userTransfer;
  let userInf;
  let expiredTimeJob, warningTimeJob
  let configProcessing = {}

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!content) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_REFUSE_CONTENT
      })
    }

    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.CHO_TIEP_NHAN && result.status !== CONSTANTS.STATUS.DA_TIEP_NHAN) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }
        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_TN_PA && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_XU_LY) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        if(!petition.unitPath.length) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(petition.unitPath.length === 1) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.CANNOT_REFUSE
          })
        }
        if(petition.cannotReject) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.CANNOT_REFUSE
          })
        }
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, data) => {
        if(!err && data && data.config && data.config.time) {
          configProcessing = data.config
          expiredTimeJob = configProcessing && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].time? ms(configProcessing[petition.statusJob.toString()].time) + Date.now() : null;
          warningTimeJob = expiredTimeJob && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].warningTime ? expiredTimeJob - ms(configProcessing[petition.statusJob.toString()].warningTime) : null
        }
        next();
      })
  }

  const checkUser = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        userInf = result
        let isLeader =  permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))
        if((isLeader && !petition.user && result.units.map(String).includes(petition.unit.toString()))
          || (petition.user && petition.user.toString() === userId)
        ) {
          next()
        } else {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.DONT_HAVE_PERMISSION_REFUSE
          })
        }
      })
  }

  const findTransferLog = (next) => {
    PetitionJobLogModel
      .findOne({
        petition: _id,
        action: 'chuyen_xu_ly',
        toPerson: userId
      })
      .sort({
        createdAt: -1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          userTransfer = result.fromPerson
        }
        next();
      })
  }

  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.CHO_TIEP_NHAN, CONSTANTS.STATUS.DA_TIEP_NHAN]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_XU_LY]
      }
    }
    let objUpdate = {
      updatedAt: Date.now(),
      expiredTimeJob,
      warningTimeJob
    }

    if(petition.user) {
      objUpdate.user = userTransfer ? userTransfer : null
      objUpdate.users = petition.users.filter(user => user.toString()!== userId)
    } else {
      if(petition.unitPath.length === 2) {
        objUpdate.statusJob = CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI
      }
      objUpdate.unit = petition.unitPath[petition.unitPath.length-2]
      objUpdate.unitPath = petition.unitPath.slice(0, -1)
    }
    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push

        if(petition.user) {
          if(userTransfer) {
            NotifyManager
            .handleNotify(userTransfer,{
              title: 'Thông báo',
              message: `Cán bộ ${userInf.name} đã từ chối phản ánh kiến nghị ${petition.code}. Bạn cần phân phối lại hoặc tiếp nhận`,
              data: {
                link: 'PetitionPage',
                extras: {
                  _id
                }
              },
              eventName: "petition_update",
            })
          } else {
            NotifyManager
            .handleNotifyUnit(petition.unit, 'dieu-chuyen-phan-anh', {
              title: 'Thông báo',
              message: `Cán bộ ${userInf.name} đã từ chối phản ánh kiến nghị ${petition.code}. Bạn cần phân phối lại hoặc tiếp nhận`,
              data: {
                link: 'PetitionPage',
                extras: {
                  _id
                }
              },
              eventName: "petition_update",
            })
          }
        } else {
          NotifyManager
            .handleNotifyUnit(objUpdate.unit, 'dieu-chuyen-phan-anh', {
              title: 'Thông báo',
              message: `Cán bộ ${userInf.name} đã từ chối phản ánh kiến nghị ${petition.code}. Bạn cần phân phối lại hoặc tiếp nhận`,
              data: {
                link: 'PetitionPage',
                extras: {
                  _id
                }
              },
              eventName: "petition_update",
            })
        }
        PetitionJobLogModel
          .create({
            action: 'tu_choi_xu_ly',
            user: userId,
            petition: _id,
            description: 'Từ chối xử lý',
            content,
            oldStatus: petition.statusJob,
            newStatus: objUpdate.statusJob,
            oldPetitionInf: petition,
            newPetitionInf: result,
            expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
          })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    getConfigExpiredTime,
    checkUser,
    findTransferLog,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}