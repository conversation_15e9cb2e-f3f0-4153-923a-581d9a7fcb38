const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const CategoryManager = require('../../../../job/categoryManager')

//Sửa lĩnh vực phản ánh
/*
  input: userId, _id, category,
  output: Cập nhật lại lĩnh vực phản ánh
*/
// chưa có handle push
/*
  STATUS: không thay đổi
  STATUS_JOB: Không thay đổi
  Cập nhật với STATUS_JOB: CHO_PHAN_PHOI hoặc CHO_TN_PA hoặc CHO_XU_LY
*/
module.exports = (req, res) => {

  const _id = _.get(req.body, '_id', '')

  let petition;

  const checkParams = (next) => {
    if(!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next()
  }



  const findPetition = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;

        next();
      })
  }


  const syncProcessing = (next) => {
    next(null,{
      code: CONSTANTS.CODE.SUCCESS
    });
    CategoryManager.addPetition(petition._id, `${petition.title}.${petition.content}`)
  }


  async.waterfall([
    checkParams,
    findPetition,
    syncProcessing
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}