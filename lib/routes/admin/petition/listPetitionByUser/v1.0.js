const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')


//Từ chối xử lý phản ánh
/*
  input: userId, _id
  output: danh sách phản ánh sắp xếp theo bộ lọc gử<PERSON> lên, c<PERSON> thể từ mới đến cũ hoặc gần nhất

  danh sách phản ánh lấy phải ở trạng thái STATUS: DANG_XU_LY, DA_XU_LY, DA_TU_CHOI
*/


module.exports = (req, res) => {

  const limit = _.get(req, "body.limit", 10);
  const page = _.get(req, "body.page", 0);
  const sortBy = _.get(req, "body.sortBy", 'location');
  const sort = _.get(req, "body.sort", -1);
  const _id = _.get(req, "body._id", '');
  const text = _.get(req, "body.text", '');

  const userId = _.get(req, "user.id", '');

  let petitionInf;

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getPetitionInf = (next) => {
    Petition
      .findOne({
        _id,
        statusJob: {
          $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_XU_LY]
        }
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petitionInf = result;
        next()
      })
  }


  const listPetition = (next) => {
    const skip = page * limit;
    let objSearch = {
      isDuplicate: {
        $ne: true
      },
      originalPetition:null,
      status: {
        $in: [CONSTANTS.STATUS.DANG_XU_LY, CONSTANTS.STATUS.DA_XU_LY, CONSTANTS.STATUS.DA_TU_CHOI]
      },
      member: petitionInf.member
    }
    if(text && text.trim()) {
      const $regex = escapeStringRegexp(text.trim());
      objSearch["$or"] = [
        {
          title: {
            $regex,
            $options: "i",
          },
        },
        {
          content: {
            $regex,
            $options: "i",
          },
        },
        {
          code: {
            $regex,
            $options: "i",
          },
        },
      ];
    }


    const options = {
    };

    let PetitionHandle =
      Petition
        .find(objSearch,options)
        .near('location', {
          center: {
            coordinates: [petitionInf.location.coordinates[0], petitionInf.location.coordinates[1]],
            type: 'Point'
          },
          maxDistance: 30000
        })
    if(!petitionInf.location || !petitionInf.location.coordinates || !petitionInf.location.coordinates.length){
      sortBy = 'createdAt'
    }
    if(sortBy == 'createdAt') {
      options.sort = sort == 1 ? "createdAt" : "-createdAt"
      PetitionHandle =
        Petition
          .find({
            ...objSearch
          })
          .sort(options.sort)
    }
    PetitionHandle
      .populate("unit", "name")
      .populate("district","name")
      .populate("ward","name")
      .populate("member", "name phone")
      .populate("category", "name icon")
      .skip(skip)
      .limit(limit)
      .lean()
      .exec((err, results) => {

        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        });
      });

  }

  async.waterfall([
    checkParams,
    getPetitionInf,
    listPetition
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}