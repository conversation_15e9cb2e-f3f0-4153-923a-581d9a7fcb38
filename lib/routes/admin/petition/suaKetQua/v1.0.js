const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')

// Sửa kết quả
/*
  input: userId, _id, replyContent, replyAttachments,isPublic
  output: Cập nhật phản ánh, trạng thái giữ nguyên
*/
// chưa có handle push
// check user đang nhận phản ánh xem có can thiệp được không
/*
  STATUS: DANG_XU_LY
  DANG_XU_LY =>  DANG_XU_LY;

  STATUS_JOB: CHO_DUYET_KQ
  DANG_XU_LY =>  CHO_DUYET_KQ;
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const replyContent = _.get(req.body,'replyContent', '');
  const replyAttachments = _.get(req.body,'replyAttachments', []);
  const isPublic = _.get(req.body,'isPublic', false);

  let petition;

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!replyContent) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_ACCEPT_CONTENT
      })
    }
    if(!_.has(req, 'body.isPublic')){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_PUBLIC
      })
    }

    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.DANG_XU_LY) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_DUYET_KQ) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        next();
      })
  }


  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.DANG_XU_LY]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_DUYET_KQ]
      }
    }

    let objUpdate = {
      updatedAt: Date.now(),
      replyContent,
      replyAttachments,
      isPublic
    }
    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push

        PetitionJobLogModel
          .create({
            action: 'sua_kq',
            user: userId,
            petition: _id,
            description: 'Sửa kết quả',
            oldStatus: petition.statusJob,
            newStatus: petition.statusJob,
            oldPetitionInf: petition,
            newPetitionInf: result
          })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}