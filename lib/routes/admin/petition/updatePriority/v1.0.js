const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')

//Sửa lĩnh vực phản ánh
/*
  input: userId, _id, category,
  output: Cập nhật lại lĩnh vực phản ánh
*/
// chưa có handle push
/*
  STATUS: không thay đổi
  STATUS_JOB: Không thay đổi
  Cập nhật với STATUS_JOB: CHO_PHAN_PHOI hoặc CHO_TN_PA hoặc CHO_XU_LY
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const priority = _.get(req.body,'priority', '');

  let petition;

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(!_.has(req,'body.priority')) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_PRIORITY
      })
    }
    if(![-1,0,1].includes(priority)){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.PRIORITY_NOT_VALID
      })
    }
    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.CHO_TIEP_NHAN && result.status!== CONSTANTS.STATUS.DA_TIEP_NHAN) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }
        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_TN_PA && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI  && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_XU_LY) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        next();
      })
  }


  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.CHO_TIEP_NHAN, CONSTANTS.STATUS.DA_TIEP_NHAN]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI, CONSTANTS.STATUS_JOB.CHO_XU_LY]
      }
    }
    let objUpdate = {
      updatedAt: Date.now(),
      priority
    }


    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push

        PetitionJobLogModel
          .create({
            action: 'cap_nhat_muc_do',
            user: userId,
            petition: _id,
            description: 'Cập nhật mức độ phản ánh',
            oldStatus: petition.statusJob,
            newStatus: petition.statusJob,
            oldPetitionInf: petition,
            newPetitionInf: result
          })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}