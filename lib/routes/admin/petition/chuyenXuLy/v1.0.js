const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const UnitModel = require('../../../../models/unit')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const PushNotifyManager = require('../../../../job/pushNotify');
const NotifyManager = require('../../../../job/notifyManager');

//Chuyển xử lý
/*
  input: userId, _id, personId or unitId, content,
  output: Chuyển phản ánh đến cán bộ/đơn vị phù hợp, bắn thông báo, lưu lại nội dung chuyển
*/
/*
  STATUS: CHO_TIEP_NHAN,DA_TIEP_NHAN => DA_TIEP_NHAN(Chỉ với case của UBND Quận huyện(CHO_TN_PA => CHO_XU_LY))

  STATUS_JOB: CHO_TN_PA, CHO_XU_LY, CHO_PHAN_PHOI

  Với cấp Quận Huyện:
  CHO_TN_PA => CHO_XU_LY, CHO_XU_LY => CHO_XU_LY

  Với các cấp dưới
  CHO_XU_LY => CHO_XU_LY

  Với cấp Văn phòng UBND Tỉnh
  CHO_PHAN_PHOI => CHO_XU_LY

*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  const _id = _.get(req.body, '_id', '')
  let petition;
  const personId = _.get(req.body, 'personId', '')
  const unitId = _.get(req.body, 'unitId', '')
  const content = _.get(req.body, 'content', '')
  let unitPath = [];
  const permissions = _.get(req.user, 'permissions', [])
  let unit,person
  let users = []
  let expiredTimeJob, warningTimeJob
  let configProcessing = {}

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(!personId && !unitId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_PERSON_OR_UNIT
      })
    }

    if(personId && unitId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.NO_BOTH_PERSON_AND_UNIT
      })
    }

    if(personId === userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.DONT_HAVE_TO_ASSIGN_YOURSEFT
      })
    }
    next()
  }


  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(petition.user && personId === petition.user.toString()) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.PETITION.ALREADY_USER
          })
        }
        if(petition.unit && unitId === petition.unit.toString()) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.PETITION.ALREADY_UNIT
          })
        }
        if(result.status !== CONSTANTS.STATUS.CHO_TIEP_NHAN && result.status !== CONSTANTS.STATUS.DA_TIEP_NHAN) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_TN_PA && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_XU_LY && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }
        next();
      })
  }

  const checkUnit = (next) => {
    if(!unitId) {
      return next();
    }
    User
      .count({
        units: unitId
      })
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(!count) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND_USER_IN_UNIT
          })
        }
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, data) => {
        if(!err && data && data.config && data.config.time) {
          configProcessing = data.config
          expiredTimeJob = configProcessing && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].time? ms(configProcessing[petition.statusJob.toString()].time) + Date.now() : null;
          warningTimeJob = expiredTimeJob && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].warningTime ? expiredTimeJob - ms(configProcessing[petition.statusJob.toString()].warningTime) : null
        }
        next();
      })
  }

  const checkUser = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        let isLeader =  permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))
        if((isLeader && result.units.map(String).includes(petition.unit.toString()))) {
          next()
        } else {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.DONT_HAVE_PERMISSION_MOVE
          })
        }
      })
  }

  const findPerson = (next) => {
    if(!personId) {
      return next();
    }
    User
      .findOne({
        _id: personId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND_PERSON_SELECTED
          })
        }
        person = result;
        next();
      })
  }


  const findUnitParent = (next) => {
    if(!unitId) {
      return next();
    }
    UnitModel
      .findOne({
        _id: unitId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND_UNIT_SELECTED
          })
        }
        unitPath = [...result.parentPath,unitId]
        unit = result;
        next();
      })
  }

  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.CHO_TIEP_NHAN, CONSTANTS.STATUS.DA_TIEP_NHAN]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_XU_LY, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI]
      }
    }

    let objUpdate = {
      status: CONSTANTS.STATUS.DA_TIEP_NHAN,
      statusJob: CONSTANTS.STATUS_JOB.CHO_XU_LY,
      updatedAt: Date.now(),
      expiredTimeJob,
      warningTimeJob
    }
    if(petition.statusJob === CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI) {
      objUpdate.cannotReject = true
    }
    if(personId) {
      objUpdate.user = personId;
      objUpdate.users = [...petition.users, personId]
    }
    if(unitId) {
      objUpdate.unit = unitId;
      objUpdate.unitPath = unitPath;
      objUpdate.user = null
    }
    Petition
      .findOneAndUpdate(
        query,
        objUpdate,
        {new: true})
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err)
          }

          if(!result) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.PETITION.UPDATE_FAIL
            })
          }
          //handle push
          if(petition.status !== CONSTANTS.STATUS.DA_TIEP_NHAN) {
            PushNotifyManager
              .sendToMember(petition.member.toString(), 'Phản ánh đã được tiếp nhận', `Cảm ơn Ông/bà đã gửi phản ánh. Mã số: #${petition.code}. Ấn để xem thông tin chi tiết. Chúng tôi sẽ phản hồi trong thời gian sớm nhất!`, {link: 'MyPetitionDetailScreen', extras: {id: _id}}, 'petition_update','')

            PetitionLogModel
            .create({
              action: 'tiep_nhan_phan_anh',
              user: userId,
              petition: _id,
              description: 'Tiếp nhận phản ánh',
              oldStatus: petition.status,
              newStatus: CONSTANTS.STATUS.DA_TIEP_NHAN,
              oldPetitionInf: petition,
              newPetitionInf: result
            })
          }

          if(unitId) {
            NotifyManager
              .handleNotifyUnit(unitId, 'dieu-chuyen-phan-anh', {
                title: 'Thông báo',
                message: `Bạn có phản ánh kiến nghị ${petition.code} cần xử lý tiếp nhận/điều phối`,
                data: {
                  link: 'PetitionPage',
                  extras: {
                    _id
                  }
                },
                eventName: "petition_update",
              })
          } else {
            NotifyManager
            .handleNotify(personId,{
              title: 'Thông báo',
              message: `Bạn có phản ánh kiến nghị ${petition.code} cần tiếp nhận`,
              data: {
                link: 'PetitionPage',
                extras: {
                  _id
                }
              },
              eventName: "petition_update",
            })
          }
          PetitionJobLogModel
            .create({
              action: 'chuyen_xu_ly',
              user: userId,
              petition: _id,
              description: unitId ? `Chuyển xử lý đến đơn vị ${unit.name}`: `Chuyển xử lý đến cán bộ ${person.name}`,
              oldStatus: petition.statusJob,
              newStatus: CONSTANTS.STATUS_JOB.CHO_XU_LY,
              oldPetitionInf: petition,
              newPetitionInf: result,
              content,
              fromPerson: petition.user,
              fromUnit: unitId ? unitPath[unitPath.length - 2] : petition.unit,
              toUnit: unitId? unitId : null,
              toPerson: unitId ? null : personId,
              expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
            })

          next(null,{
            code: CONSTANTS.CODE.SUCCESS
          });
        })
  }


  async.waterfall([
    checkParams,
    checkStatus,
    checkUnit,
    getConfigExpiredTime,
    checkUser,
    findPerson,
    findUnitParent,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}