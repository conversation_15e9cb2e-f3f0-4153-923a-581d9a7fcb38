const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const NotifyManager = require('../../../../job/notifyManager');
const UnitModel = require('../../../../models/unit')

// Gửi duyệt kết quả
/*
  input: userId, _id, replyContent, replyAttachments,isPublic
  output: Cập nhật phản ánh, phản ánh chuyển sang , job chuyển sang Chờ duyệt KQ
*/
// chưa có handle push
// check user đang nhận phản ánh xem có can thiệp được không
/*
  STATUS: DANG_XU_LY
  DANG_XU_LY =>  DANG_XU_LY;

  STATUS_JOB: DANG_XU_LY
  DANG_XU_LY =>  CHO_DUYET_KQ;
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const replyContent = _.get(req.body,'replyContent', '');
  const replyAttachments = _.get(req.body,'replyAttachments', []);
  const permissions = _.get(req.user, 'permissions', [])
  let expiredTimeJob, warningTimeJob
  let configProcessing = {}

  let userTransfer;
  let petition;
  let fromPerson, fromUnit

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!replyContent) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_ACCEPT_CONTENT
      })
    }


    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.DANG_XU_LY) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        if(result.statusJob !== CONSTANTS.STATUS_JOB.DANG_XU_LY && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_DUYET_KQ) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        next();
      })
  }


  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, data) => {
        if(!err && data && data.config && data.config.time) {
          configProcessing = data.config
          expiredTimeJob = configProcessing && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].time? ms(configProcessing[petition.statusJob.toString()].time) + Date.now() : null;
          warningTimeJob = expiredTimeJob && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].warningTime ? expiredTimeJob - ms(configProcessing[petition.statusJob.toString()].warningTime) : null
        }
        next();
      })
  }

  const checkUser = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        let isLeader =  permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'duyet-ket-qua'))
        const rootUnit = petition.unitPath[0].toString();
        if(petition.user) {
          if(petition.user.toString() !== userId) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.PETITION.DONT_HAVE_PERMISSION
            })
          } else if(petition.user.toString() === userId && result.units.map(String).includes(rootUnit) && isLeader) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.PETITION.DONT_HAVE_PERMISSION
            })
          } else {
            next();
          }
        } else {
          if(result.units.map(String).includes(rootUnit) && isLeader) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.PETITION.DONT_HAVE_PERMISSION
            })
          } else {
            next();
          }
        }
      })
  }

  const findTransferLog = (next) => {
    let obj = {
      petition: _id,
      action: 'chuyen_xu_ly'
    }
    if(petition.user) {
      obj.toPerson = userId
    } else {
      obj.toUnit = petition.unit
    }
    PetitionJobLogModel
      .findOne(obj)
      .sort({
        createdAt: -1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(result.fromPerson) {
          fromPerson = result.fromPerson
        }  else {
          fromUnit = result.fromUnit
        }

        next();
      })
  }

  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.DANG_XU_LY]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.DANG_XU_LY, CONSTANTS.STATUS_JOB.CHO_DUYET_KQ]
      }
    }

    let objUpdate = {
      statusJob: CONSTANTS.STATUS_JOB.CHO_DUYET_KQ,
      updatedAt: Date.now(),
      replyContent,
      replyAttachments,
      expiredTimeJob,
      warningTimeJob
    }
    if(fromPerson) {
      objUpdate.user = fromPerson
    } else {
      objUpdate.unit = fromUnit
      objUpdate.user = null
    }
    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push
        if(fromPerson) {
          NotifyManager
          .handleNotify(fromPerson,{
            title: 'Thông báo',
            message: `Bạn có phản ánh kiến nghị ${petition.code} cần duyệt kết quả`,
            data: {
              link: 'PetitionPage',
              extras: {
                _id
              }
            },
            eventName: "petition_update",
          })
        } else {
          NotifyManager
          .handleNotifyUnit(fromUnit, 'dieu-chuyen-phan-anh', {
            title: 'Thông báo',
            message: `Bạn có phản ánh kiến nghị ${petition.code} cần duyệt. Ấn để xem chi tiết`,
            data: {
              link: 'PetitionPage',
              extras: {
                _id
              }
            },
            eventName: "petition_update",
          })
        }
        if(objUpdate.unit) {
          UnitModel
            .findOne({
              _id: objUpdate.unit
            })
            .lean()
            .exec((err, unitData) => {
              if(unitData) {
                PetitionJobLogModel
                .create({
                  action: 'gui_duyet_kq',
                  user: userId,
                  petition: _id,
                  description: `Gửi duyệt kết quả đến ${unitData.name}`,
                  oldStatus: petition.statusJob,
                  newStatus: CONSTANTS.STATUS_JOB.CHO_DUYET_KQ,
                  oldPetitionInf: petition,
                  newPetitionInf: result,
                  fromUnit: petition.user ? null : petition.unit,
                  toUnit: objUpdate.unit,
                  fromPerson: petition.user? petition.user : null,
                  toPerson: objUpdate.user,
                  expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
                })
              }
            })
        } else {
          User
            .findOne({
              _id: objUpdate.user
            })
            .lean()
            .exec((err, userData) => {
              if(userData) {
                PetitionJobLogModel
                .create({
                  action: 'gui_duyet_kq',
                  user: userId,
                  petition: _id,
                  description: `Gửi duyệt kết quả đến ${userData.name}`,
                  oldStatus: petition.statusJob,
                  newStatus: CONSTANTS.STATUS_JOB.CHO_DUYET_KQ,
                  oldPetitionInf: petition,
                  newPetitionInf: result,
                  fromUnit: petition.user ? null : petition.unit,
                  toUnit: objUpdate.unit,
                  fromPerson: petition.user? petition.user : null,
                  toPerson: objUpdate.user,
                  expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
                })
              }
            })
        }


        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    getConfigExpiredTime,
    checkUser,
    findTransferLog,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}