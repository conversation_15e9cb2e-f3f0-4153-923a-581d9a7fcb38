const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const MemberModel = require('../../../../models/member')
const validator = require('validator')

module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const permissions = _.get(req, "user.permissions", []);

  let userInf;
  let warningTime = ms('2d');
  let result = {};
  const textSearch = _.get(req, "body.textSearch", '');
  const category = _.get(req, "body.category", '');
  let member;

  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getUserInf = (next) => {
    User
    .findOne({_id: userId})
    .lean()
    .exec((err, result) => {
      if(err) {
        return next(err)
      }
      if(!result) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        })
      }
      userInf = result
      next();
    })
  }

  const getMemberInf = (next) => {
    if (!textSearch || (textSearch && !validator.isMobilePhone(textSearch, ["vi-VN"]))) {
        return next();
    }
    MemberModel
      .findOne({
        phone: textSearch
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        member = result && result._id ? result._id : ''
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, config) => {
        if(!err && config && config.config && config.config.warningTime) {
          warningTime =  ms(config.config.warningTime);
        }
        next();
      })
  }

  const countTab = (next) => {
    let hasPermissionMovePetition = permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))

    const countTab0 = (done) => {
      let objSearch = {
        createdAt:{
          $gt: Date.now() - ms('30d')
        },
        isDuplicate: {
          $ne: true
        }
      }
      let firstOr = []
      let secondOr = []
      firstOr = [
        {user: userId}
      ]

      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI,CONSTANTS.STATUS_JOB.CHO_XU_LY]
      }
      if(hasPermissionMovePetition) {
        firstOr.push({
          user: null,
          unit: {
            $in: userInf.units
          }
        })
      }

      if(member) {
        objSearch.member = member
      } else if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        secondOr = [
          {
            title : {
              $regex,
              $options: 'i'
            }
          },
          {
            content : {
              $regex,
              $options: 'i'
            }
          },
          {
            address : {
              $regex,
              $options: 'i'
            }
          },
          {
            code : textSearch
          }
        ]
      }
      objSearch['$and'] = [
          { $or: firstOr }
      ]
      if(secondOr.length){
        objSearch['$and'].push({$or: secondOr})
      }
      if(category) {
        objSearch.category = category
      }

      Petition
        .count(objSearch,(err,count) => {
          result['0'] = count
          done();
        })
    }

    const countTab1 = (done) => {
      let objSearch = {
        createdAt:{
          $gt: Date.now() - ms('30d')
        },
        isDuplicate: {
          $ne: true
        }
      }
      let firstOr = []
      let secondOr = []

      if(hasPermissionMovePetition) {
        firstOr = [{
          user: null,
          unit: {
            $in: userInf.units
          }
        },{
          user: userId
        }]
      } else {
        objSearch.user = userId
      }
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.DANG_XU_LY]
      }
      if(member) {
        objSearch.member = member
      } else if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        secondOr = [
          {
            title : {
              $regex,
              $options: 'i'
            }
          },
          {
            content : {
              $regex,
              $options: 'i'
            }
          },
          {
            address : {
              $regex,
              $options: 'i'
            }
          },
          {
            code : textSearch
          }
        ]
      }
      if(firstOr.length) {
        objSearch['$and'] = [
          { $or: firstOr }
        ]
        if(secondOr.length){
          objSearch['$and'].push({$or: secondOr})
        }
      } else {
        if(secondOr.length){
          objSearch['$and'] = [{$or: secondOr}]
        }
      }
      Petition
      .count(objSearch,(err,count) => {
        result['1'] = count
        done();
      })
    }

    const countTab2 = (done) => {
      let objSearch = {
        createdAt:{
          $gt: Date.now() - ms('30d')
        },
        isDuplicate: {
          $ne: true
        }
      }
      let firstOr = []
      let secondOr = []

      firstOr = [
        {
          user: userId,
          statusJob: {
            $in:[CONSTANTS.STATUS_JOB.CHO_XU_LY, CONSTANTS.STATUS_JOB.CHO_DUYET_KQ,CONSTANTS.STATUS_JOB.DANG_XU_LY]
          }
        }
      ]
      if(hasPermissionMovePetition) {
        firstOr.push({
          user: null,
          unit: {
            $in: userInf.units
          },
          statusJob:{
            $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI,CONSTANTS.STATUS_JOB.CHO_XU_LY]
          }
        })
      }
      objSearch.expiredTime = {
        $lt: Date.now() + warningTime
      }

      if(member) {
        objSearch.member = member
      } else if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        secondOr = [
          {
            title : {
              $regex,
              $options: 'i'
            }
          },
          {
            content : {
              $regex,
              $options: 'i'
            }
          },
          {
            address : {
              $regex,
              $options: 'i'
            }
          },
          {
            code : textSearch
          }
        ]
      }
      objSearch['$and'] = [
          { $or: firstOr }
      ]
      if(secondOr.length){
        objSearch['$and'].push({$or: secondOr})
      }
      if(category) {
        objSearch.category = category
      }

      Petition
      .count(objSearch,(err,count) => {
        result['2'] = count
        done();
      })
    }

    const countTab3 = (done) => {
      let objSearch = {
        createdAt:{
          $gt: Date.now() - ms('30d')
        },
        isDuplicate: {
          $ne: true
        }
      }
      let firstOr = []
      let secondOr = []

      if(hasPermissionMovePetition) {
        firstOr=[{
          user: null,
          unit: {
            $in: userInf.units
          }
        },{
          user: userId
        }]
      } else {
        objSearch.user = userId
      }
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.CHO_DUYET_KQ]
      }
      if(member) {
        objSearch.member = member
      } else if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        secondOr = [
          {
            title : {
              $regex,
              $options: 'i'
            }
          },
          {
            content : {
              $regex,
              $options: 'i'
            }
          },
          {
            address : {
              $regex,
              $options: 'i'
            }
          },
          {
            code : textSearch
          }
        ]
      }
      if(firstOr.length) {
        objSearch['$and'] = [
          { $or: firstOr }
        ]
        if(secondOr.length){
          objSearch['$and'].push({$or: secondOr})
        }
      } else {
        if(secondOr.length){
          objSearch['$and'] = [{$or: secondOr}]
        }
      }
      if(category) {
        objSearch.category = category
      }
      Petition
      .count(objSearch,(err,count) => {
        result['3'] = count
        done();
      })
    }

    const countTab4 = (done) => {
      let objSearch = {
        createdAt:{
          $gt: Date.now() - ms('30d')
        },
        isDuplicate: {
          $ne: true
        }
      }
      objSearch.user = userId
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.HOAN_THANH, CONSTANTS.STATUS_JOB.DA_TU_CHOI]
      }

      if(member) {
        objSearch.member = member
      } else if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        objSearch['$or'] = [
          {
            title : {
              $regex,
              $options: 'i'
            }
          },
          {
            content : {
              $regex,
              $options: 'i'
            }
          },
          {
            address : {
              $regex,
              $options: 'i'
            }
          },
          {
            code : textSearch
          }
        ]
      }

      if(category) {
        objSearch.category = category
      }

      Petition
      .count(objSearch,(err,count) => {
        result['4'] = count
        done();
      })
    }

    const countTab5 = (done) => {
      let objSearch = {
        createdAt:{
          $gt: Date.now() - ms('30d')
        },
        isDuplicate: {
          $ne: true
        }
      }
      let firstOr = []
      let secondOr = []
      if(hasPermissionMovePetition) {
        firstOr=[{
          user: null,
          unit: {
            $in: userInf.units
          }
        },{
          user: userId
        }]
      } else {
        objSearch.user = userId
      }
      objSearch.maybeSpam = true
      if(member) {
        objSearch.member = member
      } else if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        secondOr = [
          {
            title : {
              $regex,
              $options: 'i'
            }
          },
          {
            content : {
              $regex,
              $options: 'i'
            }
          },
          {
            address : {
              $regex,
              $options: 'i'
            }
          },
          {
            code : textSearch
          }
        ]
      }
      if(firstOr.length) {
        objSearch['$and'] = [
          { $or: firstOr }
        ]
        if(secondOr.length){
          objSearch['$and'].push({$or: secondOr})
        }
      } else {
        if(secondOr.length){
          objSearch['$and'] = [{$or: secondOr}]
        }
      }
      if(category) {
        objSearch.category = category
      }
      Petition
      .count(objSearch,(err,count) => {
        result['5'] = count
        done();
      })
    }
    async.parallel([
      countTab0,
      countTab1,
      countTab2,
      countTab3,
      countTab4,
      countTab5
    ], (err, data) => {
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: result
      });
    })
  }

  async.waterfall([
    checkParams,
    getUserInf,
    getMemberInf,
    getConfigExpiredTime,
    countTab
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}