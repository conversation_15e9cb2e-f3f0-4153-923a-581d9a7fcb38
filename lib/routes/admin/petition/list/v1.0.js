const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const MemberModel = require('../../../../models/member')
const validator = require('validator')

module.exports = (req, res) => {

  const limit = _.get(req, "body.limit", 10);
  const page = _.get(req, "body.page", 0);
  const sort = _.get(req, "body.sort", -1);

  const userId = _.get(req, "user.id", '');
  const status = _.get(req, "body.status", []);
  const isListExpire = _.get(req, "body.isListExpire", 0);
  const maybeSpam = _.get(req, "body.maybeSpam", false);
  const isDuplicate = _.get(req, "body.isDuplicate", false);
  const permissions = _.get(req, "user.permissions", []);
  const textSearch = _.get(req, "body.textSearch", '');
  const category = _.get(req, "body.category", '');
  const unit = _.get(req, "body.unit", '');
  const user = _.get(req, "body.user", '');

  let member;
  let warningTime = ms('2d');

  let userInf;

  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getUserInf = (next) => {
    User
      .findOne({_id: userId})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        userInf = result
        next();
      })
  }

  const getMemberInf = (next) => {
    if (!textSearch || (textSearch && !validator.isMobilePhone(textSearch, ["vi-VN"]))) {
        return next();
    }
    MemberModel
      .findOne({
        phone: textSearch
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        member = result && result._id ? result._id : ''
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    if(!isListExpire) {
      return next();
    }
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, config) => {
        if(!err && config && config.config && config.config.warningTime) {
          warningTime =  ms(config.config.warningTime);
        }
        next();
      })
  }

  const listPetition = (next) => {
    const skip = page * limit;
    let hasPermissionMovePetition = permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))

    let objSearch = {

      isDuplicate: {
        $ne: true
      }
    }
    let firstOr = []
    let secondOr = []

    if(!hasPermissionMovePetition) {
      firstOr = [{
        user: userId
      },{
        users: userId
      }]
    } else {
      firstOr = [{
        unitPath: {
          $in: userInf.units
        }
      },{
        users: userId
      }]
    }
    if(status.length) {
      objSearch.statusJob = {
        $in: status
      }
    }
    if(isListExpire) {
      objSearch.expiredTime = {
        $lt: Date.now() + warningTime
      }
      objSearch.statusJob = {
        $nin: [CONSTANTS.STATUS_JOB.HOAN_THANH, CONSTANTS.STATUS_JOB.DA_TU_CHOI]
      }
    }
    if(isDuplicate) {
      objSearch.isDuplicate = true
    }
    if(maybeSpam) {
      objSearch.maybeSpam = true
    }
    if(category) {
      objSearch.category = category
    }
    if(unit) {
      objSearch.unitPath = unit
    }
    if(user) {
      objSearch.users = unit
    }
    const options = {
      limit,
      skip,
      sort: "-updatedAt",
    };
    if(member) {
      objSearch.member = member
    } else if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      secondOr = [
        {
          title : {
            $regex,
            $options: 'i'
          }
        },
        {
          content : {
            $regex,
            $options: 'i'
          }
        },
        {
          address : {
            $regex,
            $options: 'i'
          }
        },
        {
          code : textSearch
        }
      ]
    }
    objSearch['$and'] = [
      { $or: firstOr }
    ]
    if(secondOr.length){
      objSearch['$and'].push({$or: secondOr})
    }
    Petition.find(objSearch, "", options)
    .populate("unit", "name")
    .populate("district","name")
    .populate("ward","name")
    .populate("member", "name phone")
    .populate("category", "name icon")
    .lean()
    .exec((err, results) => {

      if (err) {
        return next(err);
      }
      let res = {
        code: CONSTANTS.CODE.SUCCESS,
        data: results
      }
      next(null, res);
    });

  }

  async.waterfall([
    checkParams,
    getUserInf,
    getMemberInf,
    getConfigExpiredTime,
    listPetition
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}