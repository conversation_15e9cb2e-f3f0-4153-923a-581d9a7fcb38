const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const PushNotifyManager = require('../../../../job/pushNotify');
const NotifyManager = require('../../../../job/notifyManager');

// Duyệt phản ánh
/*
  input: userId, _id, replyContent, replyAttachments,
  output: Duyệt phản ánh, phản ánh chuyển sang đã xử lý, job chuyển sang hoàn thành
*/
// chưa có handle push
// check user đang nhận phản ánh xem có can thiệp được không
/*
  STATUS: DANG_XU_LY
  DANG_XU_LY =>  DA_XU_LY;

  STATUS_JOB: CHO_DUYET_KQ
  DANG_XU_LY =>  HOAN_THANH;
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const isPublic = _.get(req.body,'isPublic', false);

  let petition;

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(!_.has(req, 'body.isPublic')){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_PUBLIC
      })
    }

    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.DANG_XU_LY) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_DUYET_KQ) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        next();
      })
  }


  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.DANG_XU_LY]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_DUYET_KQ]
      }
    }

    let objUpdate = {
      status: CONSTANTS.STATUS.DA_XU_LY,
      statusJob: CONSTANTS.STATUS_JOB.HOAN_THANH,
      updatedAt: Date.now(),
      replyAt: Date.now(),
      isPublic,
      isExpired: Date.now() > petition.expiredTime ? true : false,
      expiredTimeJob:null,
      warningTimeJob:null
    }
    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push
        if(petition.processingUser) {
          NotifyManager
          .handleNotify(petition.processingUser,{
            title: 'Thông báo',
            message: `Phản ánh kiến nghị ${petition.code} đã được duyệt kết quả`,
            data: {
              link: 'PetitionPage',
              extras: {
                _id
              }
            },
            eventName: "petition_update",
          })
        }
        UnitModel
        .findOne({
         _id: petition.processingUnit
        })
        .lean()
        .exec((err, res) => {
          if(res) {
            PushNotifyManager
            .sendToMember(petition.member.toString(), 'Phản ánh đã được xử lý', `Phản ánh ${petition.title} của ông/bà đã được ${res.name} xử lý. Vui lòng xem kết quả trong ứng dụng. Xin cảm ơn sự hợp tác của Quý vị!`, {link: 'MyPetitionDetailScreen', extras: {id: _id}}, 'petition_update','')
          }
        })

        PetitionLogModel
          .create({
            action: 'tra_loi',
            user: userId,
            petition: _id,
            description: 'Trả lời phản ánh',
            oldStatus: petition.status,
            newStatus: CONSTANTS.STATUS.DA_XU_LY,
            oldPetitionInf: petition,
            newPetitionInf: result
          })

        PetitionJobLogModel
          .create({
            action: 'duyet_ket_qua',
            user: userId,
            petition: _id,
            description: 'Duyệt kết quả',
            oldStatus: petition.statusJob,
            newStatus: CONSTANTS.STATUS_JOB.HOAN_THANH,
            oldPetitionInf: petition,
            newPetitionInf: result,
            expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
          })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}