const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const MemberModel = require('../../../../models/member')
const validator = require('validator')
var ObjectId = require('mongodb').ObjectId;

module.exports = (req, res) => {

  let userId = _.get(req, "user.id", '');
  userId = ObjectId(userId)
  const permissions = _.get(req, "user.permissions", []);
  let hasPermissionMovePetition = permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))
  const textSearch = _.get(req, "body.textSearch", '');
  const category = _.get(req, "body.category", '');
  const unit = _.get(req, "body.unit", '');
  const user = _.get(req, "body.user", '');
  let member;

  let userInf;
  let warningTime = ms('2d');
  let result = {
    "0":0,
    "1":0,
    "2":0,
    "3":0,
    "4":0,
    "5":0,
    "6":0,
    "total":0,
    "expired":0,
    "duplicate":0
  };

  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getUserInf = (next) => {
    User
    .findOne({_id: userId})
    .lean()
    .exec((err, result) => {
      if(err) {
        return next(err)
      }
      if(!result) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        })
      }
      userInf = result
      next();
    })
  }

  const getMemberInf = (next) => {
    if (!textSearch || (textSearch && !validator.isMobilePhone(textSearch, ["vi-VN"]))) {
        return next();
    }
    MemberModel
      .findOne({
        phone: textSearch
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        member = result && result._id ? result._id : ''
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, config) => {
        if(!err && config && config.config && config.config.warningTime) {
          warningTime =  ms(config.config.warningTime);
        }
        next();
      })
  }

   const countTab =  (next) => {
    let objSearch = {
      createdAt:{
        $gt: Date.now() - ms('30d')
      },
      isDuplicate: {
        $ne: true
      }
    }
    let firstOr = []
    let secondOr = []
    if(!hasPermissionMovePetition) {
      firstOr = [{
        user: userId
      },{
        users: userId.toString()
      }]
    } else {
      firstOr = [{
        unitPath: {
          $in: userInf.units
        }
      },{
        users: userId.toString()
      }]
    }

    if(category) {
      objSearch.category = ObjectId(category)
    }
    if(unit) {
      objSearch.unitPath = unit
    }
    if(user) {
      objSearch.users = ObjectId(user)
    }
    if(member) {
      objSearch.member = member
    } else if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      secondOr = [
        {
          title : {
            $regex,
            $options: 'i'
          }
        },
        {
          content : {
            $regex,
            $options: 'i'
          }
        },
        {
          address : {
            $regex,
            $options: 'i'
          }
        },
        {
          code : textSearch
        }
      ]
    }
    objSearch['$and'] = [
      { $or: firstOr }
    ]
    if(secondOr.length){
      objSearch['$and'].push({$or: secondOr})
    }
    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: "$statusJob",
          count: { $sum: 1 },
        },
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      results.map(item => {
        result[item._id.toString()] = item.count;
        result.total += item.count
      })
      next(null)
    })
  }

  const countExpired = (next) => {
    let objSearch = {
      createdAt:{
        $gt: Date.now() - ms('30d')
      },
      expiredTime : {
        $lt: Date.now() + warningTime
      },
      statusJob : {
        $nin: [CONSTANTS.STATUS_JOB.HOAN_THANH, CONSTANTS.STATUS_JOB.DA_TU_CHOI]
      },
      isDuplicate: {
        $ne: true
      }
    }
    let firstOr = []
    let secondOr = []
    if(!hasPermissionMovePetition) {
      firstOr= [{
        user: userId
      },{
        users: userId
      }]
    } else {
      firstOr = [{
        unitPath: {
          $in: userInf.units
        }
      },{
        users: userId
      }]
    }

    if(category) {
      objSearch.category = category
    }
    if(unit) {
      objSearch.unitPath = unit
    }
    if(user) {
      objSearch.users = unit
    }

    if(member) {
      objSearch.member = member
    } else if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      secondOr = [
        {
          title : {
            $regex,
            $options: 'i'
          }
        },
        {
          content : {
            $regex,
            $options: 'i'
          }
        },
        {
          address : {
            $regex,
            $options: 'i'
          }
        },
        {
          code : textSearch
        }
      ]
    }
    objSearch['$and'] = [
      { $or: firstOr }
    ]
    if(secondOr.length){
      objSearch['$and'].push({$or: secondOr})
    }
    Petition
      .count(objSearch)
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        result["expired"] = count;
        next();
      })
  }

  const countSpam = (next) => {
    let objSearch = {
      createdAt:{
        $gt: Date.now() - ms('30d')
      },
      maybeSpam : true,
      isDuplicate: {
        $ne: true
      }
    }
    let firstOr = []
    let secondOr = []
    if(!hasPermissionMovePetition) {
      firstOr = [{
        user: userId
      },{
        users: userId
      }]
    } else {
      firstOr = [{
        unitPath: {
          $in: userInf.units
        }
      },{
        users: userId
      }]
    }

    if(category) {
      objSearch.category = category
    }
    if(unit) {
      objSearch.unitPath = unit
    }
    if(user) {
      objSearch.users = unit
    }
    if(member) {
      objSearch.member = member
    } else if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      secondOr = [
        {
          title : {
            $regex,
            $options: 'i'
          }
        },
        {
          content : {
            $regex,
            $options: 'i'
          }
        },
        {
          address : {
            $regex,
            $options: 'i'
          }
        },
        {
          code : textSearch
        }
      ]
    }
    objSearch['$and'] = [
      { $or: firstOr }
    ]
    if(secondOr.length){
      objSearch['$and'].push({$or: secondOr})
    }
    Petition
      .count(objSearch)
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        result["maybeSpam"] = count;
        next();
      })
  }

  const countDuplicate = (next) => {
    let objSearch = {
      createdAt:{
        $gt: Date.now() - ms('30d')
      },
      isDuplicate : true
    }
    let firstOr = []
    let secondOr = []
    if(!hasPermissionMovePetition) {
      firstOr = [{
        user: userId
      },{
        users: userId
      }]
    } else {
      firstOr = [{
        unitPath: {
          $in: userInf.units
        }
      },{
        users: userId
      }]
    }

    if(category) {
      objSearch.category = category
    }
    if(unit) {
      objSearch.unitPath = unit
    }
    if(user) {
      objSearch.users = unit
    }
    if(member) {
      objSearch.member = member
    } else if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      secondOr = [
        {
          title : {
            $regex,
            $options: 'i'
          }
        },
        {
          content : {
            $regex,
            $options: 'i'
          }
        },
        {
          address : {
            $regex,
            $options: 'i'
          }
        },
        {
          code : textSearch
        }
      ]
    }
    objSearch['$and'] = [
      { $or: firstOr }
    ]
    if(secondOr.length){
      objSearch['$and'].push({$or: secondOr})
    }
    Petition
      .count(objSearch)
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        result["duplicate"] = count;
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      })
  }

  async.waterfall([
    checkParams,
    getUserInf,
    getMemberInf,
    getConfigExpiredTime,
    countTab,
    countExpired,
    countSpam,
    countDuplicate
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}