const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const PushNotifyManager = require('../../../../job/pushNotify');
const UnitModel = require('../../../../models/unit')

//Tiếp nhận xử lý
/*
  Chỉ được tiếp nhận xử lý với Phản ánh thuộc đơn vị của mình và có quyền điều phối, nếu không có quyền điều phối, phản ánh phải được gán cho người đó
*/
// chưa có handle push
/*
  STATUS: CHO_TIEP_NHAN => DANG_XU_LY, DA_TIEP_NHAN => DANG_XU_LY

  STATUS_JOB: CHO_TN_PA, CHO_XU_LY, CHO_PHAN_PHOI

  Với cấp Quận Huyện:
  CHO_TN_PA => DANG_XU_LY, CHO_XU_LY => DANG_XU_LY

  Với các cấp dưới
  CHO_XU_LY => DANG_XU_LY

  Với cấp Văn phòng UBND Tỉnh
  CHO_PHAN_PHOI => DANG_XU_LY

*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  const _id = _.get(req.body, '_id', '')
  const permissions = _.get(req.user, 'permissions', [])
  let petition;
  let expiredTimeJob, warningTimeJob
  let configProcessing = {}

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }


  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.CHO_TIEP_NHAN && result.status !== CONSTANTS.STATUS.DA_TIEP_NHAN) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_TN_PA && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_XU_LY && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, data) => {
        if(!err && data && data.config && data.config.time) {
          configProcessing = data.config
          expiredTimeJob = configProcessing && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].time? ms(configProcessing[petition.statusJob.toString()].time) + Date.now() : null;
          warningTimeJob = expiredTimeJob && configProcessing[petition.statusJob.toString()] && configProcessing[petition.statusJob.toString()].warningTime ? expiredTimeJob - ms(configProcessing[petition.statusJob.toString()].warningTime) : null
        }
        next();
      })
  }

  const checkUser = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        let isLeader =  permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))
        if((isLeader && !petition.user && result.units.map(String).includes(petition.unit.toString()))
          || (petition.user && petition.user.toString() === userId)
        ) {
          next()
        } else {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.DONT_HAVE_PERMISSION_HANDLE
          })
        }
      })
  }

  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.CHO_TIEP_NHAN, CONSTANTS.STATUS.DA_TIEP_NHAN]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_XU_LY, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI]
      }
    }

    let objUpdate = {
      status: CONSTANTS.STATUS.DANG_XU_LY,
      statusJob: CONSTANTS.STATUS_JOB.DANG_XU_LY,
      user: userId,
      users: [...petition.users, userId],
      updatedAt: Date.now(),
      processingUnit: petition.unit,
      processingUser: userId,
      expiredTimeJob,
      warningTimeJob
    }
    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push
        UnitModel
         .findOne({
          _id: petition.unit
         })
         .lean()
         .exec((err, res) => {
          if(res) {
            PushNotifyManager
              .sendToMember(petition.member.toString(), 'Phản ánh đang được xử lý', `Phản ánh ${petition.title} của Ông/bà đang được ${res.name} xử lý. Chúng tôi sẽ cập nhật kết quả trong thời gian sớm nhất!`, {link: 'MyPetitionDetailScreen', extras: {id: _id}}, 'petition_update','')
          }
         })

        if(!petition.user) {
          PetitionJobLogModel
          .create({
            action: 'chuyen_xu_ly',
            user: userId,
            petition: _id,
            description: `Chuyển xử lý đến cán bộ`,
            oldStatus: petition.statusJob,
            newStatus: CONSTANTS.STATUS_JOB.CHO_XU_LY,
            oldPetitionInf: petition,
            newPetitionInf: petition,
            fromPerson: null,
            fromUnit: petition.unit,
            toUnit: null,
            toPerson: userId,
            expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
          }, () => {
            PetitionLogModel
            .create({
              action: 'tiep_nhan_xu_ly',
              user: userId,
              petition: _id,
              description: 'Tiếp Nhận xử lý',
              oldStatus: petition.status,
              newStatus: CONSTANTS.STATUS.DANG_XU_LY,
              oldPetitionInf: petition,
              newPetitionInf: result
            })

          PetitionJobLogModel
            .create({
              action: 'tiep_nhan_xu_ly',
              user: userId,
              petition: _id,
              description: 'Tiếp Nhận xử lý',
              oldStatus: petition.statusJob,
              newStatus: CONSTANTS.STATUS_JOB.DANG_XU_LY,
              oldPetitionInf: petition,
              newPetitionInf: result,
              expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
            })
          })
        } else {
          PetitionLogModel
          .create({
            action: 'tiep_nhan_xu_ly',
            user: userId,
            petition: _id,
            description: 'Tiếp Nhận xử lý',
            oldStatus: petition.status,
            newStatus: CONSTANTS.STATUS.DANG_XU_LY,
            oldPetitionInf: petition,
            newPetitionInf: result
          })

        PetitionJobLogModel
          .create({
            action: 'tiep_nhan_xu_ly',
            user: userId,
            petition: _id,
            description: 'Tiếp Nhận xử lý',
            oldStatus: petition.statusJob,
            newStatus: CONSTANTS.STATUS_JOB.DANG_XU_LY,
            oldPetitionInf: petition,
            newPetitionInf: result,
            expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
          })
        }


        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    getConfigExpiredTime,
    checkUser,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}