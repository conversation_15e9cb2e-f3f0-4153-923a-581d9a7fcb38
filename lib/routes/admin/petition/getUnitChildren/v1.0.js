const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const UnitModel = require('../../../../models/unit')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')

module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  let userInf;

  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getUserInf = (next) => {
    User.findById(userId)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(!result.units || !result.units.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.PETITION.NOT_FOUND_UNIT
          })
        }
        userInf = result;
        next();
      })
  }

  const getUserByUnit = (next) => {
    UnitModel.find({
      parent: {
        $in: userInf.units
      },
      status: 1
    })
    .lean()
    .select('name icon')
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      if(!results.length) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.PETITION.NOT_FOUND_USER
        })
      }
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: results
      });
    })
  }


  async.waterfall([
    checkParams,
    getUserInf,
    getUserByUnit
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}