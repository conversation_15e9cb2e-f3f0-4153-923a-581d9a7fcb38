const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const MemberModel = require('../../../../models/member')
const validator = require('validator')

module.exports = (req, res) => {

  const limit = _.get(req, "body.limit", 10);
  const page = _.get(req, "body.page", 0);
  let sort = -1;

  const userId = _.get(req, "user.id", '');
  const tab = _.get(req, "body.tab", 0);
  const permissions = _.get(req, "user.permissions", []);
  const textSearch = _.get(req, "body.textSearch", '');
  const category = _.get(req, "body.category", '');

  let userInf;
  let warningTime = ms('2d');
  let member;

  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getUserInf = (next) => {
    User
    .findOne({_id: userId})
    .lean()
    .exec((err, result) => {
      if(err) {
        return next(err)
      }
      if(!result) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        })
      }
      userInf = result
      next();
    })
  }

  const getMemberInf = (next) => {
    if (!textSearch || (textSearch && !validator.isMobilePhone(textSearch, ["vi-VN"]))) {
        return next();
    }
    MemberModel
      .findOne({
        phone: textSearch
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        member = result && result._id ? result._id : ''
        next();
      })
  }

  const getConfigExpiredTime = (next) => {
    ConfigModel
      .findOne({
        type:1
      })
      .lean()
      .exec((err, config) => {
        if(!err && config && config.config && config.config.warningTime) {
          warningTime =  ms(config.config.warningTime);
        }
        next();
      })
  }


  const listPetition = (next) => {
    const skip = page * limit;
    let hasPermissionMovePetition = permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))

    let objSearch = {
      isDuplicate: {
        $ne: true
      }
    }
    let firstOr = []
    let secondOr = []

    if(tab == 0) {
      firstOr = [
        {user: userId}
      ]
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI,CONSTANTS.STATUS_JOB.CHO_XU_LY]
      }
      if(hasPermissionMovePetition) {
        firstOr.push({
          user: null,
          unit: {
            $in: userInf.units
          }
        })
      }
      sort=1
    } else if(tab == 1) {
      if(hasPermissionMovePetition) {
        firstOr=[{
          user: null,
          unit: {
            $in: userInf.units
          }
        },{
          user: userId
        }]
      } else {
        objSearch.user = userId
      }
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.DANG_XU_LY]
      }
    } else if(tab == 2) {
      firstOr = [
        {
          user: userId,
          statusJob: {
            $in:[CONSTANTS.STATUS_JOB.CHO_XU_LY, CONSTANTS.STATUS_JOB.CHO_DUYET_KQ,CONSTANTS.STATUS_JOB.DANG_XU_LY]
          }
        }
      ]
      if(hasPermissionMovePetition) {
        firstOr.push({
          user: null,
          unit: {
            $in: userInf.units
          },
          statusJob:{
            $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI,CONSTANTS.STATUS_JOB.CHO_XU_LY]
          }
        })
      }
      objSearch.expiredTime = {
        $lt: Date.now() + warningTime
      }
    } else if(tab == 3) {
      if(hasPermissionMovePetition) {
        firstOr=[{
          user: null,
          unit: {
            $in: userInf.units
          }
        },{
          user: userId
        }]
      } else {
        objSearch.user = userId
      }
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.CHO_DUYET_KQ]
      }
    } else if(tab == 4) {
      objSearch.user = userId
      objSearch.statusJob = {
        '$in':[CONSTANTS.STATUS_JOB.HOAN_THANH, CONSTANTS.STATUS_JOB.DA_TU_CHOI]
      }
    } else if(tab == 5) {
      if(hasPermissionMovePetition) {
        firstOr=[{
          user: null,
          unit: {
            $in: userInf.units
          }
        },{
          user: userId
        }]
      } else {
        objSearch.user = userId
      }
      objSearch.maybeSpam = true
    }
    if(member) {
      objSearch.member = member
    } else if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      secondOr = [
        {
          title : {
            $regex,
            $options: 'i'
          }
        },
        {
          content : {
            $regex,
            $options: 'i'
          }
        },
        {
          address : {
            $regex,
            $options: 'i'
          }
        },
        {
          code : textSearch
        }
      ]
    }
    if(firstOr.length) {
      objSearch['$and'] = [
        { $or: firstOr }
      ]
      if(secondOr.length){
        objSearch['$and'].push({$or: secondOr})
      }
    } else {
      if(secondOr.length){
        objSearch['$or'] = secondOr
      }
    }

    if(category) {
      objSearch.category = category
    }

    const options = {
      limit,
      skip,
      sort: sort == 1 ? "createdAt" : "-updatedAt"
    };
    Petition.find(objSearch, "", options)
    .populate("unit", "name")
    .populate("district","name")
    .populate("ward","name")
    .populate("member", "name phone")
    .populate("category", "name icon")
    .lean()
    .exec((err, results) => {
      if (err) {
        return next(err);
      }
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: results
      });
    });

  }

  async.waterfall([
    checkParams,
    getUserInf,
    getMemberInf,
    getConfigExpiredTime,
    listPetition
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}