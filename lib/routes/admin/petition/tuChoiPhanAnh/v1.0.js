const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const PushNotifyManager = require('../../../../job/pushNotify');

//Từ chối phản ánh
/*
  input: userId, _id, replyContent, replyAttachments,
  output: Từ chối phản ánh, gửi nội dung về cho người dùng, chuyển trạng thái về từ chối
*/
// chưa có handle push
// check user đang nhận phản ánh xem có can thiệp được không
/*
  STATUS: CHO_TIEP_NHAN,DA_TIEP_NHAN

  STATUS_JOB: CHO_TN_PA, CHO_XU_LY, CHO_PHAN_PHOI
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const replyContent = _.get(req.body,'replyContent', '');
  const replyAttachments = _.get(req.body,'replyAttachments', []);

  let petition;

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!replyContent) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_REFUSE_CONTENT
      })
    }

    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.CHO_TIEP_NHAN && result.status !== CONSTANTS.STATUS.DA_TIEP_NHAN) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_TN_PA && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_XU_LY && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        next();
      })
  }

  const checkUserUnit = (next) => {
    User
      .findOne({
        _id: userId
      })
      .select('units')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.WRONG_PARAMS
          })
        }
        if(!result.units.map(String).includes(petition.unit.toString())) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_UNIT
          })
        }
        next();
      })
  }

  const updateStatus = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.CHO_TIEP_NHAN, CONSTANTS.STATUS.DA_TIEP_NHAN]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_XU_LY, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI]
      }
    }
    if(petition.user) {
      query.user = petition.user
    }

    let objUpdate = {
      status: CONSTANTS.STATUS.DA_TU_CHOI,
      statusJob: CONSTANTS.STATUS_JOB.DA_TU_CHOI,
      user: userId,
      updatedAt: Date.now(),
      replyAt: Date.now(),
      replyContent,
      replyAttachments,
      expiredTimeJob: null,
      warningTimeJob: null
    }
    Petition
      .findOneAndUpdate(query, objUpdate,{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.UPDATE_FAIL
          })
        }
        //handle push
        PushNotifyManager
          .sendToMember(petition.member.toString(), 'Phản ánh không được tiếp nhận', `Phản ánh  ${petition.title} của ông/bà không được tiếp nhận vì không phù hợp tiêu chí xử lý. Ấn để xem lý do chi tiết!`, {link: 'MyPetitionDetailScreen', extras: {id: _id}}, 'petition_update','')
        PetitionLogModel
          .create({
            action: 'tu_choi_phan_anh',
            user: userId,
            petition: _id,
            description: 'Từ chối phản ánh',
            oldStatus: petition.status,
            newStatus: CONSTANTS.STATUS.DA_TU_CHOI,
            oldPetitionInf: petition,
            newPetitionInf: result
          })

        PetitionJobLogModel
          .create({
            action: 'tu_choi_phan_anh',
            user: userId,
            petition: _id,
            description: 'Từ chối phản ánh',
            oldStatus: petition.statusJob,
            newStatus: CONSTANTS.STATUS_JOB.DA_TU_CHOI,
            oldPetitionInf: petition,
            newPetitionInf: result,
            expiredTime: petition.expiredTimeJob ? petition.expiredTimeJob : null
          })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        });
      })

  }


  async.waterfall([
    checkParams,
    checkStatus,
    checkUserUnit,
    updateStatus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}