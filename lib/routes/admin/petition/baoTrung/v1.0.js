const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const PushNotifyManager = require('../../../../job/pushNotify');

//Báo trùng
/*
  input: userId, _id, originalPetitionId
  output: Gán trùng phản ánh với phản ánh gốc, cập nhật kết quả xử lý cho phản ánh giống phản ánh gốc nếu phản ánh gốc đã xử lý
*/
// chưa có handle push
/*
  STATUS: CHO_TIEP_NHAN, DA_TIEP_NHAN
  STATUS_JOB: CHO_TN_PA, CHO_XU_LY

  Cơ bản trạng thái Job không thay đổi, chỉ cần check
*/
module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');
  const _id = _.get(req.body, '_id', '')
  const originalPetitionId = _.get(req.body,'originalPetitionId', '');
  const permissions = _.get(req.user, 'permissions', [])
  let petitionOriginal;
  let petition;

  const checkParams = (next) => {
    if(!userId || !_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!originalPetitionId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PETITION.MISSING_ORIGINAL_PETITION_ID
      })
    }

    next()
  }



  const checkStatus = (next) => {
    Petition
      .findOne({
        _id
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petition = result;
        if(result.status !== CONSTANTS.STATUS.CHO_TIEP_NHAN && result.status !== CONSTANTS.STATUS.DA_TIEP_NHAN) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }
        if(result.statusJob !== CONSTANTS.STATUS_JOB.CHO_TN_PA && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_XU_LY && result.statusJob !== CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS_JOB
          })
        }

        next();
      })
  }


  const checkUser = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        let isLeader =  permissions.some(permission => (permission.code === 'admin-all' || permission.code === 'dieu-chuyen-phan-anh'))
        if((isLeader && !petition.user && result.units.map(String).includes(petition.unit.toString()))
          || (petition.user && petition.user.toString() === userId)
        ) {
          next()
        } else {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.DONT_HAVE_PERMISSION_DOUPLICATE
          })
        }
      })
  }


  const findOriginalPetition = (next) => {
    Petition
      .findOne({
        _id: originalPetitionId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.NOT_FOUND
          })
        }

        petitionOriginal = result;
        if(result.isDuplicate) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PETITION.WRONG_STATUS
          })
        }

        next();
      })
  }


  const updateDuplicatePetition = (next) => {
    let query = {
      _id,
      status: {
        $in: [CONSTANTS.STATUS.CHO_TIEP_NHAN, CONSTANTS.STATUS.DA_TIEP_NHAN]
      },
      statusJob: {
        $in: [CONSTANTS.STATUS_JOB.CHO_TN_PA, CONSTANTS.STATUS_JOB.CHO_XU_LY, CONSTANTS.STATUS_JOB.CHO_PHAN_PHOI]
      }
    }
    let objUpdate = {
      updatedAt: Date.now(),
      isDuplicate: true,
      originalPetition: originalPetitionId
    }
    if(petitionOriginal.status === CONSTANTS.STATUS.DA_XU_LY || petitionOriginal.status === CONSTANTS.STATUS.DA_TU_CHOI) {
      objUpdate.status = petitionOriginal.status;
      objUpdate.statusJob = petitionOriginal.statusJob;
      objUpdate.replyAt = Date.now();
      objUpdate.replyContent = petitionOriginal.replyContent;
      objUpdate.replyAttachments = petitionOriginal.replyAttachments;
    }

    Petition
      .findOneAndUpdate(
        query, objUpdate,
        {new:true})
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err)
          }

          if(!result) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.PETITION.UPDATE_FAIL
            })
          }
          //handle push
          if(petitionOriginal.status === CONSTANTS.STATUS.DA_XU_LY || petitionOriginal.status === CONSTANTS.STATUS.DA_TU_CHOI) {
            if(petitionOriginal.status === CONSTANTS.STATUS.DA_XU_LY) {
              PushNotifyManager
                .sendToMember(petitionOriginal.member.toString(), 'Phản ánh đã được xử lý', `Phản ánh  #${petitionOriginal.code} của ông/bà đã được cơ quan chức năng xử lý. Vui lòng xem kết quả trong ứng dụng. Xin cảm ơn sự hợp tác của Quý vị!`, {link: 'MyPetitionDetailScreen', extras: {id: _id}}, 'petition_update','')
            } else {
              PushNotifyManager
                .sendToMember(petitionOriginal.member.toString(), 'Phản ánh không được tiếp nhận', `Phản ánh  #${petitionOriginal.code} của ông/bà không được tiếp nhận vì không phù hợp tiêu chí xử lý. Ấn để xem lý do chi tiết!`, {link: 'MyPetitionDetailScreen', extras: {id: _id}}, 'petition_update','')
            }
            PetitionLogModel
            .create({
              action: 'gan_trung_phan_anh',
              user: userId,
              petition: _id,
              description: 'Phản ánh bị trùng',
              oldStatus: petition.status,
              newStatus: result.statusJob,
              oldPetitionInf: petition,
              newPetitionInf: result
            })
          }

          PetitionJobLogModel
            .create({
              action: 'gan_trung_phan_anh',
              user: userId,
              petition: _id,
              description: `Gán trùng với phản ánh gốc ${petitionOriginal.code}`,
              oldStatus: petition.statusJob,
              newStatus: result.statusJob,
              oldPetitionInf: petition,
              newPetitionInf: result
            })

          next(null,{
            code: CONSTANTS.CODE.SUCCESS
          });
        })

  }

  async.waterfall([
    checkParams,
    checkStatus,
    checkUser,
    findOriginalPetition,
    updateDuplicatePetition
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}