const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const Instruction = require('../../../../models/instruction')
const MESSAGES = require('../../../../message');


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let updatedData = {};
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      })
    }

    next();
  }

  const inactiveInstruction = (next) => {
    Instruction
      .update({ _id: id }, { active: 0 })
      .exec((err, result) => {
        updatedData = result;
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'inactive_instruction',
        description: `<PERSON><PERSON><PERSON> bài hướng dẫn`,
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    inactiveInstruction,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}