const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Instruction = require('../../../../models/instruction')
const tool = require('../../../../util/tool')


module.exports = (req, res) => {
  let title = req.body.title || '';
  let image = req.body.image || '';
  let video = req.body.video || '';
  let description = req.body.description || '';
  let category = req.body.category || '';
  let content = req.body.content || '';
  let url = req.body.url || '';
  let updatedData = {};
  const checkParams = (next) => {
    if (!title || !image || (!content && !url)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const createInstruction = (next) => {

    let objCreate = {
      title,
      titleAlias: tool.change_alias(title),
      image,
      video,
      description,
      content,
      url
    }

    if (category) {
      objCreate.category = category;
    }

    Instruction
      .create(objCreate, (err, result) => {
        updatedData = result;
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'create_instruction',
        description: 'Tạo bài hướng dẫn mới',
        data: req.body,
        updatedData
      },() =>{})
   }


  async.waterfall([
    checkParams,
    createInstruction,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}