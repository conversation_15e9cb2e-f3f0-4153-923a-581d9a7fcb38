const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/instruction'); //<PERSON> tê<PERSON> cho dễ nhân bản
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const sort = _.get(req, 'body.sort', 0);
  const textSearch = _.get(req, 'body.textSearch', '');
  const isFilter = _.get(req, 'body.isFilter', 0);
  const active = _.get(req, 'body.active');
  let query = {};
  let count = 0;

  const checkParams = (next) => {
    if (textSearch.trim() && textSearch.length > 0) {
      query = {
        $or: [
          { name: new RegExp(textSearch, 'gi') },
          { nameAlias: new RegExp(tool.change_alias(textSearch), 'gi') },
          { address: new RegExp(textSearch, 'gi') },
          { phone: new RegExp(textSearch, 'gi') },
        ],
      };
    }

    if (_.isNumber(active)) {
      query.active = active;
    }

    next();
  };

  const countQuery = (next) => {
    Model.count(query)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const list = (next) => {
    let filterOptions = {
      limit,
      skip: limit * page,
      sort: sort === 1 ? 'updatedAt' : '-updatedAt',
    };

    Model.find(query, '-nameAlias', filterOptions)
      .populate('category')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, countQuery, list], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
