const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Instruction = require('../../../../models/instruction')
const tool = require('../../../../util/tool')


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let title = req.body.title || '';
  let image = req.body.image || '';
  let video = req.body.video || '';
  let description = req.body.description || '';
  let category = req.body.category || '';
  let content = req.body.content || '';
  let url = req.body.url || '';
  let updatedData = {};
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const modifyInstruction = (next) => {
    let objModify = {
      title,
      titleAlias: tool.change_alias(title),
      image,
      video,
      description,
      category,
      content,
      url
    }

    Instruction
      .update({ _id: id }, objModify)
      .exec((err, result) => {
        updatedData = result;
        next(err)
      })
  }


  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'modify_instruction',
        description: `Cập nhật bài hướng dẫn`,
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    modifyInstruction,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
