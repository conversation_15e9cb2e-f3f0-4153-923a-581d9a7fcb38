const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Instruction = require('../../../../models/instruction');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');

  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    next();
  };

  const getInfo = (next) => {
    Instruction.findById(id).exec((err, result) => {
      if (err) {
        return next(err);
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: result,
      });
    });
  };

  async.waterfall([checkParams, getInfo], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
