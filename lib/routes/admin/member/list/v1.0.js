const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/member'); //<PERSON> tên cho dễ nhân bản
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const sort = _.get(req, 'body.sort', 0);
  const textSearch = _.get(req, 'body.textSearch', '');
  let query = {};
  let count = 0;

  const checkParams = (next) => {
    if (textSearch.trim() && textSearch.length > 0) {
      query = {
        $or: [
          { name: new RegExp(textSearch, 'gi') },
          { email: new RegExp(textSearch, 'gi') },
          { idCard: new RegExp(textSearch, 'gi') },
          { phone: new RegExp(textSearch, 'gi') },
          { code: new RegExp(textSearch, 'gi') },
        ],
      };
    }

    next();
  };

  const countQuery = (next) => {
    Model.count(query)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const list = (next) => {
    let filterOptions = {
      limit,
      skip: limit * page,
      sort: '-createdAt',
    };

    Model.find(query, '-password', filterOptions)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count
        });
      });
  };

  async.waterfall([checkParams, countQuery, list], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
