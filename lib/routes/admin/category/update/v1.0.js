
const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Category = require('../../../../models/category')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const idCategory = req.body._id || '';
  const icon = req.body.icon || '';

  let categoryInf;
  let updatedData = {};

  const checkParams = (next) => {
    if(!idCategory){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!name){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CATEGORY.NOT_FOUND_NAME
      })
    }

    if(!icon){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CATEGORY.NOT_FOUND_ICON
      })
    }

    next();
  }

  const checkCategoryExists = (next) => {

    Category
      .findById(idCategory)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.CATEGORY.CATEGORY_NOT_EXISTS
          })
        }
        categoryInf = result
        next()
      })

  }

  const checkNameNotExists = (next) => {

    Category
      .findOne({
        $or:[
          {
            name: name.trim()
          }
        ],
        _id: {
          $ne: idCategory
        },
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.CATEGORY.CATEGORY_EXISTS
          })
        }
        next()
      })

  }


  const createCategory = (next) => {

    let objModify = {
      updatedAt: Date.now(),
      name : name.trim(),
      icon
    }

    Category
      .update({
        _id: idCategory
      },objModify,(err, result) => {
        updatedData = result;
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.CATEGORY.UPDATE_SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'modify_category',
        description: 'Chỉnh sửa dah mục',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    checkCategoryExists,
    checkNameNotExists,
    createCategory,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
