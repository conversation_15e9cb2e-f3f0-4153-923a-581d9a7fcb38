const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Category = require('../../../../models/category')


module.exports = (req, res) => {

  let name = req.body.name || '';
  let icon = req.body.icon || '';
  let order = 0;
  let updatedData = {};
  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CATEGORY.NOT_FOUND_NAME
      })
    }
    if(!icon){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CATEGORY.NOT_FOUND_ICON
      })
    }
    next();
  }

  const checkCategoryExists = (next) => {

    Category
      .find({
        $or:[
          {name, icon}
        ]
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        if(results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.CATEGORY.CATEGORY_EXISTS
          })
        }
        next()
      })

  }

  const getBiggestOrder = (next) => {
    Category
      .findOne({})
      .sort({order: -1})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        order = result ? result.order + 1 : req.body.order || 1;
        next();
      })
  }

  const createCategory = (next) => {

    let objCreate = {
      name,
      icon,
      order
    }


    Category
      .create(objCreate,(err, result) => {
        updatedData = result;
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null,{
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.CATEGORY.CREATE_SUCCESS,
      data: updatedData._id
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'create_category',
        description: 'Tạo dannh mục mới',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    checkCategoryExists,
    getBiggestOrder,
    createCategory,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}