const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Category = require('../../../../models/category')


module.exports = (req, res) => {
  const idCategory = req.body._id || '';
  const status = req.body.status || 0;
  let categoryInf;
  let data;

  const checkParams = (next) => {
    if(!idCategory){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }


  const deactiveCategory = (next) => {

    Category
      .findOneAndUpdate({
        _id: idCategory
      },{
        status
      },(err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.CATEGORY.CATEGORY_NOT_EXISTS
          })
        }
        data = result
        next()
      })
  }


  const writeLog = (next) => {
    next(null, {
     code: CONSTANTS.CODE.SUCCESS,
     data
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'inactive_category',
        description: status ? 'Mở lại danh mục' : 'Tắt danh mục',
        data: req.body,
        updatedData: data
      },() =>{})
   }

  async.waterfall([
    checkParams,
    deactiveCategory
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}