const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const UnitModel = require('../../../../models/unit')
const Excel = require('exceljs');
const path = require('path');
const ABSPATH = path.dirname(require.main.filename);

module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  let petition;
  const month = _.get(req, "body.month","")
  const year = _.get(req, "body.year","")
  let startTime = Date.now();
  let endTime = Date.now();
  const unit = _.get(req, "body.unit", '');
  let currentUnit;

  let defaultPetitionUnits = []
  let defaultPetitionUnitIds = []
  let seftPetitionUnitIds = []

  let unitChoose;
  let dataTotal = [];
  let records = [];
  const checkParams = (next) => {
    if(!userId || !month || !year){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    const date = new Date(year, month - 1, 1);
    startTime = date.getTime();
    const endDate = new Date(year, month, 1);
    endTime = endDate.getTime();
    next()
  }


  const getUserInf = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result){
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(result.units.length == 1){
          unitChoose = result.units[0]
        } else {
          if(!unit) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.STATISTIC.MISSING_UNIT
            })
          } else {
            unitChoose = unit
          }
        }
        next();
      })
  }

  const getUnitChildren = (next) => {
    UnitModel
      .find({
        $or:[
          {parent: unitChoose},
          {_id: unitChoose}
        ]
      })
      .select('name defaultPetitionDistrict defaultPetitionWard')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }
        results.map((unit) => {
          if(unit._id.toString() === unitChoose.toString()) {
            currentUnit = {
              _id: unitChoose._id,
              name: unit.name,
              onTimeCount: 0, expiredCount: 0, totalCount: 0
            }
            seftPetitionUnitIds.push(unit._id)
          } else {
            if(unit.defaultPetitionDistrict || unit.defaultPetitionWard) {
              defaultPetitionUnitIds.push(unit._id)
              defaultPetitionUnits.push({
                _id: unit._id,
                name: unit.name,
                onTimeCount: 0, expiredCount: 0, totalCount: 0
              })
            } else {
              seftPetitionUnitIds.push(unit._id)
            }
          }
        })
        next();
      })

  }


  const statisticStatusSelf = (next) => {
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      processingUnit: {
        $in: seftPetitionUnitIds
      },
      status: CONSTANTS.STATUS.DA_XU_LY,
    }
    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: "$processingUnit",
          onTimeCount: {
            $sum: { $cond: [{ $eq: ["$isExpired", false] }, 1, 0] }
          },
          expiredCount: {
            $sum: { $cond: [{ $eq: ["$isExpired", true] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          _id: 0,
          unit: "$_id",
          onTimeCount: 1,
          expiredCount: 1
        }
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      results.map((result) => {
        currentUnit.onTimeCount += result.onTimeCount;
        currentUnit.expiredCount += result.expiredCount;
        currentUnit.totalCount += result.expiredCount + result.onTimeCount;
      })
      next();
    })

  }


  const statisticStatus = (next) => {
    async.mapLimit(defaultPetitionUnits,5, (unit, done) => {
      let objSearch = {
        createdAt:{
          $gt: startTime,
          $lt: endTime
        },
        unitPath: unit._id,
        status: CONSTANTS.STATUS.DA_XU_LY,
      }

      Petition
        .find(objSearch)
        .select('status')
        .lean()
        .exec((err, results) => {
          if(err) {
            return done(err);
          }
          results.map(result => {
            unit.totalCount ++
            if(result.isExpired) {
              unit.expiredCount ++
            } else {
              unit.onTimeCount ++
            }
          })
          done();
        })
    }, (err) => {
      if(err) {
        return next(err);
      }
      dataTotal = [
        ...defaultPetitionUnits,
        currentUnit
      ]
      dataTotal.sort((a, b) =>{
        return b.totalCount - a.totalCount
      })
      next();
    })

  }

  const exportExcel = (next) => {
    let index = 0
    dataTotal.forEach((record) => {
      index++
      let objPush = {
        "STT": index,
        "Đơn vị": record.name,
        "Phản ánh đã xử lý": record.totalCount,
        "Phản ánh đúng hạn": record.onTimeCount,
        "Phản ánh quá hạn": record.expiredCount,
      }
      records.push(objPush)
    })

    var workbook = new Excel.Workbook();
    var sheet = workbook.addWorksheet('1');
    sheet.addRow().values = [`Thống kê phản ánh thời hạn xử lý - ( Tháng ${month}/${year})`];
    if(records[0]) {
      sheet.addRow().values = Object.keys(records[0]);
    }
    records.forEach(function(item){
      var valueArray = [];
      valueArray = _.values(item);
      sheet.addRow().values = valueArray;
    })

    fileName = `thong-ke-thoi-han-phan-anh-${month}-${year}-${Date.now()}.xlsx`;
    filepath = `${ABSPATH}/public/uploads/petition/${fileName}`;

    workbook.xlsx.writeFile(filepath).then(function() {
      let data = `/uploads/petition/${fileName}`
      SystemLogModel.create(
        {
          user: _.get(req,'user.id', ''),
          action: 'export_statistic_time_progress_unit',
          description: 'Xuất file excel thống kê thời hạn xử lý phản ánh',
          data
        },
        () => {}
      );
      return next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data
      });
    })
  }

  async.waterfall([
    checkParams,
    getUserInf,
    getUnitChildren,
    statisticStatusSelf,
    statisticStatus,
    exportExcel
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}