const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const UnitModel = require('../../../../models/unit')
const PetitionLogModel = require('../../../../models/petitionLog')
const DistrictModel = require('../../../../models/district')
const WardModel = require('../../../../models/ward')
var ObjectId = require('mongodb').ObjectId;


module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  const unit = _.get(req, "body.unit", '');
  const category = _.get(req, "body.category", '');

  let unitChoose;
  let data = {}
  let unitInf;
  let dataDistrict, dataWard = []
  const checkParams = (next) => {
    if(!userId){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next()
  }

  const getUserInf = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result){
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(result.units.length == 1){
          unitChoose = result.units[0]
        } else {
          if(!unit) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.STATISTIC.MISSING_UNIT
            })
          } else {
            unitChoose = unit
          }
        }
        next();
      })
  }

  const statistic = (next) => {
    let objSearch = {
      unitPath: unitChoose,
      createdAt: { $gte: new Date(new Date().setMonth(new Date().getMonth() - 5)).getTime() }
    }
    if(category) {
      objSearch.category = ObjectId(category)
    }

    Petition.aggregate([
      {
        $addFields: {
          month: { $month: { $toDate: "$createdAt" } },
          year: { $year: { $toDate: "$createdAt" } }
        }
      },
      {
        $match: objSearch
      },
      {
        $group: {
          _id: { year: "$year", month: "$month" },
          count: { $sum: 1 }
        }
      },
      {
        $sort: {
          "_id.year": 1,
          "_id.month": 1
        }
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      data = results
      next();
    })
  }


  const statisticExpire = (next) => {
    let objSearch = {
      unitPath: unitChoose,
      status: CONSTANTS.STATUS.DA_XU_LY,
      createdAt: { $gte: new Date(new Date().setMonth(new Date().getMonth() - 5)).getTime() }
    }
    if(category) {
      objSearch.category = ObjectId(category)
    }

    Petition.aggregate([
      {
        $addFields: {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } }
        }
      },
      {
        $match: objSearch
      },
      {
        $group: {
          _id: { year: "$year", month: "$month" },
          onTimeCount: {
            $sum: { $cond: [{ $eq: ["$isExpired", false] }, 1, 0] }
          },
          expiredCount: {
            $sum: { $cond: [{ $eq: ["$isExpired", true] }, 1, 0] }
          }
        }
      },
      {
        $sort: {
          "_id.year": 1,
          "_id.month": 1
        }
      },
      {
        $project: {
          _id: 0,
          year: "$_id.year",
          month: "$_id.month",
          onTimeCount: 1,
          expiredCount: 1
        }
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          dataExpired: results, dataTotal: data
        }
      });
    })
  }

  async.waterfall([
    checkParams,
    getUserInf,
    statistic,
    statisticExpire
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}