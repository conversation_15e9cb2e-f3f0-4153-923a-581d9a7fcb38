const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
var ObjectId = require('mongodb').ObjectId;


module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  let petition;
  const month = _.get(req, "body.month","")
  const year = _.get(req, "body.year","")
  let startTime = Date.now();
  let endTime = Date.now();
  const unit = _.get(req, "body.unit", '');
  const category = _.get(req, "body.category", '');

  let unitChoose;
  let status = {
    "0":0,
    "1":0,
    "2":0,
    "3":0,
    "4":0,
    "expired":0
  }
  let rate = {
    "-1":0,
    "0":0,
    "1":0,
  }
  const checkParams = (next) => {
    if(!userId || !month || !year){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    const date = new Date(year, month - 1, 1);
    startTime = date.getTime();
    const endDate = new Date(year, month, 1);
    endTime = endDate.getTime();
    next()
  }

  const getUserInf = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result){
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(result.units.length == 1){
          unitChoose = result.units[0]
        } else {
          if(!unit) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.STATISTIC.MISSING_UNIT
            })
          } else {
            unitChoose = unit
          }
        }
        next();
      })
  }


  const statisticStatus = (next) => {
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      unitPath: unitChoose
    }
    if(category) {
      objSearch.category = ObjectId(category)
    }

    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      results.map(item => {
        status[item._id.toString()] = item.count;
      })
      next();
    })

  }

  const statisticExpire = (next) => {
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      unitPath: unitChoose,
      status: CONSTANTS.STATUS.DA_XU_LY,
      isExpired: true
    }
    if(category) {
      objSearch.category = category
    }

    Petition
      .count(objSearch)
      .exec((err,count) => {
        if(err) {
          return next(err)
        }
        status.expired = count
        next();
      })
  }

  const statisticRating = (next) => {
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      unitPath: unitChoose,
      status: CONSTANTS.STATUS.DA_XU_LY,
      rate: {
        $exists:true
      }
    }
    if(category) {
       objSearch.category = ObjectId(category)
    }

    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: "$rate",
          count: { $sum: 1 },
        },
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      results.map(item => {
        rate[item._id.toString()] = item.count;
      })
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data:{
          status,
          rate
        }
      });
    })
  }


  async.waterfall([
    checkParams,
    getUserInf,
    statisticStatus,
    statisticExpire,
    statisticRating
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}