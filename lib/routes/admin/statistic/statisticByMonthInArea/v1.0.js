const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const UnitModel = require('../../../../models/unit')
const PetitionLogModel = require('../../../../models/petitionLog')
const DistrictModel = require('../../../../models/district')
const WardModel = require('../../../../models/ward')
var ObjectId = require('mongodb').ObjectId;

module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  let petition;
  const month = _.get(req, "body.month","")
  const year = _.get(req, "body.year","")
  let startTime = Date.now();
  let endTime = Date.now();
  const unit = _.get(req, "body.unit", '');
  const category = _.get(req, "body.category", '');

  let unitChoose;
  let data = {}
  let unitInf;
  let dataDistrict, dataWard = []
  const checkParams = (next) => {
    if(!userId || !month || !year){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    const date = new Date(year, month - 1, 1);
    startTime = date.getTime();
    const endDate = new Date(year, month, 1);
    endTime = endDate.getTime();
    next()
  }

  const getUserInf = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result){
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(result.units.length == 1){
          unitChoose = result.units[0]
        } else {
          if(!unit) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.STATISTIC.MISSING_UNIT
            })
          } else {
            unitChoose = unit
          }
        }
        next();
      })
  }

  const findUnit = (next) => {
    UnitModel
      .findOne({
        _id: unitChoose
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data
          })
        }

        unitInf = result
        if(!unitInf.defaultPetition && !unitInf.defaultPetitionDistrict) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data
          })
        }
        next();
      })
  }


  const statisticDistrict = (next) => {
    if(!unitInf.defaultPetition) {
      return next();
    }
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      unitPath: unitChoose
    }
    if(category) {
      objSearch.category = ObjectId(category)
    }

    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: "$district",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      data = results
      dataDistrict = data.map((district) => district._id)
      next();
    })
  }

  const findDistrict = (next) => {
    if(!unitInf.defaultPetition) {
      return next();
    }

    DistrictModel
      .find({
        _id: {
          $in: dataDistrict
        }
      })
      .select('name')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }

        results.map((district) => {
          data.map(d => {
            if(district._id.toString() === d._id.toString()) {
              d.name = district.name
            }
          })
        })
        next({
          code: CONSTANTS.CODE.SUCCESS,
          data
        })
      })
  }


  const statisticWard = (next) => {
    if(!unitInf.defaultPetitionDistrict) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data
      })
    }
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      unitPath: unitChoose
    }
    if(category) {
      objSearch.category = ObjectId(category)
    }

    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: "$ward",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      data = results
      dataWard = data.map((ward) => ward._id)
      next();
    })
  }

  const findWard = (next) => {
    if(!unitInf.defaultPetitionDistrict) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data
      })
    }

    WardModel
      .find({
        _id: {
          $in: dataWard
        }
      })
      .select('name')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }

        results.map((ward) => {
          data.map(d => {
            if(ward._id.toString() === d._id.toString()) {
              d.name = ward.name
            }
          })
        })
        next({
          code: CONSTANTS.CODE.SUCCESS,
          data
        })
      })
  }

  async.waterfall([
    checkParams,
    getUserInf,
    findUnit,
    statisticDistrict,
    findDistrict,
    statisticWard,
    findWard
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}