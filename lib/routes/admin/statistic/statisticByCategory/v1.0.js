const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const Petition = require('../../../../models/petition')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const escapeStringRegexp = require('escape-string-regexp');
const ConfigModel = require('../../../../models/config')
const PetitionJobLogModel = require('../../../../models/petitionJobLog')
const PetitionLogModel = require('../../../../models/petitionLog')
const CategoryModel = require('../../../../models/category')


module.exports = (req, res) => {

  const userId = _.get(req, "user.id", '');

  let petition;
  const month = _.get(req, "body.month","")
  const year = _.get(req, "body.year","")
  let startTime = Date.now();
  let endTime = Date.now();
  const unit = _.get(req, "body.unit", '');
  let category;

  let unitChoose;

  const checkParams = (next) => {
    if(!userId || !month || !year){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    const date = new Date(year, month - 1, 1);
    startTime = date.getTime();
    const endDate = new Date(year, month, 1);
    endTime = endDate.getTime();
    next()
  }

  const getUserInf = (next) => {
    User
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result){
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        if(result.units.length == 1){
          unitChoose = result.units[0]
        } else {
          if(!unit) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.STATISTIC.MISSING_UNIT
            })
          } else {
            unitChoose = unit
          }
        }
        next();
      })
  }

  const getCategory = (next) => {
    CategoryModel
      .find({
      })
      .select('name')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }
        category = results
        next(null)
      })
  }


  const statisticTotal = (next) => {
    let objSearch = {
      createdAt:{
        $gt: startTime,
        $lt: endTime
      },
      unitPath: unitChoose,
      category: {
        $ne: null
      }
    }


    Petition.aggregate([
      {
        $match: objSearch,
      },
      {
        $group: {
          _id: { category: "$category", status: "$status" },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: "$_id.category",
          total: { $sum: "$count" },
          statuses: {
            $push: { status: "$_id.status", count: "$count" }
          }
        }
      },
      {
        $sort: { total: -1 }
      },
      {
        $project: {
          _id: 0,
          category: "$_id",
          total: 1,
          statuses: 1
        }
      }
    ])
    .exec((err, results) => {
      if(err) {
        return next(err)
      }
      results.map((result) =>{
        category.map((cat) => {
          if(cat._id.toString() === result.category.toString()) {
            result.name = cat.name
          }
        })
        result.done = 0;
        result.notDone = 0;
        result.statuses.map((state) => {
          if([0,1,2].includes(state.status)) {
            result.notDone += state.count
          } else {
            result.done += state.count
          }
        })
        delete result.statuses
      })
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: results
      });
    })

  }


  async.waterfall([
    checkParams,
    getUserInf,
    getCategory,
    statisticTotal
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}