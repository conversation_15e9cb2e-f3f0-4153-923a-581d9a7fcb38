const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Unit = require('../../../../models/unit')
const tool = require('../../../../util/tool');


module.exports = (req, res) => {

  let name = req.body.name || '';
  let parent = req.body.parent || null
  let units = [];

  const listUnits = (next) => {

    let objSearch = {
      status: 1,
      parent
    }

    if(name && name.trim()) {
      const nameAlias = tool.change_alias(name.trim());

      // Tìm kiếm theo name<PERSON>lia<PERSON> (đã được chuẩn hóa)
      objSearch.nameAlias = { $regex: nameAlias };
    }

    Unit
      .find(objSearch)
      .select('-createdAt')
      .sort({order: 1})
      .populate('parent', 'name')
      .populate('parentPath', 'name')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        units = results
        next()
      })
  }

  const countUnit = (next) => {
    async.mapLimit(units, 3, (unit,done) => {
      Unit
        .count({
          parent: unit._id,
          status:1
        })
        .exec((err,count) => {
          if(err) {
            return done(err);
          }
          unit.countUnit = count;
          done()
        })
    },(err) => {
      if(err) {
        return next(err);
      }
      next();
    })
  }

  const countUser = (next) => {
    async.mapLimit(units, 3, (unit,done) => {
      User
        .count({
          units: unit._id,
          status:1
        })
        .exec((err,count) => {
          if(err) {
            return done(err);
          }
          unit.countUser = count;
          done()
        })
    },(err) => {
      if(err) {
        return next(err);
      }
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: units
      });
    })
  }

  async.waterfall([
    listUnits,
    countUnit,
    countUser
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}