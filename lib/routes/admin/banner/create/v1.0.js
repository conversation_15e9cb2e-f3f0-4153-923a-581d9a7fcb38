const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Banner = require('../../../../models/banner')


module.exports = (req, res) => {
  let banner = req.body.banner || '';
  let position = req.body.position;
  let description = req.body.description || '';
  let updatedData = {}

  const checkParams = (next) => {
    if (!banner || !position) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const createBanner = (next) => {
    let objCreate = {
      config: banner,
      platform: {
        android: {
          from: 1,
          to: 1000000000
        },
        ios: {
          from: 1,
          to: 1000000000
        }
      },
      position,
      description
    }
    Banner
      .create(objCreate, (err, result) => {
        updatedData = result;
        next(err);
      })
  }

 const writeLog = (next) => {
  next(null, {
   code: CONSTANTS.CODE.SUCCESS,
   data: updatedData._id
  })

  SystemLogModel
    .create({
      user: _.get(req,'user.id', ''),
      action: 'create_banner',
      description: 'Tạo banner mới',
      data: req.body,
      updatedData
    },() =>{})
 }

  async.waterfall([
    checkParams,
    createBanner,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}