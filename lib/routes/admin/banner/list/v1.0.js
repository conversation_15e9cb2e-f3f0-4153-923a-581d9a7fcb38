const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Banner = require('../../../../models/banner')
const escapeStringRegexp = require('escape-string-regexp');


module.exports = (req, res) => {

  let name = req.body.name || '';

  const listBanners = (next) => {

    let objSearch = {}

    Banner
      .find(objSearch, 'config position description status banner isAddable isHideable')
      .sort('position')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        results.forEach(element => {
          element.banner = element.config
          delete element.config
        });

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listBanners
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}