const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const Banner = require('../../../../models/banner')
const MESSAGES = require('../../../../message')


module.exports = (req, res) => {
  const idBanner = req.body._id || '';
  const status = req.body.status || 0;
  let data
  const checkParams = (next) => {
    if (!idBanner) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }


  const inactiveBanner = (next) => {

    Banner
      .findOneAndUpdate({
        _id: idBanner
      }, {
        status
      }, {
        new: true
      })
      .select('config position description status banner isAddable isHideable')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || new Error('Banner not found'));
        }
        result.banner = result.config
        delete result.config
        data = result
        next()
      })
  }

 const writeLog = (next) => {
  next(null, {
   code: CONSTANTS.CODE.SUCCESS,
   data
  })

  SystemLogModel
    .create({
      user: _.get(req,'user.id', ''),
      action: 'inactive_banner',
      description: status ? 'Mở lại banner' : 'Tắt banner',
      data: req.body,
      updatedData: data
    },() =>{})
 }

  async.waterfall([
    checkParams,
    inactiveBanner,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}