const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Banner = require('../../../../models/banner')


module.exports = (req, res) => {
  let idBanner = req.body._id || '';
  let banner = req.body.banner || '';
  let description = req.body.description || '';
  let updatedData = {}
  const checkParams = (next) => {
    if (!idBanner || !banner) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const modifyBanner = (next) => {

    let objModify = {
      config: banner,
      description
    }


    Banner
      .findOneAndUpdate({_id: idBanner}, objModify, {new: true}).lean()
      .exec((err, result) => {
        updatedData = result;
        next(err);
      })
  }

  const writeLog = (next) => {
    next(null, {
     code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'modify_banner',
        description: 'Chỉnh sửa banner',
        data: req.body,
        updatedData
      },() =>{})
   }


  async.waterfall([
    checkParams,
    modifyBanner,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}