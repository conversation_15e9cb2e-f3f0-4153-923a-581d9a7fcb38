const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const User = require('../../../../models/user');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const escapeStringRegexp = require('escape-string-regexp');

module.exports = (req, res) => {
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const sort = _.get(req, 'body.sort', 1);
  const textSearch = _.get(req, 'body.textSearch', '');
  let obj = {};
  let count = 0;
  let userAction = null;

  const checkParams = (next) => {
    if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      obj['$or'] = [
        {
          action: {
            $regex,
            $options: 'i',
          },
        },
        {
          description: {
            $regex,
            $options: 'i',
          },
        }
      ];
    }

    next();
  };

  const getUserInfoByTextSearch = (next) => {
    if (textSearch && textSearch.trim()) {
      const $regex = escapeStringRegexp(textSearch.trim());
      User.findOne({
        $or: [
          {
            name: {
              $regex,
              $options: 'gi',
            },
          },
          {
            username: {
              $regex,
              $options: 'gi',
            },
          },
          {
            email: {
              $regex,
              $options: 'gi',
            },
          },
          {
            phone: {
              $regex,
              $options: 'gi',
            },
          },
        ],
      })
        .lean()
        .exec((err, user) => {
          if (user) {
            obj['$or'].push({ user: user._id });
          }
          next();
        });
    } else {
      next();
    }
  };

  const countLog = (next) => {
    SystemLogModel.count(obj)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const list = (next) => {
    const skip = page * limit;
    const options = {
      limit,
      skip,
      sort: sort == 1 ? 'createdAt' : '-createdAt',
    };
    SystemLogModel.find(obj, '-data -updatedData', options)
      .populate('user', 'name')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, getUserInfoByTextSearch, countLog, list], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
