const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const rp = require('request-promise');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const redisConnection = require('../../../../connections/redis');

module.exports = (req, res) => {
  const { id } = req.body || '';
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }
    next(null);
  };

  const getLog = (next) => {
    SystemLogModel.findOne({
      _id: id,
    })
      .populate('user')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result,
        });
      });
  };

  async.waterfall([checkParams, getLog], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
