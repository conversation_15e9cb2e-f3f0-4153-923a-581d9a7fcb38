const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis');
const User = require('../../../../models/user');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Position = require('../../../../models/position');
const escapeStringRegexp = require('escape-string-regexp');

module.exports = (req, res) => {
  let name = req.body.name || '';
  let unit = req.body.unit || '';

  const listPositions = (next) => {
    let objSearch = {
      status: 1,
    };

    if (name && name.trim()) {
      const $regex = escapeStringRegexp(name.trim());
      objSearch.name = {
        $regex,
        $options: 'i',
      };
    }

    if (unit) {
      objSearch.unit = unit;
    }
    Position.find(objSearch)
      .sort({order: 1})
      .populate('unit', 'name')
      .populate('permissions', 'name code status')
      .populate({
        path: 'groupPermissions',
        select: 'name status permissions',
        populate: {
          path: 'permissions',
          select: 'name'
        }
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
        });
      });
  };

  async.waterfall([listPositions], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
