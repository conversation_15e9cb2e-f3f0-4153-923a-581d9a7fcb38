const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const UserNotify = require('../../../../models/userNotify')


module.exports = (req, res) => {

  let _id = req.body._id || '';
  const userId = _.get(req,'user.id','')

  const seen = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    let objSearch = {
      user: userId
    }

    if (_id) {
      objSearch._id = _id
    } else {
      objSearch.seen = 0
    }

    UserNotify
      .update(
        objSearch,{
          seen: 1
      },{new:true, multi:_id ? false : true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    seen
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}