const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const UserNotify = require('../../../../models/userNotify')
const escapeStringRegexp = require('escape-string-regexp');


module.exports = (req, res) => {

  const userId = _.get(req,'user.id','')
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const seen = _.get(req, 'body.seen');

  const listUserNotify = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    let objSearch = {
      user: userId
    }

    if (seen === 0 || seen === 1) {
      objSearch.seen = seen
    }

    let filterOptions = {
      limit,
      skip: limit * page,
      sort: '-createdAt',
    };

    UserNotify
      .find(objSearch, '', filterOptions)
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listUserNotify
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}