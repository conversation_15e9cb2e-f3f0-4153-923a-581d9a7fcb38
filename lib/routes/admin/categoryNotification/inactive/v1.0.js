const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const CategoryNotification = require('../../../../models/categoryNotification');
const MESSAGES = require('../../../../message');


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let updatedData = {};
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      })
    }

    next();
  }

  const inactiveCategoryNotification = (next) => {
    CategoryNotification
      .update({ _id: id }, { status: 0 })
      .exec((err, result) => {
        updatedData = result;
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req, 'user.id', ''),
        action: 'inactive_category_notify',
        description: 'X<PERSON>a danh mục thông báo',
        data: req.body,
        updatedData
      }, () => { })
  }

  async.waterfall([
    checkParams,
    inactiveCategoryNotification,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}