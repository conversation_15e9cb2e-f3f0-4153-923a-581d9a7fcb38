const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const CategoryNotification = require('../../../../models/categoryNotification')
const tool = require('../../../../util/tool')
const MESSAGES = require('../../../../message')

module.exports = (req, res) => {
  let name = req.body.name || '';
  let service = req.body.serviceId || '';

  const checkParams = (next) => {
    if (!name || !service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const createCategoryNotification = (next) => {

    let objCreate = {
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      service
    }

    CategoryNotification
      .create(objCreate, (err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = result;

        next()
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req, 'user.id', ''),
        action: 'create_category_notify',
        description: 'Tạo danh mục thông báo mới',
        data: req.body,
        updatedData
      }, () => { })
  }

  async.waterfall([
    checkParams,
    createCategoryNotification,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}