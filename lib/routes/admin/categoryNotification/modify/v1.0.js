const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const CategoryNotification = require('../../../../models/categoryNotification');
const tool = require('../../../../util/tool')


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let name = req.body.name || '';
  let service = req.body.serviceId || '';
  let updatedData = {};
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const modifyCategoryNotification = (next) => {
    let objModify = {
      name,
      nameAlias: tool.change_alias(name.trim()),
      service,
    }

    CategoryNotification
      .update({ _id: id }, objModify)
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = result

        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req, 'user.id', ''),
        action: 'modify_category_notify',
        description: 'Cập nhật danh mục thông báo',
        data: req.body,
        updatedData
      }, () => { })
  }

  async.waterfall([
    checkParams,
    modifyCategoryNotification,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
