const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { convertScreenToLink } = require('../../../../util/tool');

module.exports = (req, res) => {
  const service = req.body.service;
  const source = req.body.source;
  const update = {
    $set: { 'extras.source': source },
  };

  let seviceCategoryInf, serviceInfo;
  const checkParams = (next) => {
    if (!service || !source) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const updateServiceCategory = (next) => {
    ServiceCategoryModel.findById(service)
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        if (data) {
          let query = { _id: data._id };
          ServiceCategoryModel.findOneAndUpdate(query, update, { new: true })
            .lean()
            .exec((err, data) => {
              if (err) {
                return next(err);
              }
              SystemLogModel.create(
                {
                  user: _.get(req,'user.id', ''),
                  action: 'update_source_service_category',
                  description: 'Cập nhật dữ liệu danh mục dịch vụ',
                  data: req.body,
                  updatedData: data,
                },
                () => {}
              );
              next({
                code: CONSTANTS.CODE.SUCCESS,
                message: {
                  head: 'Thông báo',
                  body: 'Cập nhật thành công',
                },
                data,
              });
            });
        } else {
          next();
        }
      });
  };

  const updateService = (next) => {
    ServiceModel.findById(service)
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        if (data) {
          let query = { _id: data._id };
          ServiceModel.findOneAndUpdate(query, update, { new: true })
            .lean()
            .exec((err, data) => {
              if (err) {
                return next(err);
              }
              SystemLogModel.create(
                {
                  user: _.get(req,'user.id', ''),
                  action: 'update_source_service',
                  description: 'Cập nhật dữ liệu dịch vụ',
                  data: req.body,
                  updatedData: data,
                },
                () => {}
              );
              next({
                code: CONSTANTS.CODE.SUCCESS,
                message: {
                  head: 'Thông báo',
                  body: 'Cập nhật thành công',
                },
                data,
              });
            });
        } else {
          next();
        }
      });
  };

  const updateServiceChild = (next) => {
    ServiceChildrenModel.findById(service)
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        if (data) {
          let query = { _id: data._id };
          ServiceChildrenModel.findOneAndUpdate(query, update, { new: true })
            .lean()
            .exec((err, data) => {
              if (err) {
                return next(err);
              }
              SystemLogModel.create(
                {
                  user: _.get(req,'user.id', ''),
                  action: 'update_source_service_children',
                  description: 'Cập nhật dữ liệu dịch vụ con',
                  data: req.body,
                  updatedData: data,
                },
                () => {}
              );
              next({
                code: CONSTANTS.CODE.SUCCESS,
                message: {
                  head: 'Thông báo',
                  body: 'Cập nhật thành công',
                },
                data,
              });
            });
        } else {
          next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không đúng dữ liệu cập nhật',
            },
          });
        }
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật thành công',
      },
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'update_service_source',
        description: 'Cập nhật nguồn dịch vụ',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall(
    [checkParams, updateServiceCategory, updateService, updateServiceChild, writeLog],
    (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
      }

      err &&
        _.isError(err) &&
        (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
        });

      res.json(data || err);
    }
  );
};
