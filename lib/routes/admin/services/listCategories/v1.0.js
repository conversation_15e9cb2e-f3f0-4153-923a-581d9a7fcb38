const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = (req, res) => {
  const checkParams = (next) => {
    next();
  };

  const lisService = (next) => {
    ServiceCategoryModel.find({})
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data,
        });
      });
  };

  async.waterfall(
    [
      // checkParams,
      lisService,
    ],
    (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
      }

      err &&
        _.isError(err) &&
        (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
        });

      res.json(data || err);
    }
  );
};
