const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = (req, res) => {
  const { opens = [] } = req.body;

  const checkParams = (next) => {
    if (!opens || _.isEmpty(opens)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const updateService = (next) => {
    async.parallel(
      opens.map((item) => (done) => {
        const { id, open } = item;
        if (!id || !_.isNumber(open)) {
          return done(new Error('ID is required'));
        }
        ServiceModel.findOneAndUpdate({ _id: id }, { open }, { new: true })
          .lean()
          .exec((err, service) => {
            if (err) {
              return done(err);
            }
            if (!service) {
              return done(new Error('Service not found'));
            }
            done(err);
          });
      }),
      (err) => {
        next(err, {
          code: CONSTANTS.CODE.SUCCESS,
        });
      }
    );
  };

  async.waterfall([checkParams, updateService], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    console.log(err);

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
