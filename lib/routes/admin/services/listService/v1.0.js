const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { convertScreenToLink } = require('../../../../util/tool');

module.exports = (req, res) => {
  const userPermissions = _.get(req, 'user.permissions', []);
  const serviceCategory = req.body.serviceCategory;
  let seviceCategoryInf;
  const checkParams = (next) => {
    if (!serviceCategory) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getServiceCategory = (next) => {
    ServiceCategoryModel.findById(serviceCategory)
      .lean()
      .exec((err, data) => {
        if (err || !data) {
          return next(err);
        }
        seviceCategoryInf = data;
        _.set(seviceCategoryInf, 'link', convertScreenToLink(_.get(seviceCategoryInf, 'link')));
        next();
      });
  };

  const lisService = (next) => {
    const query = { category: serviceCategory };
    if (userPermissions.length) {
      query.permissions = { $in: userPermissions.map(permission => permission.code) }
    }

    ServiceModel.find(query)
      .sort('order')
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: data.map((service) => ({ ...service, link: convertScreenToLink(service.link) })),
          info: seviceCategoryInf,
        });
      });
  };

  async.waterfall([checkParams, getServiceCategory, lisService], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
