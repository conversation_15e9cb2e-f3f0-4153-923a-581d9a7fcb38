const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { convertScreenToLink } = require('../../../../util/tool');

module.exports = (req, res) => {
  const service = req.body.service;
  let serviceInfo;
  const checkParams = (next) => {
    if (!service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getService = (next) => {
    ServiceModel.findById(service)
      .populate('category')
      .lean()
      .exec((err, data) => {
        if (err || !data) {
          return next(err);
        }
        serviceInfo = data;
        _.set(serviceInfo, 'link', convertScreenToLink(_.get(serviceInfo, 'link')));
        next();
      });
  };

  const lisService = (next) => {
    ServiceChildrenModel.find({ service })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: data.map((service) => ({ ...service, link: convertScreenToLink(service.link) })),
          info: serviceInfo,
        });
      });
  };

  async.waterfall([checkParams, getService, lisService], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
