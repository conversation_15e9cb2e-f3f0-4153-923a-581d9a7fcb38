const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = (req, res) => {
  const { icons = [] } = req.body;

  const checkParams = (next) => {
    if (!icons || _.isEmpty(icons)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const updateService = (next) => {
    async.parallel(
      icons.map((item) => (done) => {
        const { id, icon, backgrounds } = item;
        if (!id || !icon) {
          return done(new Error('ID is required'));
        }
        ServiceChildrenModel.findOneAndUpdate({ _id: id }, { icon, backgrounds }, { new: true })
          .lean()
          .exec((err, service) => {
            if (err) {
              return done(err);
            }
            if (!service) {
              return done(new Error('Service not found'));
            }
            SystemLogModel.create(
              {
                user: _.get(req,'user.id', ''),
                action: 'update_icon_service_children',
                description: 'Cập nhật icon dịch vụ con',
                data: item,
                updatedData: service,
              },
              () => {}
            );
            done(err);
          });
      }),
      (err) => {
        next(err, {
          code: CONSTANTS.CODE.SUCCESS,
        });
      }
    );
  };

  async.waterfall([checkParams, updateService], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    console.log(err);

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
