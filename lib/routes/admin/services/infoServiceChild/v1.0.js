const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { convertScreenToLink } = require('../../../../util/tool');

module.exports = (req, res) => {
  const service = req.body.service;

  let seviceCategoryInf, serviceInfo;
  const checkParams = (next) => {
    if (!service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const getServiceCategory = (next) => {
    ServiceCategoryModel.findById(service)
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        seviceCategoryInf = data;
        _.set(seviceCategoryInf, 'link', convertScreenToLink(_.get(seviceCategoryInf, 'link')));
        next();
      });
  };


  const getService = (next) => {
    ServiceModel.findById(service)
      .populate('category')
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }
        serviceInfo = data;
        _.set(serviceInfo, 'link', convertScreenToLink(_.get(serviceInfo, 'link')));
        next();
      });
  };

  const getServiceChild = (next) => {
    ServiceChildrenModel.findById(service)
      .populate({
        path: 'service',
        select: 'link name title icon category',
        populate: {
          path: 'category',
        },
      })
      .lean()
      .exec((err, data) => {
        if (err) {
          return next(err);
        }

        _.set(data, 'link', convertScreenToLink(_.get(data, 'link')));
        _.set(data, 'service.link', convertScreenToLink(_.get(data, 'service.link')));
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: data || serviceInfo || seviceCategoryInf,
        });
      });
  };

  async.waterfall([checkParams, getServiceCategory, getService, getServiceChild], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
