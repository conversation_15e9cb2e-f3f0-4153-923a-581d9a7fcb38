const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = (req, res) => {
  const { orders = [] } = req.body;

  const checkParams = (next) => {
    if (!orders || _.isEmpty(orders)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next();
  };

  const updateService = (next) => {
    async.parallel(
      orders.map((item) => (done) => {
        const { id, order } = item;
        if (!id || !_.isNumber(order)) {
          return done(new Error('ID is required'));
        }
        ServiceModel.findOneAndUpdate({ _id: id }, { order }, { new: true })
          .lean()
          .exec((err, service) => {
            if (err) {
              return done(err);
            }
            if (!service) {
              return done(new Error('Service not found'));
            }
            done(err);
          });
      }),
      (err) => {
        next(err, {
          code: CONSTANTS.CODE.SUCCESS,
        });
      }
    );
  };

  async.waterfall([checkParams, updateService], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
