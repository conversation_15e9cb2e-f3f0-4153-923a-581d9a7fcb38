const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')
const Permission = require('../../../../models/permission')
const GroupPermission = require('../../../../models/groupPermission')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const idRole = req.body._id || '';
  const description = req.body.description || '';
  const permissions = req.body.permissions || [];
  const groupPermissions = req.body.groupPermissions || [];

  let roleInf;
  let updatedData = {};

  const checkParams = (next) => {
    if(!idRole){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!name){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ROLE.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkRoleExists = (next) => {

    Role
      .findById(idRole)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.ROLE.ROLE_NOT_EXISTS
          })
        }
        roleInf = result
        next()
      })

  }

  const checkNameNotExists = (next) => {

    Role
      .findOne({
        $or:[
          {
            name: name.trim()
          }
        ],
        _id: {
          $ne: idRole
        },
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.ROLE.ROLE_EXISTS
          })
        }
        next()
      })

  }


  const createRole = (next) => {

    let obj = {
      updatedAt: Date.now(),
      name : name.trim(),
      description: description.trim(),
      permissions,
      groupPermissions
    }

    Role.findOneAndUpdate({ _id: idRole }, obj, { new: true }).lean().exec((err, result) => {
      updatedData = result;
      next(err);
    });
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'update_role',
        description: 'Cập nhật vai trò',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    checkRoleExists,
    checkNameNotExists,
    createRole,
    writeLog
  ], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
