const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Role = require('../../../../models/role')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const description = req.body.description || '';
  const permissions = req.body.permissions || [];
  const groupPermissions = req.body.groupPermissions || [];

  let updatedData = {};

  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ROLE.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkRoleExists = (next) => {

    Role
      .find({
        $or:[
          {name}
        ]
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        if(results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.ROLE.ROLE_EXISTS
          })
        }
        next()
      })

  }

  const createRole = (next) => {

    let objCreate = {
      name,
      description,
      permissions,
      groupPermissions
    }


    Role
      .create(objCreate,(err, result) => {
        if(err) {
          return next(err);
        }
        updatedData = result;
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'create_role',
        description: 'Tạo vai trò',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    checkRoleExists,
    createRole,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}