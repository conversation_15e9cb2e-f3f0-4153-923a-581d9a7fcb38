const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const ConfigModel = require('../../../../models/config')


module.exports = (req, res) => {
  const getCurrentMaintain = (next) => {
    ConfigModel
      .findOne({ type: CONSTANTS.CONFIG_TYPE.CHATBOT_MAINTAIN })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result && result.config ? result.config : null
        })
      });
  }

  async.waterfall([
    getCurrentMaintain
  ], (err, data) => {
    console.log('haha:getcurrent', err, data)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
}
