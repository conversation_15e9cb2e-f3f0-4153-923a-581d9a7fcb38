const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const ConfigModel = require('../../../../models/config')


module.exports = (req, res) => {
  const maintain = _.get(req, 'body.maintain', 0);
  const message = _.get(req, 'body.message', 'Chức năng hiện tại đang được bảo trì để nâng cấp tri thức dữ liệu lớn. Chúng tôi sẽ thông báo ngay khi hoàn thành. Xin cảm ơn');

  const checkParams = (next) => {
    if (maintain && !message) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    next();
  }

  const updateConfig = (next) => {
    const updateData = {
      config: {
        maintain,
        message
      }
    }

    ConfigModel
      .update({ type: CONSTANTS.CONFIG_TYPE.CHATBOT_MAINTAIN }, updateData, (err, result) => {
        if (err) {
          return next(err)
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      });
  }

  async.waterfall([
    checkParams,
    updateConfig
  ], (err, data) => {
    console.log('haha:open maintain', err, data, maintain)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
