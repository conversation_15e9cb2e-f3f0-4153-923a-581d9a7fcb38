const _ = require('lodash');
const AgentModel = require('../../../models/agent');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const AgentManager = require('../../../job/agentManager');

module.exports = async (req, res) => {
  try {
    const {
      name,
      description,
      value,
      valueWithDoc,
      env
    } = req.body;

    // Validate required fields
    if (!name || !env || !value || !valueWithDoc) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Validate environment value
    if (!['dev', 'production'].includes(env)) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Check if agent already exists with same name, value or valueWithDoc
    const existingAgent = await AgentModel.findOne({
      $or: [
        { name },
        { value: value || 0 },
        { valueWithDoc: valueWithDoc || 0 }
      ]
    });

    if (existingAgent) {
      return res.json({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thêm mới thất bại',
          body: 'Agent đã tồn tại'
        }
      });
    }

    // Create new agent
    const newAgent = await AgentModel.create({
      name,
      description,
      value: value || 0,
      valueWithDoc: valueWithDoc || 0,
      env,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    AgentManager.syncConfig();
    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.SYSTEM.SUCCESS,
      data: newAgent
    });

  } catch (err) {
    console.error('Create agent error:', err);
    res.json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};