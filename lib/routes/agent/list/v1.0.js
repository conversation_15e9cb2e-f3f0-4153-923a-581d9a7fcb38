const _ = require('lodash');
const AgentModel = require('../../../models/agent');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

module.exports = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      textSearch = '',
      env,
      sortBy = 'createdAt',
      sortOrder = -1
    } = req.body;

    // Build query conditions
    const conditions = {};
    
    // textSearch by name or description
    if (textSearch) {
      conditions.$or = [
        { name: { $regex: textSearch, $options: 'i' } },
        { description: { $regex: textSearch, $options: 'i' } }
      ];
    }

    // Filter by environment
    if (env && ['dev', 'production'].includes(env)) {
      conditions.env = env;
    }

    // Calculate skip value for pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build sort object
    const sort = {
      [sortBy]: parseInt(sortOrder)
    };
    // Execute queries in parallel
    const [agents, total] = await Promise.all([
      AgentModel.find(conditions)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      AgentModel.countDocuments(conditions)
    ]);

    // Calculate total pages
    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: agents,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        limit: parseInt(limit)
      }
    });

  } catch (err) {
    console.error('List agents error:', err);
    res.json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};