const _ = require('lodash');
const AgentModel = require('../../../models/agent');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const AgentManager = require('../../../job/agentManager');

module.exports = async (req, res) => {
  try {
    const  id  = req.body._id;
    const {
      name,
      description,
      value,
      valueWithDoc,
      env
    } = req.body;

    // Validate id
    if (!id || !name  || !value || !valueWithDoc) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Check if agent exists
    const existingAgent = await AgentModel.findById(id);
    if (!existingAgent) {
      return res.json({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.SYSTEM.ERROR
      });
    }

    // Check if name/value/valueWithDoc already exists for other agents
    const duplicateAgent = await AgentModel.findOne({
      _id: { $ne: id },
      $or: [
        { name },
        { value: value || 0 },
        { valueWithDoc: valueWithDoc || 0 }
      ]
    });

    if (duplicateAgent) {
      return res.json({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Cập nhật thất bại',
          body: 'Agent đã tồn tại'
        }
      });
    }

    // Validate environment value if provided
    if (env && !['dev', 'production'].includes(env)) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Build update object with only provided fields
    const updateData = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (value !== undefined) updateData.value = value;
    if (valueWithDoc !== undefined) updateData.valueWithDoc = valueWithDoc;
    if (env) updateData.env = env;
    updateData.updatedAt = Date.now();

    // Update agent
    const updatedAgent = await AgentModel.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    );

    AgentManager.syncConfig();

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.SYSTEM.SUCCESS,
      data: updatedAgent
    });

  } catch (err) {
    console.error('Update agent error:', err);
    res.json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};