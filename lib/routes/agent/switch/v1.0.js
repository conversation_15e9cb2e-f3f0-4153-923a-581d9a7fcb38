const _ = require('lodash')
const async = require('async')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const bcrypt = require('bcryptjs')

const request = require('request')
const MemberModel = require('../../../models/member')
const AgentModel = require('../../../models/agent')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const MailUtil = require('../../../util/mail');
const AgentManager = require('../../../job/agentManager')

module.exports = (req, res) => {
  const {  name, env } = req.body;

  const checkParams = (next) => {
    if (!name || !env) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(['dev', 'production'].indexOf(env) === -1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const updateAgent = (next) => {
    AgentManager
      .setEnv(name, env, (err, data) => {
        if (err) {
          return next(err);
        }

        if (!data) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGES.SYSTEM.SUCCESS
          })
        }

        return next(null, data);
      });
  }

  async.waterfall([
    checkParams,
    updateAgent
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
}