const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Legislature = require('../../../../models/legislature')


module.exports = (req, res) => {
  const legislatureId = _.get(req, 'body.id', '');

  const checkParams = (next) => {
    if (!legislatureId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.ERROR
      })
    }

    next();
  }

  const getLegislature = (next) => {
    Legislature
      .findOne({ _id: legislatureId }, '-nameAlias -active -updatedAt')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (result.location && result.location.coordinates) {
          result.location = {
            lat: result.location.coordinates[1],
            lng: result.location.coordinates[0]
          }

          delete result.location.coordinates;
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    checkParams,
    getLegislature
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
