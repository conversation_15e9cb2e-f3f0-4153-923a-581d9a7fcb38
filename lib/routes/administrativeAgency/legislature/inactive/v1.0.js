const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Legislature = require('../../../../models/legislature')


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let updatedData = {};

  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const inactiveLegislature = (next) => {
    Legislature
      .findOneAndUpdate({ _id: id }, {active: 0}, {new: true})
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        updatedData = result;
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'inactive_legislature',
        description: 'Vô hiệu hóa cơ quan lập pháp',
        data: req.body,
        updatedData
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    inactiveLegislature,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
