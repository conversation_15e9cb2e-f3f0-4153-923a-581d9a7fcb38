const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Legislature = require('../../../../models/legislature')
const tool = require('../../../../util/tool')

module.exports = (req, res) => {
  const location = _.get(req, 'body.location', {});
  const distance = _.get(req, 'body.distance', 5000);
  const limit = _.get(req, 'body.limit', 10);
  const skip = _.get(req, 'body.skip', 0);
  const district = _.get(req, 'body.district', null);
  const ward = _.get(req, 'body.ward', null);
  const name = _.get(req, 'body.name', '');

  const listLegislature = (next) => {
    const query = {
      active: 1
    }
    if (district) {
      query.district = district
    }
    if (ward) {
      query.ward = ward
    }
    if (name.trim()) {
      query.nameAlias = new RegExp(tool.change_alias(name.trim()), 'gi')
    }

    let func = Legislature.find(query, '-nameAlias -active -updatedAt', { limit, skip })

    if (location && location.lng && location.lat) {
      func = func.near('location', {
        center: {
          coordinates: [location.lng, location.lat],
          type: 'Point'
        },
        maxDistance: distance
      })
    }

    func
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        results.map(result => {
          result.location = {
            lat: result.location.coordinates[1],
            lng: result.location.coordinates[0]
          }

          delete result.location.coordinates
        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listLegislature
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
