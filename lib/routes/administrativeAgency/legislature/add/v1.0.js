const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Legislature = require('../../../../models/legislature')
const tool = require('../../../../util/tool')


module.exports = (req, res) => {
  const name = _.get(req, 'body.name', '');
  const image = _.get(req, 'body.image', '');
  const phone = _.get(req, 'body.phone', '');
  const generalInfo = _.get(req, 'body.generalInfo', '');
  const address = _.get(req, 'body.address', '');
  const location = _.get(req, 'body.location', {});
  const district = _.get(req, 'body.district', '');
  const ward = _.get(req, 'body.ward', '');

  let updatedData = {};

  const checkParams = (next) => {
    if (!name || !image || !phone || !generalInfo || !address || !location || !location.lat || !location.lng) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const addLegislature = (next) => {
    let objCreate = {
      name,
      nameAlias: tool.change_alias(name),
      image,
      phone,
      generalInfo,
      address,
      district,
      ward,
      location: {
        coordinates: [location.lng, location.lat],
        type: 'Point'
      }
    }

    Legislature
      .create(objCreate, (err, result) => {
        if (err) {
          return next(err);
        }
        updatedData = result;
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'add_legislature',
        description: 'Thêm cơ quan lập pháp',
        data: req.body,
        updatedData
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    addLegislature,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
