const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const list = (next) => {
    OrganizationalStructureModel.find({})
      .lean()
      .exec((err, results) => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
        });
      });
  };

  async.waterfall([list], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
