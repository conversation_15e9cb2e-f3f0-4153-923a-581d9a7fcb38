const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const config = require('config')
const util = require('util')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const LawEnforcementAgency = require('../../../../models/lawEnforcementAgency')
const tool = require('../../../../util/tool')

module.exports = (req, res) => {
  const location = _.get(req, 'body.location', {});
  const distance = _.get(req, 'body.distance', 5000);
  const limit = _.get(req, 'body.limit', 10);
  const skip = _.get(req, 'body.skip', 0);
  const district = _.get(req, 'body.district', null);
  const ward = _.get(req, 'body.ward', null);
  const structure = _.get(req, 'body.structure', null);
  const governmentLevel = _.get(req, 'body.level', null);
  const name = _.get(req, 'body.name', '');

  const listLawEnforcementAgency = (next) => {
    const query = {
      active: 1
    }
    if (district) {
      query.district = district
    }
    if (ward) {
      query.ward = ward
    }
    if (structure) {
      query.structure = structure
    }
    if (governmentLevel) {
      query.governmentLevel = governmentLevel
    }
    if (name.trim()) {
      query.nameAlias = new RegExp(tool.change_alias(name.trim()), 'gi')
    }

    let func = LawEnforcementAgency.find(query, '-nameAlias -active -updatedAt', { limit, skip })

    if (location && location.lng && location.lat) {
      func = func.near('location', {
        center: {
          coordinates: [location.lng, location.lat],
          type: 'Point'
        },
        maxDistance: distance
      })
    }

    func
      .sort({
        'governmentLevel': 1,
        'structure': 1,
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        results.map(result => {
          result.location = {
            lat: result.location.coordinates[1],
            lng: result.location.coordinates[0]
          }

          delete result.location.coordinates
        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listLawEnforcementAgency
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
