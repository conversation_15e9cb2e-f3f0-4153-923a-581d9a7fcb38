const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const LawEnforcementAgency = require('../../../../models/lawEnforcementAgency')


module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  let updatedData = {};
  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const inactiveLawEnforcementAgency = (next) => {
    LawEnforcementAgency
      .findOneAndUpdate({ _id: id }, {active: 0},{new: true})
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        updatedData = result;
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'inactive_law_enforcement_agency',
        description: 'Vô hiệu hóa cơ quan thực thi pháp luật',
        data: req.body,
        updatedData
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    inactiveLawEnforcementAgency,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
