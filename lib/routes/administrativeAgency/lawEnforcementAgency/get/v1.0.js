const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const LawEnforcementAgency = require('../../../../models/lawEnforcementAgency')


module.exports = (req, res) => {
  const lawEnforcementAgencyId = _.get(req, 'body.id', '');

  const checkParams = (next) => {
    if (!lawEnforcementAgencyId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.ERROR
      })
    }

    next();
  }

  const getLawEnforcementAgency = (next) => {
    LawEnforcementAgency
      .findOne({ _id: lawEnforcementAgencyId }, '-nameAlias -active -updatedAt')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (result.location && result.location.coordinates) {
          result.location = {
            lat: result.location.coordinates[1],
            lng: result.location.coordinates[0]
          }

          delete result.location.coordinates;
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    checkParams,
    getLawEnforcementAgency
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
