const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/traditionalVillage');

module.exports = (req, res) => {
  const _id = _.get(req, 'body._id', '');
  const active = _.get(req, 'body.active', 0);
  let updatedData = {};

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    next();
  };

  const inactive = (next) => {
    Model.findOneAndUpdate({ _id }, { active }, { new: true })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        updatedData = result;
        next(null);
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'inactive_trade_village',
        description: 'Vô hiệu hóa làng nghề truyền thống',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([checkParams, inactive, writeLog], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
