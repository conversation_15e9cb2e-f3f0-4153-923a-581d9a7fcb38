const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/traditionalVillage');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const name = _.get(req, 'body.name', '');
  const image = _.get(req, 'body.image', '');
  const certificate = _.get(req, 'body.certificate', '');
  const products = _.get(req, 'body.products', '');
  const phone = _.get(req, 'body.phone', '');
  const generalInfo = _.get(req, 'body.generalInfo', '');
  const address = _.get(req, 'body.address', '');
  const location = _.get(req, 'body.location', {});
  const district = _.get(req, 'body.district', '');
  const ward = _.get(req, 'body.ward', '');
  let phones = [];
  let updatedData = {};
  if (typeof phone === 'string') {
    phones = phone.split(/\D+/).filter(Boolean);
  } else {
    phones = phone;
  }
  const checkParams = (next) => {
    if (!name || !image || !address || !district || !location || !location.lat || !location.lng) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    next();
  };

  const add = (next) => {
    let objCreate = {
      name,
      nameAlias: tool.change_alias(name),
      image,
      certificate,
      products,
      generalInfo,
      address,
      phones,
      district,
      ward,
      location: {
        coordinates: [location.lng, location.lat],
        type: 'Point',
      },
    };

    Model.create(objCreate, (err, result) => {
      if (err) {
        return next(err);
      }
      updatedData = result;
      next(null);
    });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'add_traditional_village',
        description: 'Thêm làng nghề truyền thống',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([checkParams, add, writeLog], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
