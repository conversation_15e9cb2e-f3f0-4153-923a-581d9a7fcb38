const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/traditionalVillage');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const sort = _.get(req, 'body.sort', 0);
  const limit = _.get(req, 'body.limit', 10);
  const page = _.get(req, 'body.page', 0);
  const district = _.get(req, 'body.district', null);
  const ward = _.get(req, 'body.ward', null);
  const active = _.get(req, 'body.active', '');
  const textSearch = _.get(req, 'body.textSearch', '');
  const isFilter = _.get(req, 'body.isFilter', 0);
  let query = {
    active: 1,
  };
  let count = 0;

  const checkParams = (next) => {
    if (textSearch.trim() && textSearch.length > 0) {
      query = {
        $or: [
          // { name: new RegExp(textSearch, 'gi') },
          { nameAlias: new RegExp(tool.change_alias(textSearch), 'gi') },
          { address: new RegExp(textSearch, 'gi') },
          { phones: new RegExp(textSearch, 'gi') },
        ],
      };
    }

    if (isFilter) {
      if (district) {
        query.district = district;
      }

      if (ward) {
        query.ward = ward;
      }

      if (_.isNumber(active)) {
        query.active = active;
      }
    }
    next();
  };

  const countQuery = (next) => {
    Model.count(query)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const list = (next) => {
    let filterOptions = {
      limit,
      skip: limit * page,
      sort: sort === 1 ? 'createdAt' : '-createdAt',
    };

    Model.find(query, '-nameAlias', filterOptions)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        results.map((result) => {
          if (result && result.phones && result.phones.length) {
            result.phone = result.phones.join(', ');
            delete result.phones;
          }
          result.location = {
            lat: _.get(result, 'location.coordinates[1]', 0),
            lng: _.get(result, 'location.coordinates[0]', 0),
          };

          delete result.location.coordinates;
        });

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results,
          count,
        });
      });
  };

  async.waterfall([checkParams, countQuery, list], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
