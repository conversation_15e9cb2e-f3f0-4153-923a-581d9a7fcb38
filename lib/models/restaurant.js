const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Restaurant = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  image: {
    type: String
  },
  phone: {
    type: String
  },
  generalInfo: {
    type: String
  },
  address: {
    type: String
  },
  district: {
    type: String
  },
  ward: {
    type: String
  },
  districtCode: {
    type: String
  },
  region: {
    type: String
  },
  wardCode: {
    type: String
  },
  menu: {
    type: Schema.Types.Mixed
  },
  star: {
    type: Number,
    default: 5
  },
  active: {
    type: Number,
    default: 1
  },
  priority: {
    type: Number,
  },
  order: {
    type: Number,
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('Restaurant', Restaurant);