const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const CoachStation = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  image: {
    type: String
  },
  phones: {
    type: Array
  },
  routes: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  order: {
    type: Number,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('CoachStation', CoachStation);