const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ChatSchema = new mongoose.Schema(
  {
    member: {
      type: String,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    location: {
      type: String,
    },
    error: {
      type: String,
    },
    type: {
      type: String,
      default: 'public',
    },
    answeredAt: {
      type: Number,
      default: Date.now,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },

    group: {
      type: Schema.Types.ObjectId,
      ref: 'ChatTesingGroup',
    },
    isValid: {
      type: Boolean,
      default: false,
    },
    testedAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('ChatTesingResult', ChatSchema);
