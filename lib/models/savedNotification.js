const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo')

var SavedNotification = new mongoose.Schema({
  title: { type: String },
  messageTitle: { type: String },
  message: { type: String },
  service: { type: mongoose.Schema.Types.ObjectId, ref: 'Service' },
  category: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CategoryNotification' }],
  data: { type: mongoose.Schema.Types.Mixed },
  seen: { type: Array, default: [] },
  status: { type: Number, default: 1 },
  pushNotification: { type: Boolean },
  updatedAt: { type: Number, default: Date.now },
  createdAt: { type: Number, default: Date.now }
}, { versionKey: false });

module.exports = mongoConnections('master').model('SavedNotification', SavedNotification);
