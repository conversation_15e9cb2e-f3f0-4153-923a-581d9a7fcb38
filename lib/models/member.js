const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")
const generate = require('nanoid/generate')

var MemberSchema = new mongoose.Schema(
  {
    phone: { type: String },
    email: { type: String },
    name: { type: String },
    avatar: { type: String },
    idCard: { type: String },
    address: { type: String },
    birthday: { type: String },
    password: { type: String },
    code: { type: String },
    gender: {
      type: String,
    },
    blockUtil: {
      type: "Number",
      default: 0,
    },
    region: { type: String },
    createdAt: { type: Number, default: Date.now },
    updatedAt: { type: Number, default: Date.now },
    idNumber: {
      type: String
    },
    idCardInf: {
      type: Schema.Types.Mixed
    }
  },
  { id: false, versionKey: false },
)

MemberSchema.pre('save', function (next) {
  let model = this
  attempToGenerate(model, next)
})

const attempToGenerate = (model, callback) => {
  let newCode = generate('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 5)
  model.constructor.findOne({
    'code': newCode
  }).then((course) => {
    if (course) {
      attempToGenerate(model, callback)
    } else {
      model.code = newCode
      callback();
    }
  }, (err) => {
    callback(err)
  })
}

module.exports = mongoConnections("master").model("Member", MemberSchema)
