const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

var TrackingAction = new mongoose.Schema({
  member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
  type: { type: Number }, // 0 means login, 1 means logout
  otherInf: { type: mongoose.Schema.Types.Mixed },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { versionKey: false });

module.exports = mongoConnections('master').model('TrackingAction', TrackingAction);
