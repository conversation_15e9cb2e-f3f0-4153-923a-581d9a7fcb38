const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ConversationDocumentSummarySchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    id: {
      type: Number,
    },
    is_dev: {type: Boolean},
    agents_impl_id: {
      type: Number,
    },
    isFile: {
      type: Boolean
    },
    documents: [{
      type: Schema.Types.ObjectId,
      ref: 'Document',
    }],
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    inactive: {
      type: Boolean
    },
    hasMessage: {
      type: Boolean,
    },
    conversationVideoSummary: {
      type: Schema.Types.ObjectId,
      ref: 'ConversationVideoSummary'
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

ConversationDocumentSummarySchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationDocumentSummarySchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationDocumentSummarySchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('ConversationDocumentSummary', ConversationDocumentSummarySchema);
