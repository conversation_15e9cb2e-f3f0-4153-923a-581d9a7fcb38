const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const LawEnforcementAgency = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  image: {
    type: String
  },
  phone: {
    type: String
  },
  generalInfo: {
    type: String
  },
  address: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  location: {
    type: Schema.Types.Mixed,
  },
  district: {
    type: Schema.Types.ObjectId,
    ref: 'District'
  },
  ward: {
    type: Schema.Types.ObjectId,
    ref: 'Ward'
  },
  districtCode: {
    type: String
  },
  region: {
    type: String
  },
  wardCode: {
    type: String
  },
  structure: {
    type: Schema.Types.ObjectId,
    ref: 'OrganizationalStructure'
  },
  governmentLevel: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('LawEnforcementAgency', LawEnforcementAgency);