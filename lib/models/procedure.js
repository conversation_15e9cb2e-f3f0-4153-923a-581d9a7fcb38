const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const ProcedureSchema = new mongoose.Schema(
  {
    id: {
      type: String,
    },
    procedureCode: {
      type: String,
    },
    name: {
      type: String
    },
    nameAlias: {
      type: String
    },
    groupId: {
      type: mongoose.Schema.Types.ObjectId
    },
    eventId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'DichvucongEvent'
    },
    active: {
      type: Number,
      default: 1
    },
    publishedAgency: {
      type: String
    },
    fieldName: {
      type: String
    },
    implementationAgency: {
      type: String
    },
    amount: {
      type: Number
    },
    cachThucThucHien: {
      type: mongoose.Schema.Types.Mixed
    },
    cachThucThucHienPlainText: {
      type: String
    },
    thanhPhanHoSo: {
      type: mongoose.Schema.Types.Mixed
    },
    thanhPhanHoSoPlainText: {
      type: String
    },
    trinhTuThucHien: {
      type: mongoose.Schema.Types.Mixed
    },
    trinhTuThucHienPlainText: {
      type: String
    },
    coQuanThucHien: {
      type: String
    },
    yeuCauDieuKien: {
      type: mongoose.Schema.Types.Mixed
    },
    yeuCauDieuKienPlainText: {
      type: String
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Procedure", ProcedureSchema)
