const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ChatWithDocumentSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    type: {
      type: String,
    },
    error: {
      type: String,
    },
    urlChatbot: {
      type: String,
    },
    answeredAt: {
      type: Number,
      default: Date.now,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('ChatWithDocument', ChatWithDocumentSchema);
