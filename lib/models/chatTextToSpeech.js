const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatTextToSpeechSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    content: {
      type: String,
    },
    voiceCode: {
      type: String
    },
    responseJson: {
      type: Schema.Types.Mixed,
    },
    audio: {
      type: String,
    },
    speed: {
      type: String,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("ChatTextToSpeech", ChatTextToSpeechSchema);
