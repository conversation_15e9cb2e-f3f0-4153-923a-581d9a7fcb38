const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const UserSchema = require('./user')
const generate = require('nanoid/generate')

var Document = new mongoose.Schema({
  name: {
    type: String
  },
  nameAlias: {
    type: String
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  type: {
    type: String
  },
  size: {
    type: Number
  },
  summary: {
    type: String
  },
  url: {
    type: String
  },
  urlPreview: {
    type: String
  },
  urlDownload: {
    type: String
  },
  title: {
    type: String
  },
  summary: {
    type: String
  },
  docCategory: {
    type: String
  },
  subtitle: {
    type: String
  },
  highlight:{
    type: String
  },
  aiName:{
    type: String
  },
  code: { type: String },
  status: {
    type: String,
    default: 'PROCESSING'
  },
  id: {
    type: Number
  },
  isShared: { 
    type: Boolean,
    default: false
  },
  allowedUnits: [{ 
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  }],
  updatedAt: {
    type: Number, default: Date.now
  },
  createdAt: { type: Number, default: Date.now }
}, { id: false, versionKey: false })

Document.pre('save', function (next) {
  let model = this
  attempToGenerate(model, next)
})

const attempToGenerate = (model, callback) => {
  let newCode = generate('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 5)
  if(model.code) {
    newCode = model.code
  }
  model.constructor.findOne({
    'code': newCode
  }).then((course) => {
    if (course) {
      attempToGenerate(model, callback)
    } else {
      model.code = newCode
      callback();
    }
  }, (err) => {
    callback(err)
  })
}

module.exports = mongoConnections('master').model('Document', Document);
