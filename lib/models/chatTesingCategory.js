const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ChatSchema = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    region: {
      type: String,
    },
    status: {
      type: Number,
      default: 1,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('ChatTesingCategory', ChatSchema);
