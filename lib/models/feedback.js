const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var FeedBackSchema = new mongoose.Schema({
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  image: {
    type: mongoose.Schema.Types.Mixed
  },
  message: {
    type: 'String'
  },
  type: {
    type: Number
  },
  note: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  status: {
    type: Number,
    default: 0
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('FeedBack', FeedBackSchema);
