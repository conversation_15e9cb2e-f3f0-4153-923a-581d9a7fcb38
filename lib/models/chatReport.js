const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatReportSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    field: {
      type: String,
    },
    topic: {
      type: String,
    },
    timeline: {
      type: String,
    },
    specificTimeline: {
      type: Number,
    },
    year: {
      type: Number,
    },
    compare: {
      type: Boolean,
    },
    yearCompare: {
      type: Number,
    },
    specificTimelineCompare: {
      type: Number,
    },
    exportPdf: {
      type: Boolean,
    },
    createCharts: {
      type: Boolean,
    },
    urlDownload: {
      type: String
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chatReport", ChatReportSchema);
