const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const AgentSchema = new mongoose.Schema(
 {
  name: {
   type: String,
  },
  description: {
    type: String,
  },
  value: {
    type: Number,
  },
  valueWithDoc: {
    type: Number,
  },
  env: {
    type: String,
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("agent", AgentSchema);
