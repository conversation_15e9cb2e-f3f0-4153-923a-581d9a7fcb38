const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Ward = new mongoose.Schema({
  district: {
    type: Schema.Types.ObjectId,
    ref: "District"
  },
  province: {
    type: Schema.Types.ObjectId
  },
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  code: {
    type: String
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  isAuthen: {
    type: Number,
    default: 0
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('Ward', Ward);