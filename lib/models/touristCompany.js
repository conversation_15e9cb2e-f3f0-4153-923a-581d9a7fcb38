const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const TouristCompany= new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  image: {type: String},
  address: {
    type: String
  },
  district: {
    type: Schema.Types.ObjectId,
    ref: 'District'
  },
  ward: {
    type: Schema.Types.ObjectId,
    ref: 'Ward'
  },
  wardCode: {
    type: String
  },
  districtCode: {
    type: String
  },
  email: {
    type: String
  },
  phone: {
    type: Array
  },
  active: {
    type: Number,
    default: 1
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('TouristCompany', TouristCompany);