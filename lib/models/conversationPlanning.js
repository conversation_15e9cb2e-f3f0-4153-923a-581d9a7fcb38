const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ConversationPlanningSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    id: {
      type: Number,
    },
    is_dev: {type: <PERSON>ole<PERSON>},
    agents_impl_id: {
      type: Number,
    },
    isFile: {
      type: Boolean
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    inactive: {
      type: Boolean,
    },
    hasMessage: {
      type: Boolean,
    },
    documents: [{
      type: Schema.Types.ObjectId,
      ref: 'Document',
    }],
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

ConversationPlanningSchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationPlanningSchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationPlanningSchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('ConversationPlanning', ConversationPlanningSchema);
