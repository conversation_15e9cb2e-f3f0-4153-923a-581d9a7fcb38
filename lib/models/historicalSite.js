const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const HistoricalSite = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  image: {
    type: String
  },
  rank: {
    type: String
  },
  type: {
    type: String
  },
  certificate: {
    type: String
  },
  generalInfo: {
    type: String
  },
  district: {
    type: Schema.Types.ObjectId,
    ref: 'District'
  },
  ward: {
    type: Schema.Types.ObjectId,
    ref: 'Ward'
  },
  address: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  order: {
    type: Number,
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('HistoricalSite', HistoricalSite);