const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatPlanningSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    role: {
      type: String,
    },
    goal: {
      type: String,
    },
    urlDownload: {
      type: String
    },
    type:{
      type: String
    },
    startDate:{
      type: String
    },
    endDate:{
      type: String
    },
    page: {
      type: Number
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("ChatPlanning", ChatPlanningSchema);
