const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ConversationReportSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    id: {
      type: Number,
    },
    is_dev: {type: <PERSON>ole<PERSON>},
    agents_impl_id: {
      type: Number,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    inactive: {
      type: Boolean,
    },
    hasMessage: {
      type: Boolean,
    },
    documents: [{
      type: Schema.Types.ObjectId,
      ref: 'Document',
    }],
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

ConversationReportSchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationReportSchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationReportSchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('ConversationReport', ConversationReportSchema);
