const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatSchema = new mongoose.Schema(
 {
  member: {
   type: String,
  },
  conversation: {
    type: Schema.Types.ObjectId,
  },
  question: {
    type: String,
  },
  answer: {
    type: String,
  },
  personalInfo: {
    type: Schema.Types.Mixed,
  },
  error: {
    type: String,
  },
  urlChatbot: {
    type: String,
  },
  type: {
    type: String,
    default: 'public'
  },
  answeredAt: {
    type: Number,
    default: Date.now
  },
  rate: {
    type: Number
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chat", ChatSchema);
