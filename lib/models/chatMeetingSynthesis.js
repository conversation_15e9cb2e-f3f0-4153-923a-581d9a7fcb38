const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatMeetingSynthesisSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    page: {
      type: Number,
    },
    error: {
      type: String,
    },
    content: {
      type: String,
    },
    urlDownload: {
      type: String,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chatMeetingSynthesis", ChatMeetingSynthesisSchema);
