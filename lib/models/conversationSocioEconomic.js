const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ConversationSocioEconomicSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    id: {
      type: Number,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    is_dev: {type: Boolean},
    agents_impl_id: {
      type: Number,
    },
    inactive: {
      type: Boolean
    },
    hasMessage: {
      type: Boolean,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

ConversationSocioEconomicSchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationSocioEconomicSchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationSocioEconomicSchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('ConversationSocioEconomic', ConversationSocioEconomicSchema);
