const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const moment = require('moment');

const PetitionLogSchema = new mongoose.Schema(
 {
  petition: { type: Schema.Types.ObjectId, ref: "Petition" },
  member: { type: Schema.Types.ObjectId, ref: "Member" },
  user: { type: Schema.Types.ObjectId, ref: "User" },
  createdAt: { type: Number, default: Date.now },
  action: {type: String},
  description: {type: String},
  oldStatus: {
    type: Number
  },
  newStatus: {
    type: Number
  },
  oldPetitionInf: {type: Schema.Types.Mixed},
  newPetitionInf: {type: Schema.Types.Mixed}
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("PetitionLog", PetitionLogSchema)
