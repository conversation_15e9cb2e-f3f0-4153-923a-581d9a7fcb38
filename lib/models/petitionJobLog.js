const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const moment = require('moment');

const PetitionJobLogSchema = new mongoose.Schema(
  {
   petition: { type: Schema.Types.ObjectId, ref: "Petition" },
   member: { type: Schema.Types.ObjectId, ref: "Member" },
   user: { type: Schema.Types.ObjectId, ref: "User" },
   createdAt: { type: Number, default: Date.now },
   action: {type: String},
   description: {type: String},
   content: {type: String},
   oldStatus: {
     type: Number
   },
   newStatus: {
     type: Number
   },
   fromUnit: {
    type: Schema.Types.ObjectId, ref: "Unit"
   },
   toUnit: {
    type: Schema.Types.ObjectId, ref: "Unit"
   },
   fromPerson: {
    type: Schema.Types.ObjectId, ref: "User"
   },
   toPerson: {
    type: Schema.Types.ObjectId, ref: "User"
   },
   oldPetitionInf: {type: Schema.Types.Mixed},
   newPetitionInf: {type: Schema.Types.Mixed},
   expiredTime: {type: Number}
  },
  { id: false, versionKey: false },
 )

module.exports = mongoConnections("master").model("PetitionJobLog", PetitionJobLogSchema)
