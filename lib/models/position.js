const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const PositionSchema = new mongoose.Schema(
 {
  name: {
   type: String,
  },
  unit: {
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  },
  role: {
    type: String
  },
  groupPermissions: [{
    type: Schema.Types.ObjectId,
    ref:'GroupPermission'
  }],
  permissions: [{
    type: Schema.Types.ObjectId,
    ref:'Permission'
  }],
  status: {
    type: Number,
    default: 1
  },
  order: {
    type: Number,
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Position", PositionSchema)
