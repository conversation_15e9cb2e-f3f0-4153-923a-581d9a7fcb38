const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash');
const mongoConnections = require('../connections/mongo');
const async = require('async');
const ms = require('ms');
// import { environment } from '../config';

const ServiceChildren = new mongoose.Schema({
  icon: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  screen: {
    type: String,
  },
  open: {
    type: Number,
    default: 1,
  },
  order: {
    type: Number,
  },
  backgrounds: {
    type: [String],
  },
  extras: {
    type: mongoose.Schema.Types.Mixed,
  },
  config: {
    type: mongoose.Schema.Types.Mixed,
  },
  region: {
    type: mongoose.Schema.Types.Mixed,
  },
  platform: {
    type: mongoose.Schema.Types.Mixed,
  },
  platformDriver: {
    type: mongoose.Schema.Types.Mixed,
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
  },
  createdAt: {
    type: Number,
    default: Date.now,
  },
  updatedAt: {
    type: Number,
    default: Date.now,
  },
});

ServiceChildren.statics.list = function (service, nativeVersion, platform, versionCodePush, cb) {
  const version = Number(nativeVersion);
  const codePush = Number(versionCodePush.replace('v', ''));
  const query = {
    open: 1,
    service,
    // appName: {
    //   '$exists': false
    // },
    // $or: [
    //   {
    //     'region.allow': region
    //   },
    //   {
    //     'region.allow': 'all',
    //     'region.deny': {
    //       $ne: region
    //     }
    //   }
    // ]
  };

  if (version) {
    const platformQuery = `platform.${platform}`;

    if (codePush) {
      query[`${platformQuery}.codePushFrom`] = { $lte: codePush };
      query[`${platformQuery}.codePushTo`] = { $gte: codePush };
      query[`${platformQuery}.codePushDeny`] = { $ne: codePush };
    } else {
      query[`${platformQuery}.from`] = { $lte: version };
      query[`${platformQuery}.to`] = { $gte: version };
      query[`${platformQuery}.deny`] = { $ne: version };
    }
  }

  let func = this.find(query, '-createdAt -updatedAt -region -platform -showListShipper').lean();

  // if (environment === 'production') {
  //   func = func.cache(ms('15m') / 1000, `service:${region}:${platform}:${version}`)
  // }

  func.sort('order').exec(cb);
};

module.exports = mongoConnections('master').model('ServiceChildren', ServiceChildren);
