const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo')

var CategoryNotification = new mongoose.Schema({
  name: { type: String },
  nameAlias: { type: String },
  service: { type: mongoose.Schema.Types.ObjectId, ref: 'Service' },
  status: { type: Number, default: 1 },
  order: {type: Number},
  updatedAt: { type: Number, default: Date.now },
  createdAt: { type: Number, default: Date.now }
}, { versionKey: false });

module.exports = mongoConnections('master').model('CategoryNotification', CategoryNotification);
