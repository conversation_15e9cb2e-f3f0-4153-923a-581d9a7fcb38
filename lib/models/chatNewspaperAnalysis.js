const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatNewspaperAnalysisSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    link: {
      type: String,
    },
    answerJson: {
      type: mongoose.Schema.Types.Mixed
    },
    numOfWord: {
      type: Number,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chatNewspaperAnalysis", ChatNewspaperAnalysisSchema);
