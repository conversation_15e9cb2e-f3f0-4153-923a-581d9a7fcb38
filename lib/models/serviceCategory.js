const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")
const async = require('async');
const ms = require('ms')
// import { environment } from '../config';

const ServiceCategory = new mongoose.Schema({
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  active: {
    type: Number,
    default: 1
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  extras: {
    type: mongoose.Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
});

ServiceCategory.statics.list = function (region, cb) {
  const query = {
    active: 1,
    // appName: {
    //   '$exists': false
    // },
    // $or: [
    //   {
    //     'region.allow': region
    //   },
    //   {
    //     'region.allow': 'all',
    //     'region.deny': {
    //       $ne: region
    //     }
    //   }
    // ]
  };

  let func = this.find(query, "-createdAt -updatedAt -region -active -order").lean()

  // if (environment === 'production') {
  //   func = func.cache(ms('15m') / 1000, `service:${region}:${platform}:${version}`)
  // }

  func
    .sort('-order')
    .exec(cb)
}

module.exports = mongoConnections('master').model('ServiceCategory', ServiceCategory);
