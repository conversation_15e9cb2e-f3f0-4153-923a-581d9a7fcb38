const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ConversationSchema = new mongoose.Schema(
  {
    member: {
      type: String,
    },
    id: {
      type: Number,
    },
    is_dev: {type: Boolean},
    agents_impl_id: {
      type: Number
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    inactive: {
      type: Boolean,
    },
    hasMessage: {
      type: Boolean,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

ConversationSchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationSchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationSchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('Conversation', ConversationSchema);
