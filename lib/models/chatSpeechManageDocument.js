const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatSpeechManageDocumentSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    speaker: {
      type: String,
    },
    listener: {
      type: String,
    },
    content: {
      type: String,
    },
    numberWord: {
      type: Number,
    },
    page: {
      type: Number,
    },
    urlDownload: {
      type: String
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chatSpeechManageDocument", ChatSpeechManageDocumentSchema);
