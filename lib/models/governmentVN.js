const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const GovernmentVN = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'Government'
  },
  organization: {
    type: String
  },
  position: {
    type: String
  },
  positionAlias: {
    type: String
  },
  biography: {
    type: String
  },
  mission: {
    type: String
  },
  education: {
    type: String
  },
  workProcess: {
    type: String
  },
  reward: {
    type: String
  },
  order: {
    type: Number
  },
  active: {
    type: Number,
    default: 1
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('GovernmentVN', GovernmentVN);