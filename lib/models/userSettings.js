const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const UserSchema = new mongoose.Schema(
  {
    qlvb: {
      username: {
        type: String,
      },
      password: {
        type: String,
      },
      updatedAt: {
        type: Number,
        default: Date.now,
      },
      user_id: {
        type: String,
      },
      syncTime: {
        type: Number
      }
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    status: {
      type: Number,
      default: 1,
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('UserSettings', UserSchema);
