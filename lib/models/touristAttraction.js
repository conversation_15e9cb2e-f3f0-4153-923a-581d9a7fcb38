const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const TouristAttraction = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  images: [{
    type: String
  }],
  generalInfo: {
    type: String
  },
  address: {
    type: String
  },
  district: {
    type: Schema.Types.ObjectId,
    ref: 'District'
  },
  ward: {
    type: Schema.Types.ObjectId,
    ref: 'Ward'
  },
  wardCode: {
    type: String
  },
  districtCode: {
    type: String
  },
  star: {
    type: Number,
    default: 5
  },
  active: {
    type: Number,
    default: 1
  },
  famous: {
    type: Number,
    default: 0
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('TouristAttraction', TouristAttraction);