const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")

var Banner = new mongoose.Schema({
  config: {
    type: mongoose.Schema.Types.Mixed
  },
  description: {
    type: String
  },
  status: {
    type: Number
  },
  platform: {
    type: mongoose.Schema.Types.Mixed
  },
  position: {
    type: Number
  },
  isAddable: {
    type: Number
  },
  isHideable: {
    type: Number
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { versionKey: false })


//module.exports = mongoose.model('banner', Banner);
module.exports = mongoConnections('master').model('banner', Banner);
