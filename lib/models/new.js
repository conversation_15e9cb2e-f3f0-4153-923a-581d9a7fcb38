const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")

const New = new mongoose.Schema({
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NewCategory'
  },
  title: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  active: {
    type: Number,
    default: 1
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
});


New.statics.list = function (category, region, from, cb) {
  this
    .find({
      category,
      active: 1,
      $or: [
        {
          'region.allow': region
        },
        {
          'region.allow': 'all',
          'region.deny': {
            $ne: region
          }
        }
      ]
    }, "-createdAt -active -region -data")
    .sort({ createdAt: -1 })
    .limit(3)
    .skip(from)
    .lean()
    .exec(cb)
}

// module.exports = mongoose.model('New', New);
module.exports = mongoConnections('master').model('New', New);
