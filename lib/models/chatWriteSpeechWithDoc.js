const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatWriteSpeechWithDocSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    document: {
      type: Schema.Types.ObjectId,
      ref: 'Document'
    },
    speaker: {
      type: String,
    },
    listener: {
      type: String,
    },
    numberWord: {
      type: Number,
    },
    page: {
      type: Number,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("ChatWriteSpeechWithDoc", ChatWriteSpeechWithDocSchema);
