const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const DichvucongEventSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    nameAlias: {
      type: String,
    },
    icon: {
      type: String,
    },
    active: {
      type: Number,
      default: 1
    },
    event: {
      type: Number
    },
    group: {
      type: Schema.Types.ObjectId,
      ref: 'Dichvucong'
    },
    order: {
      type: Number
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("DichvucongEvent", DichvucongEventSchema)
