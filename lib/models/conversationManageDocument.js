const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const conversationManageDocumentSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    id: {
      type: Number,
    },
    isFile: {
      type: Boolean
    },
    is_dev: {type: Boolean},
    agents_impl_id: {
      type: Number
    },
    documents: {
      type: Array
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    inactive: {
      type: Boolean,
    },
    hasMessage: {
      type: Boolean,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

conversationManageDocumentSchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

conversationManageDocumentSchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

conversationManageDocumentSchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('conversationManageDocument', conversationManageDocumentSchema);
