const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatSocioEconomicSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    field: {
      type: String,
    },
    topic: {
      type: String,
    },
    timeline: {
      type: String,
    },
    specificTimeline: {
      type: Number,
    },
    year: {
      type: Number,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chatSocioEconomic", ChatSocioEconomicSchema);
