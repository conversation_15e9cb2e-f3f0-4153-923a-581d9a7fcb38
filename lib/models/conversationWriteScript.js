const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ConversationWriteScriptSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    id: {
      type: Number,
    },
    is_dev: {type: <PERSON>olean},
    agents_impl_id: {
      type: Number,
    },
    isFile: {
      type: Boolean
    },
    documents: [{
      type: Schema.Types.ObjectId,
      ref: 'Document',
    }],
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    inactive: {
      type: Boolean
    },
    hasMessage: {
      type: Boolean,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

ConversationWriteScriptSchema.virtual('memberInfo', {
  ref: 'Member',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationWriteScriptSchema.virtual('userInfo', {
  ref: 'User',
  localField: 'member',
  foreignField: '_id',
  justOne: true,
});

ConversationWriteScriptSchema.virtual('deviceInfo', {
  ref: 'TrackingAction',
  localField: 'member',
  foreignField: 'otherInf.uniqueId',
  justOne: true,
});

module.exports = mongoConnections('master').model('ConversationWriteScript', ConversationWriteScriptSchema);
