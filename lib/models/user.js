const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const UserSchema = new mongoose.Schema(
 {
  username: {
   type: String,
  },
  password: {
   type: String,
  },
  email: {
    type: String
  },
  name: {
   type: String,
  },
  phone: {
   type: String
  },
  avatar: {
    type: String
  },
  dob: {
    type: String,
  },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
  status: {
    type: Number,
    default: 1,
  },
  gender: {
    type: String
  },
  active: {
   type: Number,
   default: 0,
  },
  apps: {
    type: [String],
    default: 'cms'
  },
  units: [{
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  }],
  positions: [{
    type: Schema.Types.ObjectId,
    ref: 'Position'
  }],
  permissions: [{
    type: Schema.Types.ObjectId,
    ref: 'Permission'
  }],
  groupPermissions: [{
    type: Schema.Types.ObjectId,
    ref: 'GroupPermission'
  }]
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("User", UserSchema)
