const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var NotificationsSchema = new mongoose.Schema({
    platform: String,
    notify_token: String,
    member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
    deviceId: { type: String },
    newVersion: { type: Number },
    isSafari: { type: Number },
    createdAt: {
        type: Number,
        default: Date.now
    },
    updatedAt: {
        type: Number,
        default: Date.now
    }
}, { id: false, versionKey: 'v' });


module.exports = mongoConnections('master').model('Notification', NotificationsSchema);
