const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ChatSchema = new mongoose.Schema(
  {
    status: {
      type: Number,
      default: 1,
    },
    title: {
      type: String,
    },
    questions: [
      {
        question: {
          type: Schema.Types.ObjectId,
          ref: 'ChatTesing',
        },
        conversation: {
          type: Schema.Types.ObjectId,
          ref: 'Conversation',
        },
        isTesting: {
          type: Boolean,
          default: false,
        },
        isValid: {
          type: Schema.Types.Mixed,
          default: undefined,
        },
        testedAt: {
          type: Number,
          default: Date.now,
        },
        explain: {
          type: String,
        },
        urlChatbot: {
          type: String,
        },
      },
    ],
    type: {
      type: String,
    },
    mode: {
      type: String, //check-answer(ngữ nghĩa), normal(check có dữ liệu hay không)
    },
    model: {
      type: String, //public, staff, leader, pre_production
    },
    is_testing: {
      type: <PERSON>olean,
      default: true,
    },
    disable_search_web: {
      type: <PERSON><PERSON><PERSON>,
      default: true,
    },
    isTesting: {
      type: Boolean,
      default: false,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
    testedAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('ChatTesingGroup', ChatSchema);
