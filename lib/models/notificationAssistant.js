const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var NotificationAssistantSchema = new mongoose.Schema({
    platform: String,
    notify_token: String,
    member: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    isSafari: { type: Number, default: 0 },
    createdAt: {
        type: Number,
        default: Date.now
    },
    updatedAt: {
        type: Number
    }
}, { id: false, versionKey: 'v' });

NotificationAssistantSchema.index({ member: 1 });
NotificationAssistantSchema.index({ platform: 1 });

module.exports = mongoConnections('master').model('NotificationAssistant', NotificationAssistantSchema);
