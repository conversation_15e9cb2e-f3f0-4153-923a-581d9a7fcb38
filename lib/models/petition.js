const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const moment = require('moment');

const PetitionSchema = new mongoose.Schema(
 {
  code: {
    type: String,
  },
  title: {
   type: String,
  },
  content: {
    type: String,
  },
  address: {
    type: String
  },
  district: {
    type: Schema.Types.ObjectId,
    ref: 'District'
  },
  ward: {
    type: Schema.Types.ObjectId,
    ref: 'Ward'
  },
  location: {
    type: Schema.Types.Mixed
  },
  locationUser: {
    type: Schema.Types.Mixed
  },
  addressUser: {
    type: String
  },
  unit: {
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  },
  unitPath: [{
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  }],
  status: {
    type: Number,
    default: 0
  },
  statusJob: {
    type: Number,
    default: 0
  },
  attachments: {
    type: Schema.Types.Mixed
  },
  replyContent: {
    type: String
  },
  replyAttachments: {
    type: Schema.Types.Mixed
  },
  replyDocuments: {
    type: Schema.Types.Mixed
  },
  rate: {
    type: Number
  },
  isPublicName: {
    type: Boolean
  },
  isPublic: {
    type: Boolean
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  replyAt: {
    type: Number
  },
  sequence:{
    type: Number
  },
  expiredTime:{
    type: Number
  },
  expiredTimeJob:{
    type: Number
  },
  warningTimeJob:{
    type: Number
  },
  isExpired:{
    type: Boolean,
    default: false
  },
  processingUnit:{
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  },
  processingUser:{
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  priority: {
    type: Number,
    default: 0
  },
  isDuplicate: {
    type: Boolean,
  },
  originalPetition: {
    type: Schema.Types.ObjectId,
    ref: 'Petition'
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  users: {
    type: Schema.Types.Mixed,
    ref: 'User'
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'Category'
  },
  categoryReason: {
    type: String
  },
  guideContent: {
    type: String
  },
  maybeSpam: {
    type: Boolean
  },
  cannotReject: {
    type: Boolean
  },
  processingContent: {
    type: String
  }
 },
 { id: false, versionKey: false },
)

PetitionSchema.pre('save', function (next) {
  const doc = this;
  attempToGenerate(doc, next);
});

const attempToGenerate = (doc, next) => {
  if (!doc.isNew) return next(); 
  try {
    const today = new Date(); 
    today.setHours(0, 0, 0, 0);
    doc.constructor
      .findOne({ createdAt: {$gte: today.getTime()}})
      .sort({ createdAt: -1 })
      .lean()
      .exec((err, latestPetition) => {
        if(err) {
          attempToGenerate(doc, next)
        }
        const nextSequence = latestPetition ? latestPetition.sequence + 1 : 1;

        const prefix = 'PA'; 
        const datePart = moment().format('DDMMYYYY');
        const paddedValue = String(nextSequence).padStart(4, '0');
    
        doc.code = `${prefix}-${datePart}-${paddedValue}`;
        doc.sequence = nextSequence; 
        next();
      })
   
  } catch (err) {
    console.error(err); 
    next(err);
  }
}

module.exports = mongoConnections("master").model("Petition", PetitionSchema)
