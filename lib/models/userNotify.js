const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const UserNotifySchema = new mongoose.Schema(
 {
  title: {
    type: String,
  },
  message: {
   type: String,
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  seen: {
    type: Number,
    default: 0
  },
  createdAt: { type: Number, default: Date.now },
  data: {
    type: Schema.Types.Mixed
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("UserNotify", UserNotifySchema)
