const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")

var ReasonBlock = new mongoose.Schema({
  member: { type: mongoose.Schema.Types.ObjectId },
  message: { type: String },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { versionKey: false });


// module.exports = mongoose.model('ReasonBlock', ReasonBlock);
module.exports = mongoConnections('master').model('ReasonBlock', ReasonBlock);
