const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatDocumentSummarySchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    content: {
      type: String,
    },
    page: {
      type: Number,
    },
    isVideo: {
      type: Number,
    },
    word: {
      type: Number,
    },
    urlDownload: {
      type: String,
    },
    document: {
      type: Schema.Types.ObjectId,
      ref: 'Document'
    },
    queueTaskId: {
      type: String,
    },
    status: {
      type: String,
      default: 'queued'
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("chatDocumentSummary", ChatDocumentSummarySchema);
