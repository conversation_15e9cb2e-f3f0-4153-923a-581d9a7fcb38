const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatMakeDecisionSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    position: {
      type: String,
    },
    type: {
      type: String,
    },
    field: {
      type: String,
    },
    topic: {
      type: String,
    },
    timeline: {
      type: String,
    },
    specificTimeline: {
      type: Number,
    },
    year: {
      type: Number,
    },
    compare: {
      type: Boolean,
    },
    specificTimelineEnd: {
      type: Number,
    },
    yearEnd: {
      type: Number,
    },
    exportPdf: {
      type: Boolean,
    },
    urlDownload: {
      type: String
    },
    createCharts: {
      type: Boolean,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("ChatMakeDecision", ChatMakeDecisionSchema);
