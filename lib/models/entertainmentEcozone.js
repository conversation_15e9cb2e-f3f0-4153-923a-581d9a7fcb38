const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const EntertainmentEcozone = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  image: {
    type: String
  },
  phones: {
    type: Array
  },
  generalInfo: {
    type: String
  },
  address: {
    type: String
  },
  district: {
    type: String
  },
  districtCode: {
    type: String
  },
  ward: {
    type: String
  },
  region: {
    type: String
  },
  wardCode: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('EntertainmentEcozone', EntertainmentEcozone);