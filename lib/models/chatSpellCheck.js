const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema
const ChatSpellCheckSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    content: {
      type: String,
    },
    userFix: {
      type: Array
    },
    origin_text: {
      type: String,
    },
    urlDownload: {
      type: String,
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("ChatSpellCheck", ChatSpellCheckSchema);
