const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Version = new mongoose.Schema({
  platform: {
    type: String
  },
  version: {
    type: Number
  },
  change: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('Version', Version);