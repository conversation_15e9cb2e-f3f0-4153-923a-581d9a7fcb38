const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;
const ChatWriteReportManageDocumentSchema = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
    },
    conversation: {
      type: Schema.Types.ObjectId,
    },
    question: {
      type: String,
    },
    answer: {
      type: String,
    },
    error: {
      type: String,
    },
    title: {
      type: String,
    },
    content: {
      type: String,
    },
    page: {
      type: Number,
    },
    role: {
      type: String,
    },
    name: {
      type: String,
    },
    position: {
      type: String,
    },
    urlDownload: {
      type: String,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('chatWriteReportManageDocument', ChatWriteReportManageDocumentSchema);
