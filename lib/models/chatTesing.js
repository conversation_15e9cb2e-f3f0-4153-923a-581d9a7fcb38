const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const ChatSchema = new mongoose.Schema(
  {
    question: {
      type: String,
    },
    expectedAnswer: {
      type: String,
    },
    expectedCheckAnswer: {
      type: String,
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: 'ChatTesingCategory'
    },
    region: {
      type: String,
    },
    status: {
      type: Number,
      default: 1,
    },
    updatedAt: {
      type: Number,
      default: Date.now,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: false }
);

module.exports = mongoConnections('master').model('ChatTesing', ChatSchema);
