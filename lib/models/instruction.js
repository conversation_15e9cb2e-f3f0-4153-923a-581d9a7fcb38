const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")

var Instruction = new mongoose.Schema({
  title: {
    type: String
  },
  titleAlias: {
    type: String
  },
  description: {
    type: String
  },
  image: {
    type: String
  },
  video: {
    type: String
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ServiceCategory'
  },
  content: {
    type: String
  },
  url: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  order: {
    type: Number
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { versionKey: false })


//module.exports = mongoose.model('banner', Banner);
module.exports = mongoConnections('master').model('instruction', Instruction);
