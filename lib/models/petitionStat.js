const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const PetitionStatSchema = new mongoose.Schema(
  {
    total: { type: Number },
    choTiepNhan: { type: Number },
    daTiepNhan: { type: Number },
    dangXuLy: { type: Number },
    daXuLy: { type: Number },
    daTuChoi: { type: Number },
    createdAt: { type: Number, default: Date.now },
    updatedAt: { type: Number, default: Date.now }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("PetitionStat", PetitionStatSchema)
