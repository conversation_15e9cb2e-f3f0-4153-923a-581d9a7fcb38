const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const DichvucongSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    nameAlias: {
      type: String,
    },
    icon: {
      type: String,
    },
    active: {
      type: Number,
      default: 1
    },
    group: {
      type: Number
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Dichvucong", DichvucongSchema)
