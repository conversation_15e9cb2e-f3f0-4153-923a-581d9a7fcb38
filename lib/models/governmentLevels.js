const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const GovernmentLevels = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  level: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('GovernmentLevels', GovernmentLevels);