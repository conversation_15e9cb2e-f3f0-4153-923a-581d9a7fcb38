const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const District = new mongoose.Schema({
  province: {
    type: Schema.Types.ObjectId,
    ref: "Citie"
  },
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  code: {
    type: String
  },
  location: {
    type: Schema.Types.Mixed,
  },
  provider: {
    type: Schema.Types.Mixed,
    select: false
  },
  provider: {
    type: Schema.Types.Mixed,
    select: false
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  isAuthen: {
    type: Number,
    default: 0
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('District', District);