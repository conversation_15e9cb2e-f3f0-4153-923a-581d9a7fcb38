const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Education = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  code: {
    type: String
  },
  image: {
    type: String
  },
  department: {
    type: String
  },
  principal: {
    type: String
  },
  type: {
    type: String
  },
  scale: {
    type: String
  },
  level: {
    type: String
  },
  standardLevel:{
    type: String
  },
  address: {
    type: String
  },
  district: {
    type: String
  },
  districtCode: {
    type: String
  },
  ward: {
    type: String
  },
  region: {
    type: String
  },
  wardCode: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  phones: {
    type: Schema.Types.Mixed,
  },
  generalInfo: {
    type: String
  },
  location: {
    type: Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('Education', Education);