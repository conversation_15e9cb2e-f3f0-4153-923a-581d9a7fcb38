const _ = require('lodash');
global.Logger = require('./lib/logger');
global.logger = Logger(`${__dirname}/logs`);
global.mongoose = require('mongoose');
const config = require('config');
const Unit = require('./lib/models/unit');
const tool = require('./lib/util/tool');

// Colors for console output
const COLORS = {
  SUCCESS: '\x1b[32m',
  ERROR: '\x1b[31m',
  WARNING: '\x1b[33m',
  INFO: '\x1b[36m',
  SEPARATOR: '\x1b[35m',
  RESET: '\x1b[0m'
};

console.log(`${COLORS.INFO}[INFO] Starting Unit nameAlias update script...${COLORS.RESET}`);

// Function to update nameAlias for all units
async function updateUnitNameAlias() {
  try {
    console.log(`${COLORS.INFO}[INFO] Fetching all units from database...${COLORS.RESET}`);

    // Find all units that have a name field
    const units = await Unit.find({ name: { $exists: true, $ne: null, $ne: '' } }).lean();

    if (!units || units.length === 0) {
      console.log(`${COLORS.WARNING}[WARNING] No units found with name field.${COLORS.RESET}`);
      return;
    }

    console.log(`${COLORS.INFO}[INFO] Found ${units.length} units to process.${COLORS.RESET}`);
    console.log(`${COLORS.SEPARATOR}--------------------------------------------------${COLORS.RESET}`);

    let successCount = 0;
    let errorCount = 0;
    let skippedCount = 0;

    // Process each unit
    for (let i = 0; i < units.length; i++) {
      const unit = units[i];

      try {
        console.log(`${COLORS.INFO}[INFO] Processing unit ${i + 1}/${units.length}: ${unit.name} (ID: ${unit._id})${COLORS.RESET}`);

        // Generate nameAlias using the change_alias function
        const nameAlias = tool.change_alias(unit.name);

        console.log(`${COLORS.INFO}[INFO] Generated nameAlias: "${nameAlias}" from name: "${unit.name}"${COLORS.RESET}`);

        // Debug: Show current nameAlias value
        console.log(`${COLORS.INFO}[DEBUG] Current nameAlias: "${unit.nameAlias}" (type: ${typeof unit.nameAlias})${COLORS.RESET}`);
        console.log(`${COLORS.INFO}[DEBUG] New nameAlias: "${nameAlias}" (type: ${typeof nameAlias})${COLORS.RESET}`);

        // Check if nameAlias already exists and is the same
        if (unit.nameAlias === nameAlias) {
          console.log(`${COLORS.WARNING}[WARNING] Unit already has the correct nameAlias. Skipping...${COLORS.RESET}`);
          skippedCount++;
        } else {
          // Update the unit with the new nameAlias
          const result = await Unit.updateOne(
            { _id: unit._id },
            { $set: { nameAlias: nameAlias } }
          );

          console.log(`${COLORS.INFO}[DEBUG] Update result: matchedCount=${result.matchedCount}, modifiedCount=${result.modifiedCount}${COLORS.RESET}`);

          if (result.modifiedCount > 0) {
            console.log(`${COLORS.SUCCESS}[SUCCESS] Updated unit "${unit.name}" with nameAlias: "${nameAlias}"${COLORS.RESET}`);
            successCount++;
          } else {
            console.log(`${COLORS.WARNING}[WARNING] No changes made to unit "${unit.name}". Possible reasons: nameAlias already exists or update failed${COLORS.RESET}`);
            skippedCount++;
          }
        }

      } catch (error) {
        console.log(`${COLORS.ERROR}[ERROR] Failed to update unit "${unit.name}" (ID: ${unit._id}): ${error.message}${COLORS.RESET}`);
        errorCount++;
      }

      console.log(`${COLORS.SEPARATOR}--------------------------------------------------${COLORS.RESET}`);
    }

    // Summary
    console.log(`${COLORS.INFO}[SUMMARY] Update completed!${COLORS.RESET}`);
    console.log(`${COLORS.SUCCESS}[SUMMARY] Successfully updated: ${successCount} units${COLORS.RESET}`);
    console.log(`${COLORS.WARNING}[SUMMARY] Skipped: ${skippedCount} units${COLORS.RESET}`);
    console.log(`${COLORS.ERROR}[SUMMARY] Errors: ${errorCount} units${COLORS.RESET}`);
    console.log(`${COLORS.INFO}[SUMMARY] Total processed: ${units.length} units${COLORS.RESET}`);

  } catch (error) {
    console.log(`${COLORS.ERROR}[ERROR] Script failed: ${error.message}${COLORS.RESET}`);
    console.error(error);
  }
}

// Function to update only units without nameAlias
async function updateUnitsWithoutNameAlias() {
  try {
    console.log(`${COLORS.INFO}[INFO] Fetching units without nameAlias...${COLORS.RESET}`);

    // Find units that have name but no nameAlias
    const units = await Unit.find({
      name: { $exists: true, $ne: null, $ne: '' },
      $or: [
        { nameAlias: { $exists: false } },
        { nameAlias: null },
        { nameAlias: '' }
      ]
    }).lean();

    if (!units || units.length === 0) {
      console.log(`${COLORS.WARNING}[WARNING] No units found without nameAlias.${COLORS.RESET}`);
      return;
    }

    console.log(`${COLORS.INFO}[INFO] Found ${units.length} units without nameAlias to process.${COLORS.RESET}`);
    console.log(`${COLORS.SEPARATOR}--------------------------------------------------${COLORS.RESET}`);

    let successCount = 0;
    let errorCount = 0;

    // Process each unit
    for (let i = 0; i < units.length; i++) {
      const unit = units[i];

      try {
        console.log(`${COLORS.INFO}[INFO] Processing unit ${i + 1}/${units.length}: ${unit.name} (ID: ${unit._id})${COLORS.RESET}`);

        // Generate nameAlias using the change_alias function
        const nameAlias = tool.change_alias(unit.name);

        console.log(`${COLORS.INFO}[INFO] Generated nameAlias: "${nameAlias}" from name: "${unit.name}"${COLORS.RESET}`);

        // Update the unit with the new nameAlias
        const result = await Unit.updateOne(
          { _id: unit._id },
          { $set: { nameAlias: nameAlias } }
        );

        if (result.modifiedCount > 0) {
          console.log(`${COLORS.SUCCESS}[SUCCESS] Updated unit "${unit.name}" with nameAlias: "${nameAlias}"${COLORS.RESET}`);
          successCount++;
        } else {
          console.log(`${COLORS.WARNING}[WARNING] No changes made to unit "${unit.name}"${COLORS.RESET}`);
        }

      } catch (error) {
        console.log(`${COLORS.ERROR}[ERROR] Failed to update unit "${unit.name}" (ID: ${unit._id}): ${error.message}${COLORS.RESET}`);
        errorCount++;
      }

      console.log(`${COLORS.SEPARATOR}--------------------------------------------------${COLORS.RESET}`);
    }

    // Summary
    console.log(`${COLORS.INFO}[SUMMARY] Update completed!${COLORS.RESET}`);
    console.log(`${COLORS.SUCCESS}[SUMMARY] Successfully updated: ${successCount} units${COLORS.RESET}`);
    console.log(`${COLORS.ERROR}[SUMMARY] Errors: ${errorCount} units${COLORS.RESET}`);
    console.log(`${COLORS.INFO}[SUMMARY] Total processed: ${units.length} units${COLORS.RESET}`);

  } catch (error) {
    console.log(`${COLORS.ERROR}[ERROR] Script failed: ${error.message}${COLORS.RESET}`);
    console.error(error);
  }
}

// Main execution
async function main() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    const mode = args[0] || 'missing'; // Default to 'missing' mode

    console.log(`${COLORS.INFO}[INFO] Script mode: ${mode}${COLORS.RESET}`);
    console.log(`${COLORS.INFO}[INFO] Available modes:${COLORS.RESET}`);
    console.log(`${COLORS.INFO}  - missing: Update only units without nameAlias (default)${COLORS.RESET}`);
    console.log(`${COLORS.INFO}  - all: Update all units regardless of existing nameAlias${COLORS.RESET}`);
    console.log(`${COLORS.SEPARATOR}--------------------------------------------------${COLORS.RESET}`);

    if (mode === 'all') {
      await updateUnitNameAlias();
    } else if (mode === 'missing') {
      await updateUnitsWithoutNameAlias();
    } else {
      console.log(`${COLORS.ERROR}[ERROR] Invalid mode: ${mode}. Use 'all' or 'missing'.${COLORS.RESET}`);
      process.exit(1);
    }

    console.log(`${COLORS.SUCCESS}[SUCCESS] Script completed successfully!${COLORS.RESET}`);
    process.exit(0);

  } catch (error) {
    console.log(`${COLORS.ERROR}[ERROR] Script execution failed: ${error.message}${COLORS.RESET}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the script
main();

/*
HƯỚNG DẪN SỬ DỤNG:

1. Chạy script với chế độ mặc định (chỉ cập nhật unit chưa có nameAlias):
   node updateUnitNameAlias.js

2. Chạy script với chế độ cập nhật tất cả:
   node updateUnitNameAlias.js all

3. Chạy script với chế độ chỉ cập nhật unit thiếu nameAlias:
   node updateUnitNameAlias.js missing

VÍ DỤ KẾT QUẢ:
- Input name: "Phòng Tài nguyên và Môi trường"
- Output nameAlias: "phongtainguyenvamoimoitruong"

- Input name: "UBND Quận Hồng Bàng"
- Output nameAlias: "ubndquanhongbang"
*/
